@page "/settings"
@attribute [Authorize]

@using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent

@inherits UserBaseComponent

@inject HttpClient HttpClient
@inject HttpClientAnon HttpClientAnon
@inject NavigationManager NavManager

@* <div class="main-header">
    <div class="outer-wrapper">
        <div class="twenty-dishes-logo">
            @if (!string.IsNullOrWhiteSpace(logoBrand.LogoCloudflareId))
            {
                <img src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, logoBrand.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logoBrand.PageTitle)" />
            }
        </div>

        <div class="login-display">
            <LoginDisplay />
        </div>
    </div>
</div> *@
<Header/>
<SideMenu ActivePage="settings" />
 
@* <div class="cover-block">
</div>
<div class="radius-set"></div> *@

<div class="settings-heading">
    <h3>Settings</h3>
</div>

<div class="settings-main">
    <Banner @bind-MessageType="@messageType" @bind-Message="@message" />
    @if (!isLoading)
    {
        @* <div class="settings-header">
            <h1>Member Profile</h1>
            <p>
                Please note that if you are in your 7-day free trial, you may cancel at any time, otherwise, your cancelation request must be made 2 business days before you are to rebill as agreed to on our Terms of Service.
                <span class="d-block mt-2">
                    If you are a PRO user, you can learn how to manage your sub-accounts <span style="color:red; cursor:pointer">here</span>.
                    To manage your account, please go to the subscription tab below.
                </span>
            </p>
        </div> *@

        @* <div class="member-profile-sm">
            <div class="profle-img">
                @if (!string.IsNullOrWhiteSpace(userImageId))
                {
                    <img src="@($"{HttpClient.BaseAddress}/api/GetUserProfileImage/{userImageId}")" class="avatar-img-sm" />
                }
                else
                {
                    <div class="avatar-img-sm"></div>
                }
            </div>
            <label class="profile-controls" for="filePicker">
                <span>Upload New Picture</span>
                <InputFile id="filePicker" OnChange="@UploadUserImage" multiple accept=".png, .jpg, .jpeg, .gif" hidden />
            </label>
            <div class="profile-btn">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7.04482 5.61316L11.9385 5.54508L11.9579 6.94299L7.06426 7.01106L7.13234 11.9047L5.73443 11.9242L5.66636 7.03051L0.772705 7.09858L0.753258 5.70068L5.64691 5.6326L5.57884 0.738949L6.97674 0.719503L7.04482 5.61316Z" fill="#CA2F35" />
                </svg>
            </div>
        </div> *@
        <div class="div-sep-sm">
            <div class="bar-sep"></div>
        </div>
        <div class="setting-selection"> 
            <div class="setting-item-container">
                <button class="setting-item @(selectedSetting == MemberSetting.General ? "setting-item-focused" : string.Empty)"
                @onclick="(() => SetSelectedSetting(MemberSetting.General))">
                    General
                </button>
            </div>
            <div class="setting-item-container">
                <button class="setting-item @(selectedSetting == MemberSetting.SubscriptionInfo ? "setting-item-focused" : string.Empty)"
                @onclick="(() => SetSelectedSetting(MemberSetting.SubscriptionInfo))">
                    Subscription Info
                </button>
            </div>
            @* <div class="setting-item-container">
                <button class="setting-item @(selectedSetting == MemberSetting.Notifications ? "setting-item-focused" : string.Empty)"
                @onclick="(() => SetSelectedSetting(MemberSetting.Notifications))">
                    Notifications
                </button>
            </div>
            <div class="setting-item-container">
                <button class="setting-item @(selectedSetting == MemberSetting.Language ? "setting-item-focused" : string.Empty)"
                @onclick="(() => SetSelectedSetting(MemberSetting.Language))">
                    Language
                </button>
            </div>  *@
            @* <div class="setting-item-container">
                <button class="setting-item @(selectedSetting == MemberSetting.Preferences ? "setting-item-focused" : string.Empty)"
                @onclick="(() => SetSelectedSetting(MemberSetting.Preferences))">
                    Preferences
                </button>
            </div>*@
        </div>
        <div class="settings-content">
            @* <div class="member-profile-info"> *@
               @*  <div class="avatar">
                    @if (!string.IsNullOrWhiteSpace(userImageId))
                    {
                        <img src="@($"{HttpClient.BaseAddress}/api/GetUserProfileImage/{userImageId}")" class="avatar-img" />
                    }
                    else
                    {
                        <div class="avatar-img"></div>
                    }
                </div>
                <label class="avatar-controls" for="filePicker">
                    <span>Upload New Picture</span>
                    <InputFile id="filePicker" OnChange="@UploadUserImage" multiple accept=".png, .jpg, .jpeg, .gif" hidden />
                </label>
                <div class="bar-sep"></div> *@
               @*  <div class="groups">
                    <div class="group-text">Groups <span class="group-pill">2</span></div>
                    <div class="group-pos">
                        <div class="group-img"></div>
                        <div class="group-info">
                            <span style="font-family: 'Raleway';font-style: normal;font-weight: 600;">Group-Name</span>
                            <span style="font-family: 'Raleway';font-style: normal;font-weight: 400;">Members</span>
                            <span style="font-family: 'Raleway';font-style: normal;font-weight: 600; color:#CA2F35;">
                                Leave Group
                                <svg width="17" height="10" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M13.9075 3.77569L10.7257 0.593919C10.6114 0.460457 10.4106 0.444898 10.2771 0.559221C10.1436 0.673515 10.1281 0.874379 10.2424 1.00784C10.2531 1.02028 10.2647 1.0319 10.2771 1.04254L12.9148 3.68341H0.318166C0.142458 3.68341 0 3.82587 0 4.0016C0 4.17734 0.142458 4.31977 0.318166 4.31977H12.9148L10.2771 6.95744C10.1436 7.07173 10.1281 7.2726 10.2424 7.40606C10.3567 7.53952 10.5576 7.55508 10.691 7.44076C10.7035 7.43009 10.7151 7.4185 10.7257 7.40606L13.9075 4.22429C14.0308 4.10023 14.0308 3.8998 13.9075 3.77569Z" fill="#CA2F35" />
                                </svg>
                            </span>
                        </div>

                        <div class="group-img"></div>
                        <div class="group-info">
                            <span style="font-family: 'Raleway';font-style: normal;font-weight: 600;">Group-Name</span>
                            <span style="font-family: 'Raleway';font-style: normal;font-weight: 400;">Members</span>
                            <span style="font-family: 'Raleway';font-style: normal;font-weight: 600; color:#CA2F35;">
                                Leave Group
                                <svg width="17" height="10" viewBox="0 0 14 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M13.9075 3.77569L10.7257 0.593919C10.6114 0.460457 10.4106 0.444898 10.2771 0.559221C10.1436 0.673515 10.1281 0.874379 10.2424 1.00784C10.2531 1.02028 10.2647 1.0319 10.2771 1.04254L12.9148 3.68341H0.318166C0.142458 3.68341 0 3.82587 0 4.0016C0 4.17734 0.142458 4.31977 0.318166 4.31977H12.9148L10.2771 6.95744C10.1436 7.07173 10.1281 7.2726 10.2424 7.40606C10.3567 7.53952 10.5576 7.55508 10.691 7.44076C10.7035 7.43009 10.7151 7.4185 10.7257 7.40606L13.9075 4.22429C14.0308 4.10023 14.0308 3.8998 13.9075 3.77569Z" fill="#CA2F35" />
                                </svg>
                            </span>
                        </div>

                    </div>
                </div>
                <div class="bar-sep"></div>
                <div class="social">
                    <div style="font-family: 'Raleway';font-style: normal;font-weight: 600; padding: 15px 0 0 15px;">Social Media</div>
                    <div class="d-flex flex-row gap-3 social-pos">
                        <div class="social-dot">
                            <svg style="margin: 5px 0 0 11px;" width="15" height="20" viewBox="0 0 10 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6.23911 16V8.70218H8.6877L9.05506 5.85725H6.23911V4.04118C6.23911 3.21776 6.46683 2.65661 7.64894 2.65661L9.15417 2.65599V0.111384C8.89386 0.0775563 8.00032 0 6.96032 0C4.78864 0 3.30187 1.32557 3.30187 3.75942V5.85725H0.845856V8.70218H3.30187V16H6.23911Z" fill="white" />
                            </svg>
                        </div>
                        <div class="social-dot">
                            <svg style="margin: 7px 0 0 9px" ; width="20" height="20" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11.6138 0H4.38616C1.96762 0 0 1.96763 0 4.38619V11.6138C0 14.0324 1.96762 16 4.38616 16H11.6138C14.0324 16 16 14.0324 16 11.6138V4.38619C16 1.96763 14.0324 0 11.6138 0ZM8 12.3885C5.58016 12.3885 3.61153 10.4198 3.61153 8C3.61153 5.58016 5.58019 3.61156 8 3.61156C10.4198 3.61156 12.3885 5.58019 12.3885 8C12.3885 10.4198 10.4198 12.3885 8 12.3885ZM12.651 4.68237C11.9158 4.68237 11.3177 4.08425 11.3177 3.34906C11.3177 2.61387 11.9158 2.01572 12.651 2.01572C13.3862 2.01572 13.9843 2.61384 13.9843 3.34903C13.9843 4.08422 13.3862 4.68237 12.651 4.68237Z" fill="white" />
                            </svg>
                        </div>
                        <div class="social-dot">
                            <svg style="margin: 7px 0 0 9px" ; width="20" height="20" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16 2.02806C15.3988 2.28858 14.7776 2.46894 14.1162 2.5491C14.7976 2.1483 15.3186 1.50702 15.5591 0.725465C14.9178 1.10622 14.2164 1.36674 13.475 1.52706C12.8738 0.885785 12.012 0.484985 11.0702 0.484985C9.24652 0.484985 7.7836 1.96794 7.7836 3.77154C7.7836 4.03206 7.80364 4.27254 7.86376 4.51302C5.13833 4.39278 2.73353 3.07014 1.1103 1.08618C-0.0520223 3.17034 1.25058 4.89378 2.11229 5.47494C1.59125 5.47494 1.07022 5.31462 0.629336 5.07414C0.629336 6.69737 1.77161 8.04005 3.25457 8.34065C2.93393 8.44085 2.21249 8.50097 1.77161 8.40077C2.19245 9.70337 3.41489 10.6653 4.83773 10.6853C3.71549 11.5671 2.07221 12.2685 -0.0319824 12.048C1.43094 12.9899 3.15437 13.531 5.01809 13.531C11.0702 13.531 14.3567 8.52101 14.3567 4.19238C14.3567 4.0521 14.3567 3.91182 14.3367 3.77154C15.018 3.27054 15.5791 2.68938 16 2.02806Z" fill="white" />
                            </svg>
                        </div>
                        <div class="social-dot">
                            <svg style="margin: 6px 0 0 9px" ; width="20" height="20" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.4 13.4V8.71201C13.4 6.40801 12.904 4.64801 10.216 4.64801C8.92001 4.64801 8.05601 5.35201 7.70401 6.02401H7.67201V4.85601H5.12801V13.4H7.78401V9.16001C7.78401 8.04001 7.99201 6.96801 9.36801 6.96801C10.728 6.96801 10.744 8.23201 10.744 9.22401V13.384H13.4V13.4ZM0.808006 4.85601H3.46401V13.4H0.808006V4.85601ZM2.13601 0.600006C1.28801 0.600006 0.600006 1.28801 0.600006 2.13601C0.600006 2.98401 1.28801 3.68801 2.13601 3.68801C2.98401 3.68801 3.67201 2.98401 3.67201 2.13601C3.67201 1.28801 2.98401 0.600006 2.13601 0.600006Z" fill="white" />
                            </svg>
                        </div>
                    </div>
                </div> *@
            </div>
            <div class="settings-selection-data">
                @if (selectedSetting == MemberSetting.General)
                {
                    <GeneralSettings OnMessageChanged="SetBannerMessage" OnMessageTypeChanged="SetBannerMessageType" />
                }
                else if (selectedSetting == MemberSetting.SubscriptionInfo)
                {
                    <SubscriptionInfo UserSubscriptionDetail="@subscriptionDetail" />
                }
                else if (selectedSetting == MemberSetting.Notifications)
                {
                    <NotificationSettings />
                }
                else if (selectedSetting == MemberSetting.Language)
                {
                    <LanguageSelection />
                }
                else{                
                    <div class="main-head">
                        <div class="prep-text">
                            <span>Preferences</span>
                        </div>
                       @*  <div>
                            <div class="preped-btn"><span class="oi oi-print"></span> <span style="font-family: 'Raleway';">Save</span></div>
                        </div> *@
                    </div>
                    <hr />

                    <PlannerSettings CurrentUser="@currentUser" Diets="@diets" FoodCategories="@foodCategories" Ingredients="@ingredients"
                                     OnMessageChanged="SetBannerMessage" OnMessageTypeChanged="SetBannerMessageType" OnSettingsClosed="SetSelectedSetting" />
                }
            </div>
        @* </div> *@
    }
    else
    {
        <div class="loader-pos">
            <Loader />
        </div>
    }
</div>

@*footer*@
@* <div class="footer-pos">
    <Footer />
</div> *@

@code {
    private User currentUser;
    private List<Diet> diets = new List<Diet>();
    private List<string> foodCategories = new List<string>();
    private List<string> ingredients;
    private bool isLoading;
    private Brand logoBrand = new Brand();
    private string message;
    private MiscUtility.AlertMessageType messageType;
    private MemberSetting selectedSetting = MemberSetting.General;
    private UserSubscriptionDetail subscriptionDetail;
    private string userId;
    private string userImageId;
    private SubscriptionHelper.PaymentSystem userPaymentSystem;

    public enum MemberSetting
    {
        General,
        SubscriptionInfo,
        Notifications,
        Language,
        Preferences
    }

    public async Task Initialize()
    {
        isLoading = true;
        ingredients = await HttpClient.GetFromJsonAsync<List<string>>("/api/IngredientNames") ?? new List<string>();
        diets = await HttpClient.GetFromJsonAsync<List<Diet>>("/api/Diets") ?? new List<Diet>();
        foodCategories = await HttpClient.GetFromJsonAsync<List<string>>("/api/FoodCategories") ?? new List<string>();
        currentUser = userGlobalState.FullUserData;
        userId = userGlobalState.UserId;
        await GetBrand();
        userPaymentSystem = userGlobalState.UserSubscriptionDetail.Subscription.PaymentSystem;
        subscriptionDetail = userGlobalState.UserSubscriptionDetail;
        isLoading = false;
    }

    public override async Task GlobalStateLoaded()
    {
        if (!initialized && !initializationStarted)
        {
            await Initialize();
        }
    }

    private async Task GetBrand()
    {
        HttpResponseMessage response = await HttpClientAnon.Client.PostAsJsonAsync<string>("/api/BrandByName", NavManager.BaseUri.Replace("https://", string.Empty).Replace("http://", string.Empty).Replace("/", string.Empty));

        if (response.IsSuccessStatusCode)
        {
            logoBrand = await response.Content.ReadFromJsonAsync<Brand>() ?? new Brand();
        }
    }

    private void SetBannerMessage(string message)
    {
        this.message = message;
    }

    private void SetBannerMessageType(MiscUtility.AlertMessageType messageType)
    {
        this.messageType = messageType;
    }

    private void SetSelectedSetting(MemberSetting selectedSetting)
    {
        this.selectedSetting = selectedSetting;
    }

    private async Task UploadUserImage(InputFileChangeEventArgs args)
    {
        HttpResponseMessage response;

        using (MemoryStream ms = new MemoryStream())
        {
            await args.File.OpenReadStream().CopyToAsync(ms);
            byte[] newImageBytes = ms.ToArray();

            response = await HttpClient.PostAsJsonAsync($"/api/UpdateUserProfileImage/{userId}", newImageBytes);
        }

        if (!response.IsSuccessStatusCode)
        {
            message = "A problem occurred attempting to save your photo.";
            messageType = MiscUtility.AlertMessageType.Error;
        }
        else
        {
            userImageId = await response.Content.ReadAsStringAsync();
        }
    }
}