.get-help-section {
    display: flex;
    flex-flow: row;
    height: 100%;
    /*margin-top: 120px;
    margin-bottom: 50px;
    margin-left: 120px;*/
    padding-top: 120px;
    padding-left: 120px;    
}
.main-text {    
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 600;
    font-size: 23px;
    line-height: 27px;
}
.result-panel {  
    /*display:none;
    position: absolute;*/    
    position: relative;
    top: 80px;
    left: -120px;      
    background-color: white;    
    width: calc(100vw - 35%);
    height: auto;
    padding: 35px 0 100px 35px;      
}
.select-panel {     
    position:relative;
    top: 80px;
    left:-120px;
    width: 320px;
    background: rgba(242, 239, 225, 0.7);
    box-shadow: 0px 4px 65px rgba(0, 0, 0, 0.05);
    border-radius: 0px;
    min-height: 60vh;
}
.social li {
    color: #F58322;
    font-family: Raleway;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 111.111% */
    text-decoration-line: underline;
}
h3 {
    color: #000;
    font-family: Raleway;
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
}
p {
    color: rgba(0, 0, 0, 0.60);
    font-family: 'Raleway';
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 26px;    
}
.select-pos {
    padding: 40px 0 0 40px;
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 20px;
}
a {
    color: #F58322;
}
.cp-1{
    padding: 20px 0 5px 0;
}
.ep-1{

}
.select-item{
    padding: 18px 0px 18px 20px;
    cursor: default;
}

.select-item-selected {
    padding: 18px 0px 18px 20px;
    cursor: default;
    background-color: #DB792F;
    color: rgba(255, 255, 255, 1);
    border-radius: 7px 0px 0px 7px;
}

h4 {
    display: inline;
    color: #000;
    font-family: Raleway;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-decoration-line: underline;
}
.privacy-policy span {
    color: rgba(0, 0, 0, 0.8);
    font-family: Raleway;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}
.privacy-policy li {
    padding: 10px 0;
    color: #000;
    font-family: Raleway;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    opacity: 0.8;
}
.select-item-sm{
    width: calc(100vw - 30px);
}
    .select-item-sm span {
        color: rgba(0, 0, 0, 0.60);
        font-family: Raleway;
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        padding-left: 10px;
    }
    .select-item-sm svg{
        float: right;
        margin-top: 8px;
        margin-right: 5px;
    }
    .select-panel-sm{
        display:none;
    }

        .select-panel-sm .bar {
            width: 100%; 
            margin: 20px 0;
            height:0.3px;
            background-color: rgba(0, 0, 0, 0.20);
            opacity: 0.7;            
        }
        .btn-prv{
            font-family: Raleway;
            color: #DB792F !important;
            float: right;
            margin-top: -31px;
            display:none;
            
        }

@media only screen and (min-width: 300px) and (max-width: 749px){
    .select-panel{
        display:none;
    }
    .btn-prv{
        display:block;
    }
    .main-bar {
        width: 100%;
        margin: 15px 0 0 0;
        height: 0.3px;
        background-color: rgba(0, 0, 0, 0.20);
        opacity: 0.7;
    }
    .select-panel-sm{
        display:block;
        position:relative;
    }
    .get-help-section{
        margin-left: 8px;
        margin-top: 90px;
    }    
    
    .result-panel {       
        display:none;
        position: relative;
        left: -20px;
        background-color: white;
        top: -10px;
        width: calc(100% - 5px);
        height: auto;
        padding: 15px 0 100px 30px;
    }
    .get-help-section{
        display: inline-block;
        position: relative;
        top: 100px;
        margin:0;
        padding:0px 0 180px 10px;
    }
}
@media only screen and (min-width: 750px) and (max-width: 849px) {
    .select-panel {
        min-width: 250px;        
        left: -180px;
    }
    .get-help-section{
        
    }
    .select-item-selected {
        padding: 12px 0px 12px 14px;
    }

    .result-panel {        
        padding-left: 15px;
        left: -180px;
        width: 400px;
    }
    p{
        font-size: 16px;
        width: 400px;
    }
    h3,h4{
        font-size: 18px;
    }
    .get-help-section {
        margin-left: 0px;
    }
    .select-item {
        padding-left: 8px;
    }
    h5 {
        width: 400px;
    }
    .main-text span {
        width: 180px;
        display: block;
    }
}
@media only screen and (min-width: 850px) and (max-width: 950px) {
    .get-help-section {
        margin-left: -40px;
    }

    p {
        width: 450px;
    }

    .result-panel {
        left: -155px;
        width: 40px;
    }

    .select-panel {
        left: -160px;
        width: 260px;
    }

    .main-text span {
        width: 180px;
        display: block;
    }

    .select-item {
        padding-left: 8px;
    }

    h5 {
        width: 400px;
    }

    h3, li {
        width: 400px;
    }

    h4 {
        display: inline-block;
        width: 400px;
    }
}
@media only screen and (min-width: 951px) and (max-width: 1023px) {
    .get-help-section {
        margin-left: -20px;
    }
    p {
        width: 500px;
    }
    .result-panel{
        left: -155px;
        width: 40px;
    }
    .select-panel{
        left: -160px;
        width: 260px;
    }
    .main-text span{
        width: 180px;
        display:block;
    }
    .select-item {
        padding-left: 8px;
    }
    h5 {
        width: 400px;
    }
    h3, li {
        width: 500px;
    }

    h4 {
        display: inline-block;
        width: 400px;
    }
}
@media only screen and (min-width: 1024px) and (max-width: 1440px) {
    .get-help-section {
        margin-left: -20px;
    }

    p {
        width: 550px;
    }

    .result-panel {
        left: -185px;        
        width: 100px;
        
    }
    h3,li{
        width: 500px;
    }   
    h4{
        display:inline-block;
        width: 400px;
    }
    .select-panel {
        width: 260px;
        left: -180px;
        
    }
    .select-item{
        padding-left: 8px;
    }
    .select-item-selected{
        padding-left: 8px;
    }
    .main-text span {
        width: 180px;
        display: block;
    }
    h5 {
        width: 600px;
    }
}
@media only screen and (min-width:1100px) and (max-width: 1249px) {
    p {
        width: 710px;
    }

    .select-item {
        padding-left: 8px;
    }
    h5 {
        width: 600px;
    }
}
@media only screen and (min-width: 1250px) and (max-width: 1440px) {
    p {
        width: 750px;
    }
    .select-panel{
        width: 300px;
    }
    .result-panel{
        left: -175px;
    }
    .select-item {
        padding-left: 8px;
    }
    .get-help-section{
        margin-left: 20px;
    }
    h5 {
        width: 600px;
    }
}
@media only screen and (min-width: 1441px) and (max-width: 1919px) {
    p {
        width: 950px;
    }

    .select-panel {
        width: 300px;
        left: -150px;
    }

    .result-panel {
        left: -150px;
        width: 140px;
    }

    .select-item {
        padding-left: 8px;
    }

    .get-help-section {
        margin-left: 20px;
    }
    .main-text span {
        width: 180px;
        display: block;
    }
    h3, li {
        width: 500px;
    }

    h4 {
        display: inline-block;
        width: 400px;
    }
    h5 {
        width: 600px;
    }
}


@media only screen and (min-width: 2560px){
    p {
        width: 1800px;
    }

    .select-panel {
        width: 400px;
        left: -150px;
    }

    .result-panel {
        left: -170px;
        width: 140px;
    }

    .select-item {
        padding-left: 8px;
    }

    .get-help-section {
        margin-left: 20px;
    }

    .main-text span {
        width: 180px;
        display: block;
    }

    h3, li {
        width: 500px;
    }
    h5{
        width: 600px;
    }
    h4 {
        display: inline-block;
        width: 400px;
    }
}