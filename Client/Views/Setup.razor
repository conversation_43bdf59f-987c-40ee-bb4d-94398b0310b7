@page "/setup"
@attribute [Authorize]

@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using Newtonsoft.Json.Linq
@using System.Security.Claims
@using TwentyDishes.Client.State.UserGlobalState
@using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent
@using TwentyDishes.Client.Components.SplashScreen

@inherits UserBaseComponent

@inject HttpClient HttpClient
@inject NavigationManager NavManager
@inject HttpClientAnon HttpClientAnon
@inject IJSRuntime jsRuntime

@if (loading)
{
    <SplashScreen />
}
else 
{
    @if (setupStep > 0)
    {
        <div class="main-header">
            <div class="outer-wrapper">
                <div class="twenty-dishes-logo">
                    @if (!string.IsNullOrWhiteSpace(logoBrand.LogoCloudflareId))
                    {
                        <img src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, logoBrand.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logoBrand.PageTitle)" />
                    }
                </div>
            </div>
        </div>
        <div class="progress-pos">
            <div class="progress-wrapper">
                <div class="progress-img">
                    <img class="img-prp" src="/images/imgpsh_fullsize_anim (2).png" alt="error">
                </div>
                <div class="progress-heading">
                    <span style="color:var(--salesButtonOrange)">
                        @GetColoredHeadingText()
                    </span>
                    @GetPlainHeadingText()
                </div>
                <div class="progress-text">
                    @GetProgressHeadingText()
                </div>
                <div class="progress-container">
                    <div class="progress" id="progress"></div>
                    <div class="circle active">1</div>
                    <div class="circle">2</div>
                    <div class="circle">3</div>
                    <div class="circle">4</div>
                    <div class="circle">5</div>
                </div>
            </div>
        </div>
    }

    @if (setupStep == 0)
    {
        <div class="setup-wrapper">
            <div class="img-sm">
                @if (!string.IsNullOrWhiteSpace(logoBrand.LogoCloudflareId))
                {
                    <img class="logo-sm" src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, logoBrand.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logoBrand.PageTitle)" />
                }
                <img class="top-right-img" src="/images/Img-topright-sm.png" />
                <img class="bottom-img" src="/images/Img-bottom-sm.png" />
            </div>
            <div class="welcome-modal">
                <div class="welcome-heading">
                    <h2><span style="color:rgba(219, 121, 47, 1);">Hello,</span> and Welcome Aboard!</h2>
                </div>
                <div class="welcome-text">
                    <h5>
                        It's time to get you set up
                        - exciting, isn't it? This isn't set in stone.
                        You can mix it up anytime you
                        want. It's our goal to make your
                        meal planning a piece of cake
                        (pun intended!). So let's dive in
                        and start this delicious journey
                        together!
                    </h5>
                </div>
                <div class="welcome-next" style="top: -30px;">
                    <span class="btn-style" @onclick="IncrementStep">Next</span>
                </div>
            </div>
        </div>
    }

    @if (setupStep > 0)
    {
        <div class="step">
            @if (setupStep == 1)
            {
                <div class="pes-pos">
                    <div class="pes-container">
                        <div class="pes-heading">
                            <h3>What is your name?</h3>
                        </div>
                        <div class="pes-form">
                            <div class="form-group-pos">
                                <label>First Name</label>
                                <input @ref="elNameFirst" @bind="@nameFirst" @bind:event="oninput" />
                            </div>
                            <div class="form-group-pos">
                                <label>Last Name</label>
                                <input @bind="@nameLast" @bind:event="oninput" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="pes-pos">
                    <div class="member-container">
                        <div class="mem-heading">
                            <h3>
                                How many people are typically around
                                your table at dinnertime?
                            </h3>
                        </div>
                        <div class="setup-numeric">
                            <SfNumericTextBox TValue="byte" Min="1" @bind-Value="@familySize"></SfNumericTextBox>
                        </div>
                    </div>
                </div>
            }
            else if (setupStep == 2)
            {
                <div class="pes-pos">
                    <div class="pes-container">
                        <div class="div-cont">
                            @if (diets != null)
                            {
                                @*  <div class="diet-pill" @onclick="@(() => SetDiet(diet.Name))">
                                <span class="@(userDiets.Where(x => x == diet.Name).Any() ? "diet-pill-active" : "diet-pill-style")">@diet.Name</span>
                            </div> *@
                            <div class="diet-input-container">
                                <div class="diet-input-box" @onclick="ToggleDietDropdown">
                                    <span class="toggle-adj dropdown-toggle"></span>
                                    <span class="selected-text">
                                        @if (userDiets.Any())
                                        {
                                            foreach (var item in userDiets)
                                            {                                                
                                                <img class="diet-dropdown-icon" src=@($"/images/DietIcons/{GetIconByDietName(item)}") />
                                                <span class="diet-items">@item</span>                                                
                                            }
                                        }
                                        else
                                        {
                                            <span>Please Select a Diet</span>
                                        }
                                    </span>
                                </div>
                                </div>

                                <div class="diet-input-dropdown" style=@(IsDropdownOpen == false ? "display: none" : "display: block")>
                                    @foreach (Diet diet in diets)
                                    {
                                        if (diet.Name != "Vegan" && diet.Name != "5 Ingredients Or Less")
                                        {
                                            <div class="diet-items-container" @onclick=@(() => SetDiet(diet.Name))>
                                                <img class="diet-dropdown-icon" src=@($"/images/DietIcons/{GetIconByDietName(diet.Name)}") />
                                                <span class="diet-items">@diet.Name</span>
                                            </div>
                                        }
                                    }  
                                    </div>                                      
                            }
                            else
                            {
                                <Loader />
                            }
                        </div>
                    </div>
                </div>
            }
            else if (setupStep == 3)
            {
                <div class="pes-pos">
                    <div class="pes-container">
                        <div class="step3-heading-pos">
                            <h3 class="step3-heading">Select the foods you do NOT want included in your menu:</h3>
                        </div>
                        <div class="step3-cont">
                            @foreach (string fc in foodCategories)
                            {
                                <div class="diet-pill" @onclick="@(() => SetFoodCategory(fc))">
                                    <span class="@(userFoodCategories.Contains(fc) ? "diet-pill-active" : "diet-pill-style")">@fc</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
            else if (setupStep == 4)
            {
                <div class="additional-excluded-container">
                    <div class="exlude-heading">
                        <h3>Search for any additional ingredients you would like to exclude:</h3>
                        <div class="search-ingr">
                            <SfAutoComplete TValue="string" Placeholder="Search here...." TItem="string" DataSource="@ingredients" CssClass="e-outline" ValueChanged="@((args) => SetIngredientException(args))" />
                        </div>
                        <div class="ingr-exc-list">
                            <SfListView DataSource="@userIngredientExclusions">
                                <ListViewFieldSettings TValue="string"></ListViewFieldSettings>
                                <ListViewTemplates TValue="string">
                                    <Template>
                                        <div class="listView-template" @onclick="@(() => RemoveIngredientException(context))">
                                            <span>@context</span>
                                            <a><img class="img-delete" src="images/delete.png" title="Delete" /></a>
                                        </div>
                                    </Template>
                                </ListViewTemplates>
                            </SfListView>
                        </div>
                    </div>
                </div>
            }
            else if (setupStep == 5)
            {
                <div class="pes-pos">
                    <div class="pes-container">
                        <div class="recipe-selection">
                            <div class=@(mealSelection.BreakfastEnabled ? "recipe-style-active" : "recipe-style") @onclick="@(() => ToggleRecipe("breakfast"))">I Want Breakfast Recipes</div>
                            <div class=@(mealSelection.LunchEnabled ? "recipe-style-active" : "recipe-style") @onclick="@(() => ToggleRecipe("lunch"))">I Want Lunch Recipes</div>
                            <div class=@(mealSelection.DinnerEnabled ? "recipe-style-active" : "recipe-style") @onclick="@(() => ToggleRecipe("dinner"))">I Want Dinner Recipes</div>
                            @*<div class=@(mealSelection.SnacksEnabled ? "recipe-style-active" : "recipe-style") @onclick="@(() => ToggleRecipe("snacks"))">I Want Snack Recipes</div>*@
                        </div>
                    </div>
                </div>
                @*<div class="pes-pos">
                    <div class="member-container">
                        <div class="mem-heading">
                            <h3>
                                Print Settings
                            </h3>
                            <h6>
                                Lastly, how do you want your recipes
                                to strut their stuff on paper? Check
                                the box if each recipe deserves its
                                own spotlight with 'one recipe per
                                page'. And, just like choosing the
                                perfect outfit, pick the font size
                                that suits your style best. We're
                                starting you off at a chic size 12,
                                but feel free to shake it up!
                            </h6>
                            <input type="checkbox" @bind-value="printOneRecPerPage" />
                            <h3 class="d-inline ps-2">Print one recipe per page</h3>
                        </div>
                        <div class="print-size-wrapper">
                            <div class="setup-numeric">
                                <SfNumericTextBox TValue="byte" Min="8" Max="40" @bind-Value="@printFontSize"></SfNumericTextBox>
                                    </div>
                            <label class="lbl-print-font-size">Print Font Size</label>
                        </div>
                    </div>
                </div>*@

            }
            @if (!String.IsNullOrEmpty(errorMessage)) {<div class="error-message">@errorMessage</div>}
            <div class="welcome-next">
                @if (setupStep > 1 && setupStep <= 5)
                {
                    <span class="btn-style-back" @onclick="DecrementStep">Back</span>
                }
                @if (setupStep == 5)
                {
                    <span class="btn-style" style="font-size:14px;" @onclick="SaveSettings">
                        View My Meal Plan
                    </span>
                }
                else if (setupStep > 0)
                {
                    <button class="btn-style" @onclick="IncrementStep" disabled="@NextDisabled()">
                        Next
                    </button>
                }
            </div>
        </div>
    }   
}

@code {
    private User currentUser;
    private List<Diet> diets = new List<Diet>();
    private ElementReference elNameFirst;
    private byte familySize = 1;
    private List<string> foodCategories = new List<string>();
    private List<string> ingredients = new List<string>();
    private Brand logoBrand = new Brand();
    private UserMealSelection mealSelection = new UserMealSelection();
    private string nameFirst;
    private string nameLast;    
    private byte printFontSize = 12;
    private bool printOneRecPerPage;
    public bool IsDropdownOpen;
    private byte setupStep = 0;
    private List<string> userDiets = new List<string>();
    private List<string> userFoodCategories = new List<string>();
    private List<string> userIngredientExclusions = new List<string>();
    private string? errorMessage = null;

    private bool updatingUser = false;
    private bool doingSetup = true;
    private bool loading => doingSetup || userGlobalState.Loading || updatingUser;

    [CascadingParameter(Name = UserGlobalStateFields.Load)]
    public EventCallback LoadGlobalUserState { get; set; }

    public async Task Initialize()
    {
        initializationStarted = true;

        IsDropdownOpen = false;
        await GetBrand();
        IsDropdownOpen = false;
        ingredients = await HttpClient.GetFromJsonAsync<List<string>>("/api/IngredientNames") ?? new List<string>();
        diets = await HttpClient.GetFromJsonAsync<List<Diet>>("/api/Diets") ?? new List<Diet>();
        foodCategories = await HttpClient.GetFromJsonAsync<List<string>>("/api/FoodCategories") ?? new List<string>();
        currentUser = userGlobalState.FullUserData;

        initialized = true;
        initializationStarted = false;
        doingSetup = false;
    }   

    public override async Task GlobalStateLoaded()
    {
        if (!initialized && !initializationStarted)
        {
            await Initialize();
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (setupStep == 1 && string.IsNullOrWhiteSpace(nameFirst) && !loading)
        {
            await elNameFirst.FocusAsync();
        }
    }

    private async Task DecrementStep()
    {
        if (setupStep > 1)
        {
            await jsRuntime.InvokeVoidAsync("calcSetupProgress", "prev");
            setupStep--;
        }
    }

    private async Task GetBrand()
    {
        HttpResponseMessage response = await HttpClientAnon.Client.PostAsJsonAsync<string>("/api/BrandByName", NavManager.BaseUri.Replace("https://", string.Empty).Replace("http://", string.Empty).Replace("/", string.Empty));

        if (response.IsSuccessStatusCode)
        {
            logoBrand = await response.Content.ReadFromJsonAsync<Brand>() ?? new Brand();
        }
    }

    private string GetColoredHeadingText()
    {
        switch (setupStep)
        {
            case (1):
                return "Flavor";
            case (2):
                return "It's Time to Pick Your";
            case (3):
                return "Ready for a Game";
            case (4):
                return "Now We Get";
            case (5):
                return "Finally,";
            default:
                return " ";
        }
    }

    private string GetPlainHeadingText()
    {
        switch (setupStep)
        {
            case (1):
                return "Profile Setup";
            case (2):
                return "Culinary Path!";
            case (3):
                return "of Culinary ‘Would You Rather’?";
            case (4):
                return "to Play Food Hide and Seek";
            case (5):
                return "Let's Map Your Meals!";
            default:
                return " ";
        }
    }

    private void ToggleDietDropdown()
    {
        if (IsDropdownOpen == true)
        {
            IsDropdownOpen = false;
        }
        else
        {
            IsDropdownOpen = true;
        }

    }


    private string GetIconByDietName(string dietName)
    {
        return dietName + ".svg";
    }

    private string GetProgressHeadingText()
    {
        switch (setupStep)
        {
            case (1):
                return " ";
            case (2):
                return "This is where you get to tell us what kind of food you enjoy. If in doubt, choose 'Real Food' for a large variety of tasty recipes!";
            case (3):
                return "Which food groups are getting the 'not today' from you? Tick off the ones you'd rather dodge on your flavor voyage.";
            case (4):
                return "Are there any sneaky ingredients you'd rather keep out of your meals? Just type them in and we'll make sure they're kept at bay in your recipes. Shrimp, onions, mushrooms, you name it - if it's not your jam, it's not in your plan!";
            case (5):
                return "What's your recipe story going to be? Morning wake-up call with breakfast, midday boost with lunch, or evening feast for dinner? Check the boxes for the meal times you want to star in your day!";
            default:
                return " ";
        }
    }

    private async Task IncrementStep()
    {
        //Cannot increment - step 5 is max
        if (setupStep == 5)
        {
            return;
        }
        else if (setupStep == 0)
        {
            setupStep++;

            await jsRuntime.InvokeVoidAsync("ScrollToTop");
        }
        else if (setupStep == 2)
        {
            if (userDiets.Count <= 0)
            {
                errorMessage = "Pick a path option above to get started!";

                return;
            }
            setupStep++;

            await jsRuntime.InvokeVoidAsync("ScrollToTop");

            await jsRuntime.InvokeVoidAsync("calcSetupProgress", "next");
        }
        else
        {
            await jsRuntime.InvokeVoidAsync("calcSetupProgress", "next");

            setupStep++;

            await jsRuntime.InvokeVoidAsync("ScrollToTop");
        }
    }

    private void RemoveIngredientException(string args)
    {
        if (userIngredientExclusions.Contains(args))
        {
            userIngredientExclusions.Remove(args);
        }
    }

    private async Task SaveSettings()
    {
        updatingUser = true;
        StateHasChanged();

        HttpResponseMessage response;

        currentUser.FamilySize = familySize;
        currentUser.FirstName = nameFirst;
        currentUser.IsSetup = true;
        currentUser.LastName = nameLast;
        currentUser.MealSelection = mealSelection;
        currentUser.PrintFontSize = printFontSize;
        currentUser.PrintOneRecipePerPage = printOneRecPerPage;
        currentUser.UserDiets = userDiets;
        currentUser.UserFoodCategoryExclusions = userFoodCategories;
        currentUser.UserIngredientExclusions = userIngredientExclusions;

        response = await HttpClient.PutAsJsonAsync<User>("/api/UpdateUser?generateUserWeek=true", currentUser);

        if (response.IsSuccessStatusCode)
        {
            await LoadGlobalUserState.InvokeAsync();
            
            NavManager.NavigateTo("/menu");
        }
    }

    private void SetDiet(string dietName)
    {
        errorMessage = null;
        userDiets.Clear();
        userDiets.Add(dietName);
        IsDropdownOpen = false;
    }

    private void SetFoodCategory(string foodCategoryName)
    {
        //Add/Remove user food categories based on what's already in the list
        if (userFoodCategories.Contains(foodCategoryName))
        {
            userFoodCategories.Remove(foodCategoryName);
        }
        else
        {
            userFoodCategories.Add(foodCategoryName);
        }
    }

    private void SetIngredientException(string args)
    {
        if (!userIngredientExclusions.Contains(args) && !string.IsNullOrWhiteSpace(args))
        {
            userIngredientExclusions.Add(args);
        }
    }

    private void ToggleRecipe(string mealType)
    {
        switch (mealType)
        {
            case "breakfast":
                if (mealSelection.BreakfastEnabled)
                {
                    mealSelection.BreakfastEnabled = false;
                    break;
                }
                else
                {
                    mealSelection.BreakfastEnabled = true;
                    break;
                }
            case "lunch":
                if (mealSelection.LunchEnabled)
                {
                    mealSelection.LunchEnabled = false;
                    break;
                }
                else
                {
                    mealSelection.LunchEnabled = true;
                    break;
                }
            case "dinner":
                if (mealSelection.DinnerEnabled)
                {
                    mealSelection.DinnerEnabled = false;
                    break;
                }
                else
                {
                    mealSelection.DinnerEnabled = true;
                    break;
                }
            case "snacks":
                if (mealSelection.SnacksEnabled)
                {
                    mealSelection.SnacksEnabled = false;
                    break;
                }
                else
                {
                    mealSelection.SnacksEnabled = true;
                    break;
                }
        }
    }

    private bool NextDisabled() {
        if (setupStep == 1 && (String.IsNullOrEmpty(nameFirst) || String.IsNullOrEmpty(nameLast))) {
            return true;
        }

        return false;
    }
}