using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace TwentyDishes.Client.Views.Welcome
{
    public partial class Welcome
    {
        [Parameter]
        [SupplyParameterFromQuery]
        public string? Plan { get; set; }

        [Inject]
        public IJSRuntime JSRuntime {get; set;}

        protected override async Task OnInitializedAsync()
        {
            if (Plan is not null)
            {
                // await SessionStorage.SetItemAsync("Plan", Plan);
                await JSRuntime.InvokeVoidAsync("setSessionItem", "Plan", Plan);
            }
        }
    }
}