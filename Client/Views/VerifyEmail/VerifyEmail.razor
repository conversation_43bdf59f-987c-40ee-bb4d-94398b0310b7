@page "/verify-email"

@using TwentyDishes.Client.Components.FullsizeBackgroundPage
@using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent
@using TwentyDishes.Client.Components.SplashScreen
@using TwentyDishes.Client.Components.TdButton2
@using TwentyDishes.Client.Components

@inherits UserBaseComponent

<div class="verify-email">
    <Banner @bind-MessageType="@messageType" @bind-Message="@message"/>

    <div class="login-header">
        <LoginDisplay isBackgroundTransparent="true"/>
    </div>
    @if (UserGlobalStateValue.Loading)
    {
        <SplashScreen />
    }
    else if (!UserGlobalStateValue.EmailVerified)
    {
        <FullsizeBackgroundPage>
            <div class="logo-wrapper">
                @if (!string.IsNullOrWhiteSpace(logoBrand.LogoCloudflareId))
                {
                    <img src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, logoBrand.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)"
                        alt="@(logoBrand.PageTitle)" class="logo" />
                }

            </div>
            <div class="text-center">Please verify your email address using the link we emailed to you.</div>
            <div class="button-wrapper">
                <div class="btn-adj"><TdButton2 OnClick="HandleResendVerificationLinkClick" Loading="sendingRequest">Resend Verification Link</TdButton2></div>

                <div class="btn-adj-2"><TdButton2 OnClick="HandleGoHomeClick" DisplayType="@TdButton2DisplayType.Minimal"><span class="text-min">Take me to the homepage, I completed email verification</span></TdButton2></div>
            </div>
        </FullsizeBackgroundPage>
    }
    else
    {
        <FullsizeBackgroundPage>
            All set! Your email is verified.
            <div class="mt-4"><TdButton2 OnClick="HandleGoHomeClick" Loading="sendingRequest">Go to Home</TdButton2></div>
        </FullsizeBackgroundPage>
    }
</div>