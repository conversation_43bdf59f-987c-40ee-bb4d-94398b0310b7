using Microsoft.AspNetCore.Components;
using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent;
using TwentyDishes.Client.State.PublicGlobalState;
using TwentyDishes.Client.State.UserGlobalState;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Client.Views.VerifyEmail
{
    public partial class VerifyEmail : UserBaseComponent
    {
        [Inject]
        public HttpClient httpClient { get; set; }

        [Inject]
        public NavigationManager NavManager {get; set;}

        [CascadingParameter(Name = UserGlobalStateFields.Load)]
        public EventCallback LoadGlobalUserState { get; set; }

        [CascadingParameter(Name = PublicGlobalStateFields.Value)]
        public PublicGlobalStateValue PublicGlobalStateValue {get; set;}

        private Brand? logoBrand => PublicGlobalStateValue.Brand;
        private bool sendingRequest = false;
        private string message;
        private MiscUtility.AlertMessageType messageType;

        public async Task HandleResendVerificationLinkClick()
        {
            try
            {
                sendingRequest = true;

                await httpClient.PostAsync("/api/ResendEmailVerificationLink", null);

                messageType = MiscUtility.AlertMessageType.Success;
                message = "Verification email sent!";
            }
            catch (Exception)
            {
                messageType = MiscUtility.AlertMessageType.Error;
                message = "Verification email could not be sent.";
            }
            finally
            {
                sendingRequest = false;
            }
        }

        public async Task HandleGoHomeClick()
        {
            await LoadGlobalUserState.InvokeAsync();

            if (UserGlobalStateValue.EmailVerified) NavManager.NavigateTo("/menu");
        }
    }
}