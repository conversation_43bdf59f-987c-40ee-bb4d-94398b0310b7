/*header*/
/*.outer-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 115px;
    width: calc(100vw - (100vw - 100%));
    background-color: var(--lightAccentYellow);
    z-index: 1001;
}

    .outer-wrapper .twenty-dishes-logo img {
        position:absolute;
        top: 0;
        left:80px;
        height: 80px;               
        background-color: var(--lightAccentYellow);
    }
      
    .outer-wrapper .login-display { 
        position: relative;
        float: right;
        padding-right: 40px;
        padding-bottom:20px;
        background-color: var(--lightAccentYellow);
    }

.icon-position {
    font-family: 'Raleway', sans-serif;
    font-weight: 500;
    padding: 0px 0px 20px 90px;
    font-size: 20px;
    cursor: pointer;
}
*/
.settings-main {
/*    position: absolute;
    top: 85px;
    left: 120px;   */
    position: relative;
    display:inline-block;
    margin: 150px 0 70px 130px;
    min-height: 1000px;
}
.settings-heading{
    position:relative;
    top: 130px;
    margin-left: 150px;
}
.settings-heading h3{
    font-family: 'Raleway';
    font-size: 28px;
    font-weight: 700;
}
.settings-header {
    position:relative;
    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.12);
    border-radius: 10px;
    background-color:white;
    padding: 20px 5px 5px 20px;
    width: calc(94vw - 100px);
    height: 210px;
    z-index: 1002;
}
 
h1 {
    color: var(--vibrantRed);
    font-size: 26px;
    font-weight: 700;
    padding-bottom: 10px;
    padding: 10px 0 0 8px;
}

p {
    padding: 10px 250px 0 10px;
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 26px;

}
.cover-block {
    height: 58px;
    width: 20px;
    background-color: var(--lightAccentYellow);
    position: absolute;
    left: 50px;
    top: 60px;
    z-index: 5000;
}
.radius-set {
    height: 100vh;
    width: 20px;
    background-color: white;
    position: absolute;
    top: 113.9px;
    left: 50.5px;
    border-radius: 7px 0px 0 0;
    border: 1px solid var(--lightAccentYellow);
    border-right: none;
    border-bottom: none;
    z-index: 5001;
}
.avatar-img {
    background-image: url('images/background-image.png');   
    background-repeat:no-repeat;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin: 20px 0 20px 100px;
    object-fit: cover;
    object-position: center right;
}
.avatar-controls {
    color: rgba(219, 121, 47, 1);
    text-align: center;
    font-family: 'Raleway';
    font-weight: 500;
    cursor:pointer;
    width: 350px;
}

.group-img {
    background-image: url('images/background-image.png');    
    width: 76px;
    height: 80px;
    border-radius: 4px;
}
.group-text {
    font-family: 'Raleway';
    font-weight: 600;
    padding: 15px 0 0 0;
}
.group-info{
    position:relative;
    display:flex;
    flex-direction: column;
    left: 27%;
    top:-75px;    
}
.groups{
    margin-left: 20px;
}
.group-pos{
    padding-top:20px;
}

.social-dot {
    height: 38px;
    width: 38px;
    background-color: rgba(219, 121, 47, 1);
    border-radius: 50%;     
}
.social-pos{
    padding: 20px 0 30px 25px;
}
.group-pill {
    padding: 5px 10px;
    background: #F1F1F1; 
    /*background: blue;*/
    border-radius: 4px;
}
.setting-selection {
    position: relative;
    display: flex;
    flex-direction: row;
    padding: 35px 0 40px 0;    
}

.setting-item-container {
    position: relative;
    padding-left: 10px;
}
.bar-sep {
    margin: 15px 0 15px 28px;
    width: 303px;
    border: 0.1px solid rgba(0, 0, 0, 0.2);
}
.setting-item {
    color: var(--salesButtonOrange);
    background-color: #fff;
    border-color: var(--salesButtonOrange);
    border-width: 1px;
    border-style: solid;
    border-radius: 16px;
    min-width: 110px;
    font-size: 14px;
    font-family: 'Raleway';
    padding: 5px 20px 4px 20px;
}

.setting-item-focused {
    color: #fff;
    background-color: var(--salesButtonOrange);
}

.settings-content {
    display: flex;
    flex-direction: row;
}
.loader-pos{
    position: absolute;
    margin: 300px auto;
    width:82vw;
}
 
.member-profile-info {
    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.12);
    border-radius: 10px;
    margin-right: 30px;
    width: 361px;
    height: 800px;    
}

.settings-selection-data {
    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.12);
    border-radius: 10px;
    width: calc(100vw - 300px);
    height:800px;
    overflow-y:scroll;    
    justify-content: center;
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
    /*box-shadow: inset 0 0 5px grey;
        border-radius: 10px;*/
    background: #D7D7D7;
    /*box-shadow: inset 4px 4px 13px rgba(0, 0, 0, 0.14);*/
    border-radius: 10px;
    transform: rotate(90deg);
}
 
/* Handle */
::-webkit-scrollbar-thumb {
    background: #E5DFC3;
    border-radius: 10px;
    transform: rotate(90deg);
}

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #DB792F;
    }

.member-profile-sm {
    position:relative;
    margin-top: 20px;
    width: calc(100vw - 30px);
    height:68px;
    background: #FFFFFF;
    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.12);
    border-radius: 10px;    
}

.member-profile-sm{
    display:none;
}
.avatar-img-sm {
    position:relative;
    background-image: url('images/background-image.png');
    background-repeat: no-repeat;
    width: 48px;
    height: 48px;
    top: 10px;
    left: 10px;
    border-radius: 50%;    
    object-fit: cover;
    object-position: center right;
}
.profile-controls {
    position: absolute;
    display: flex;
    flex-direction: column;
    left: 67px;
    top: 11px;    
    color: #DB792F;
    font-family: 'Raleway';    
    font-weight: 500;
    font-size: 14px;
    cursor: auto;
}
.profile-btn{
    position:relative;
    top: -34px;
    right: 22px;
    float:right;
}
.div-sep-sm{
    display:none;
}

.preped-btn {
    cursor: pointer;
    float: right;
    padding: 15px 27px;
    margin-right: 60px;
    margin-top: -44px;
    background: #CA2F35;
    border: 1px solid #CA2F35;
    border-radius: 55px;
    color: white;
}

@media only screen and (max-width: 375px) and (min-width: 320px) {
    .main-header {
        display: none;
    }
    .settings-header {        
        width: calc(100vw - 31px);      
        height: auto;
        padding-left: 7px;        
    }
        .settings-header p {
            padding-right: 5px;
            font-size: 13px;
        }
    .preped-btn{
        margin: -40px 20px 0 0;
        padding: 12px 22px;
    }
    .settings-main {            
        margin: 100px 0px 0 13px;
    }
    .radius-set{
        display:none;
    }
    .cover-block{
        display:none;
    }
    .footer-pos {
        display: none;
    }

    .setting-selection {
        width: calc(100% - 50px);
        overflow-y: hidden;
        overflow-x: scroll;
        -webkit-overflow-scrolling: touch;
        &::-webkit-scrollbar{
                                display: none;
                            }
    }         
    .member-profile-info{
        display:none;
    }
    .member-profile-sm{
        display:block;
    }
    .div-sep-sm{
        display:block;
    }
    .bar-sep {
        margin: 35px 0 5px 4.5px;
        width: 280px;
        border: 0.1px solid rgba(0, 0, 0, 0.09);
    }
    .setting-item {
        font-size: 11px;
        padding: 2px 5px;                
    }
    .settings-selection-data{
        width: calc(100vw - 30px);
    }
    .settings-heading{
        top: 110px;
        margin-left: 25px;
    }
}
@media only screen and (max-width: 425px) and (min-width: 375px) {
    .main-header {
        display: none;
    }

    .settings-header {
        width: calc(100vw - 31px);
        height: auto;
        padding-left: 7px;
    }   
        .settings-header p {
            padding-right: 5px;
            font-size: 13px;
        }

    .settings-main {
        margin: 100px 0 0 15px;
    }
    .preped-btn{
        margin-right: 25px;
    }
    .radius-set {
        display: none;
    }

    .cover-block {
        display: none;
    }
    .footer-pos {
        display: none;
    }

    .setting-selection {
        width: calc(100vw - 31px);
        overflow-y: hidden;
        overflow-x: scroll;
        -webkit-overflow-scrolling: touch;
        &::-webkit-scrollbar

        {
            display: none;
        }
}

.member-profile-info {
    display: none;
}

.member-profile-sm {
    display: block;
}

.div-sep-sm {
    display: block;
}

.bar-sep {
    margin: 35px 0 5px 4.5px;
    width: calc(100vw - 40px);
    border: 0.1px solid rgba(0, 0, 0, 0.09);
}

.setting-item {
    font-size: 11px;
    padding: 2px 5px;
}

.settings-selection-data {
    width: calc(100vw - 35px);
    height: 600px;
    margin-bottom: 40px;
}
.settings-heading {
    top: 110px;
    margin-left: 25px;
}
}
@media only screen and (max-width: 600px) and (min-width: 425px) {
    .main-header {
        display: none;
    }
    .radius-set {
        display: none;
    }

    .cover-block {
        display: none;
    }
    .settings-header {
        width: calc(100vw - 31px);
        height: auto;
        padding-left: 7px;
    }

        .settings-header p {
            padding-right: 5px;
            font-size: 13px;
        }

    .settings-main {
        margin: 100px 0 0 13px;
    }

    .footer-pos {
        display: none;
    }
    .preped-btn {
        margin-right: 18px;
    }
    .setting-selection {
        width: calc(100vw - 31px);
        overflow-y: hidden;
        overflow-x: scroll;
        -webkit-overflow-scrolling: touch;
        &::-webkit-scrollbar

{
    display: none;
}

}

.member-profile-info {
    display: none;
}

.member-profile-sm {
    display: block;
}

.div-sep-sm {
    display: block;
}

.bar-sep {
    margin: 35px 0 5px 4.5px;
    width: calc(100vw - 40px);
    border: 0.1px solid rgba(0, 0, 0, 0.09);
}

.setting-item {
    font-size: 11px;
    padding: 2px 5px;
}

.settings-selection-data {
    width: calc(100vw - 28px);
    height: 600px;
    margin-bottom: 40px;
}

}

.prep-btn {
    cursor: pointer;
    float: right;
    padding: 15px 27px;
    margin-right: 60px;
    margin-top: -44px;
    background: #CA2F35;
    border: 1px solid #CA2F35;
    border-radius: 55px;
    color: white;
}

.prep-text {
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
}

.main-head {
    position: relative;
    padding: 40px 0 10px 40px;
}
.settings-heading {
    top: 110px;
    margin-left: 25px;
}
hr {
    margin: 20px 20px 10px 20px;
}
@media only screen and (max-width: 700px) and (min-width: 601px) {
    .settings-heading {
        top: 110px;
        margin-left: 85px;
    }
    .settings-selection-data{
        width: calc(100% - 70px);
        
    }
}
    @media only screen and (max-width: 1919px) and (min-width:701px) {
        .settings-header {
            height: 230px;
        }        
        .settings-heading{
            margin-left: 120px;
        }
        h1 {
            font-size: 22px;
        }

        p {
            font-size: 15px;
        }
    }
@media only screen and (min-width: 1920px){
    .settings-heading {
        margin-left: 120px;
    }
}