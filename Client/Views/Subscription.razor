@page "/subscription"
@page "/subscription/{plan}"
@attribute [Authorize]

@using TwentyDishes.Client.State.UserGlobalState
@using TwentyDishes.Client.Components.SubscriptionSelection
@using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent

@inherits UserBaseComponent

@inject NavigationManager NavManager

<div class="subsc-main">
    <SubscriptionSelection OnSubscriptionSelected="RerouteAfterSelection"
        InitialPlanSelection="@plan" />
</div>

@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? Plan { get; set; }

    [CascadingParameter(Name = UserGlobalStateFields.Load)]
    public EventCallback LoadGlobalUserState { get; set; }

    [Inject]
    public IJSRuntime JSRuntime {get; set;}

    public SubscriptionHelper.SubscriptionType? plan = null;

    public override async Task SetParametersAsync(ParameterView parameters)
        {
            parameters.SetParameterProperties(this);

            // var planFromSession = await SessionStorage.GetItemAsync<string>("Plan");
            var planFromSession = await JSRuntime.InvokeAsync<string>("getSessionItem", "Plan");

            if (planFromSession is not null)
            {
                plan = planFromSession == "free" ? SubscriptionHelper.SubscriptionType.Free : SubscriptionHelper.SubscriptionType.Monthly;

                // await SessionStorage.RemoveItemAsync("Plan");
                await JSRuntime.InvokeVoidAsync("removeSessionItem", "Plan");
            }

            if (Plan is not null)
            {
                plan = Plan == "free" ? SubscriptionHelper.SubscriptionType.Free : SubscriptionHelper.SubscriptionType.Monthly;
            }

            await base.SetParametersAsync(ParameterView.Empty);
        }

    private async Task RerouteAfterSelection()
    {
        await LoadGlobalUserState.InvokeAsync();

        if (userGlobalState.UserIsSetup)
        {
            NavManager.NavigateTo("/menu");
        }
        else
        {
            NavManager.NavigateTo("/setup");
        }
    }
}