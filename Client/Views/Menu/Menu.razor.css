/*These classes have been sorted according to the markup*/
/*.body{
    overflow-x: hidden;
}*/
svg {
    cursor: pointer;
}

.plus-adj {
    margin: 0 10px 10px 0;
}

/*.print-btns{
    margin-top: 5px;
}*/
.second-header {
    background-color: #F3F3F3;
    height: 65px;
    width: calc(100vw - (100vw - 100%));
    border-radius: 10px;
    margin-left: 51px;
    position: fixed;
    z-index: 1004;
    top: 62px;
}

.print-icon {
    height: 55px;
    width: 55px;
    margin-left: 20px;
}

.ad-horizontal {
    height: 90px;
    background-color: red;
    position: fixed;
    bottom: 0;
    z-index: 1002;
    width: calc(100vw - (100vw - 100%));
}

.close-pos {
    margin-right: 12px;
}

.ad-vertical {
    height: 720px;
    background-color: red;
    position: fixed;
    right: 0;
    top: 125px;
    z-index: 1002;
    width: 95px;
}

/*print-section*/
.tab-display {
    display: none;
}

.axis {
    padding: 0 5px;
}

.day-of-week {
    display: none;
}

/*.image-div img{
    width: 100%;
    height:auto;
}*/
.second-header-button {
    border: 1px solid #DB792F;
    border-radius: 12px;
    color: #DB792F;
    font-size: 17px;
    font-weight: 100;
    margin-left: 5px;

    font-family: 'Raleway', sans-serif;
}

.second-header-button:hover {
    background-color: #DB792F;
    color: white;
}

.second-header-save-button {
    margin: 5px;
    padding-top: 3px;
    border: 1px solid #CA2F35;
    border-radius: 14px;
    color: #CA2F35;
    font-weight: 100;
    font-size: 13px;
    width: 110px;
    font-family: 'Raleway', sans-serif;
    float: right;
    margin-top: 14px;
    margin-right: 100px;
}

.second-header-save-button:hover {
    background-color: #CA2F35;
    color: white;
}

.save-custom-menu-sm {

}

.toggle-sm {
    display: none;
}

.user-week {
    float: left;
    left: 50%;
    top: 7px;
    padding-top: 8px;
    color: #666665;

}

.print-btns {

    float: left;
    width: 25%;
    margin-top: 5px;
}

.print-btns-sm {
    display: none;
}

.print-btns-custom-sm {
    display: none;
}

.menu-container {
    display: flex;
    flex-direction: row;
    background-color: white;
    border-radius: 12px;
    margin-left: 70px;
    padding-top: 110px;
    z-index: -1;
}

.tab-font {
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 500;
    font-size: 13px;
    line-height: 21px;
    color: #BABABA;
}

.tab-font-active {
    color: black;
}

.left-arrow {
    margin-bottom: 5px;
}

.right-arrow {
    margin-bottom: 5px;
}

.menu-container-sm {
    display: flex;
    flex-direction: row;
    background-color: white;
    border-radius: 12px;
    margin-left: 70px;
    padding-top: 5.3em;
    z-index: -1;
    display: none;
}

.bar {
    width: auto;
    border: 1.31923px solid rgba(208, 220, 226, 0.5);
}

.column-container {
    padding-top: 3px;
    /*width: calc(20vw - 88px);*/
    width: calc(20vw - 88px);
}

.heart-active {
    color: red;
    font-size: 22px;
    position: relative;
    top: -22px;
}

.data-column-common {
    background-color: white;
    height: auto;
    /*width: 168px;  */
    width: calc(20vw - 88px);
    margin-top: 1em;
    padding-left: 3px;
    border: 1px solid #D3D3D380;
    display: block;
    margin-bottom: 40px;
    padding-bottom: 10px;
}

.minor-pad {
    padding-left: 1px;
}

.diet-box-container {
    padding: 6px 2px 8px 2px;
}

.diet-img {
    width: 30px;
    height: 30px;
}

.font-day {
    font-size: 10px;
    color: #666665;
    font-weight: 500;
}

.dropdown {
    position: relative;
    /*display: inline-block;*/
    box-shadow: 0px 2.22907px 36.2224px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    height: 2.4em;
    width: 100%;
    font-size: 11px;
    font-family: 'Raleway', sans-serif;
    font-weight: 500;
    right: 1.5px;
}

.toggle-adjust {
    float: right;
    padding-right: 13px;
}

.dropdown-content {
    /*width: 159px;*/
    position: relative;
    width: 100%;
    height: 290px;
    /*height: auto;*/
    box-shadow: 0px 5px 11px 0px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #DDE7EB;
    margin-bottom: 5px;
    display: block;
    right: 2px;
}

.dropdown-content:hover {
    box-shadow: 0px 5px 11px 0px rgba(0, 0, 0, 0.3);
}

.dropdown-menu-active {
    border-radius: 6px 6px 0px 0px;
    background-color: #DDE7EB;
}

.food-type {
    background-color: #CA2F35;
    color: white;
    font-size: 9px;
    border-radius: 3px;
    padding: 5px 22px;
    font-family: 'Raleway', sans-serif;
    cursor: pointer;
}

.food-type-desk {
    background-color: #CA2F35;
    color: white;
    font-size: 22px;
    border-radius: 3px;
    padding: 5px 22px;
    font-family: 'Raleway', sans-serif;
    cursor: pointer;
}

.diet-container {
    /*padding: 5px;*/
}

/*tooltip form print-section-icons*/
.mytooltip {
    position: relative;
    display: inline-block;
}

.mytooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #7B7B7B;
    font-family: 'Raleway';
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 0;
    position: absolute;
    z-index: 1;
    top: 125%;
    left: 70%;
    margin-left: -60px;
}

.mytooltip .tooltiptext::after {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    margin-left: -8px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent #7B7B7B transparent;
}

.mytooltip:hover .tooltiptext {
    visibility: visible;
}

.box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    position: absolute;
    bottom: 0;
    left: 0;
}

.box > * {
    /*flex: 0 0 33.3333%;*/
    flex: 0 0 auto;
}

.diet-type {
    font-family: 'Raleway';
    text-align: center;
    color: #DB792F;
    /*width: 110px;*/
    width: auto;
    height: 17px;
    font-size: 9px;
    background: rgba(219, 121, 47, 0.1);
    border: 0.5px solid rgba(219, 121, 47, 0.8);
    border-radius: 2px;
    padding: 2px 8px 11px 8px;
    cursor: default;
}

.diet-box-container {
    padding-left: 3px;
}

.food-date-sm {
    display: none;
}

.food-date {
    font-family: 'Oswald';
    font-weight: 600;
}

.axis {
    font-family: 'Oswald';
    font-size: 18px;
}

.dish-name {
    font-family: 'Lato';
    font-weight: 600;
    font-size: 12px;
    line-height: 10px;
}

.pointer {
    cursor: pointer;
}

.menu-header {
    display: none;
}

.splash {
    height: 1100px;
}

.food-cat {
    padding-left: calc(55% - 80px);
}

.mobile-display {
    display: none;
}

.bottom-padding-sm {
    display: none;
}

.dropdowned {
    border-bottom: 1.8px solid #DB792F;
    border-radius: 4px;
    width: 55px;
    height: 30px;
    padding-left: 4px;
    position: relative;
    display: inline-block;
}

.dropdowned-01 {
    border-bottom: 1.8px solid #DB792F;
    border-radius: 4px;
    width: 90px;
    height: 30px;
    padding-left: 4px;
    position: relative;
    display: inline-block;
}

.dropdown-content-2 {
    display: none;
    position: absolute;
    top: 34px;
    left: -6px;
    background-color: #f1f1f1;
    border-radius: 8px;
    min-width: 160px;
    overflow: auto;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.dropdown-content-2 a {
    color: black;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}

.dropdown-2 a:hover {
    background-color: #ddd;
}


*, *:before, *:after {
    box-sizing: inherit;
}

.nutrition {
    margin: 20px 0 0 39px;
    font-family: sans-serif;
    font-size: 0.6rem;
    line-height: 1.279;
    max-width: 200px;
    padding: 0.5rem;
    border: 1px solid;
}

.nutrition-see {
    margin: 20px 0 0px 39px;
    font-family: sans-serif;
    font-size: 1.2rem;
    line-height: 1.479;
    max-width: 300px;
    padding: 0.5rem;
    border: 1px solid;
}

.macros-header {
    font-family: 'Raleway';
    height: 50px;
    width: auto;
    background-color: rgba(219, 121, 47, 1);
}

.macros-header-close {
    float: right;
    padding: 25px;
}

.macros-header-text {
    position: absolute;
    top: 3%;
    left: 20%;
    color: white;
}

.macros {
    height: 430px;
    z-index: 4000;
    width: 280px;
    background-color: white;
    top: 30%;
    left: 40%;
    position: absolute;
    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.12);
}

.backdrop-fade {
    z-index: 3999;
}

.nutrition-title {
    font-size: 20px;
    font-family: inherit;
    font-weight: bold;
    line-height: 1.3;
    text-transform: none;
    margin-top: 0;
    margin-bottom: 0;
}

.nutrition-facts {
    border-top: 10px solid;
    border-bottom: 1px solid;
}

.nutrition-facts,
.nutrition-facts ul {
    list-style: none;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;
}

.nutrition-facts ul {
    flex: 0 0 100%;
}

.nutrition-facts ul li {
    margin-left: 1rem;
    margin-right: -0.25rem;
}

.nutrition-facts li {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-left: 0.25rem;
    padding-right: 0.25rem;
    border-top: 1px solid;
}

.nutrition-facts .nutrition-facts-section {
    border-top-width: 5px;
}

.nutrition-facts-label {
    font-weight: bold;
}

.nutrition-note {
    padding-top: 5px;
    font-size: 90%;
    font-family: 'Raleway'
}

/*see-recipe*/
.see-recipe {
    position: fixed;
    width: 800px;
    height: 500px;
    background-color: white;
    margin-top: 40px;
    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.5);
    /*top: 30%;*/
    top: expression(window.scrollTop + "px");
    left: 20%;
    z-index: 1;
    overflow-y: scroll;
    border-radius: 10px;
}

.see-img {
    height: 485px;
    padding: 10px 0px 5px 20px;
    display: flex;
    margin-top: 45px;
    justify-content: space-around;
    background-color: #eeeaea;
    margin: 50px 20px 10px 20px;
    z-index: 1003;

}

.tdbuttoncover {

}

.modal-img {
    object-fit: cover;
    height: 320px;
    width: 320px;
    border-radius: 50%;
    position: absolute;
    top: 20px;
    z-index: 1004;
    box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.5);
}

.see-recipe-name {
    font-family: playfair;
    font-size: 28px;
    position: relative;
    top: 14px;
}

.group-pill {
    padding: 1px 8px 4px 8px;
    background: #F1F1F1;
    /*background: blue;*/
    border-radius: 10px;
}

.see-header {
    margin-top: -220px;
    margin-left: 30px;
}

.see-footer {
    display: flex;
    justify-content: center;
    padding: 40px 20px 0px 40px;
}

.see-info {
    padding-top: 20px;
}

.see-recipe-button {
    border: 1px solid #DB792F;
    border-radius: 12px;
    background-color: white;
    color: rgba(202, 47, 53, 1);
    font-size: 13px;
    width: 130px;
    height: 32px;
    font-family: 'Raleway', sans-serif;
}

.see-recipe-button:hover {
    background-color: rgba(202, 47, 53, 1);
    color: white;
}

.ing-style {
    font-family: Raleway;
}

.see-recipe-ing {
    font-weight: 500;
    font-size: 22px;
    padding: 20px 0;
}

.see-recipe-ins {
    font-weight: 500;
    font-size: 22px;
    margin: 30px 0 0 0;
}

.see-recipe-btn-pos {
    position: relative;
    float: right;
    top: -65px;
    right: 55px;
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
    /*box-shadow: inset 0 0 5px grey;
        border-radius: 10px;*/
    background: #D7D7D7;
    /*box-shadow: inset 4px 4px 13px rgba(0, 0, 0, 0.14);*/
    border-radius: 10px;
    transform: rotate(90deg);
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #E5DFC3;
    border-radius: 10px;
    transform: rotate(90deg);
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #DB792F;
}

/*common for sm*/
.controls-sm {
    position: relative;
    float: right;
    top: 0px;
}

.controls-bar {
    width: calc(100vw - 46px);
    height: 1px;
    background-color: rgba(0, 0, 0, 0.05);
    position: absolute;
    top: 45px;
    left: 0px;
}

.see-img-sm {
    margin-top: 25px;
    height: 245px;
}

.modal-image {
    width: 100%;
    height: 100%;
}

/*see-recipe header*/
.outer-wrapper {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 65px;
    width: calc(100% - 26vw);
    background-color: var(--lightAccentYellow);
    z-index: 1001;
}

.outer-wrapper .twenty-dishes-logo img {
    height: 50px;
    padding-left: 58px;
    background-color: var(--lightAccentYellow);
}

.outer-wrapper .login-display {
    position: relative;
    float: right;
    padding-right: 40px;
    background-color: var(--lightAccentYellow);
}

.see-recipe-sidebar {
}

.dropdown-content-empty-sm {
    position: relative;
    background-color: white;
    margin-top: 20px;
    margin-left: 13px;
    margin-bottom: 15px;
    border-radius: 5px;
    width: calc(100vw - 14%);
    height: 220px;
}

.dropdown-content-empty-tab {
    position: relative;
    background-color: white;
    margin-top: 20px;
    margin-left: 0px;
    margin-bottom: 15px;
    border-radius: 5px;
    width: 250px;
    height: 220px;
}

/*Media queries*/
@media only screen and (max-width: 375px) and (min-width: 100px) {
    .main-display {
        display: none;
    }

    .mobile-display {
        display: block;
    }

    .tab-display {
        display: none;
    }

    .load {
        display: block;
    }

    .splash {
        display: none;
    }

    .menu-wrapper-sm {
        position: relative;
        top: 15px;
        left: 8px;
        display: block;
        background: rgba(217, 217, 217, 0.19);
        border: 0.735164px solid rgba(170, 194, 206, 0.5);
        border-radius: 7.35164px;
        height: auto;
        width: calc(105vw - 10%);
        /*width: 302px; */
    }

    .date-section-sm {
        position: relative;
    }

    .recipe-date-sm {
        font-family: 'Raleway', sans-serif;
        float: left;
        padding: 20px 10px 0 10px;
    }

    .meal-tabs-sm {
        margin-top: 70px;
    }

    .date-controls-sm {
        float: right;
        padding: 20px 8px 0 8px;
    }

    .tab-bar-sm {
        margin-top: 5px;
    }

    .Breakfast-bar-active {
        position: absolute;
        width: 60px;
        left: 12px;
        border: 1.83846px solid #CA2F35;
    }

    .Lunch-bar-active {
        position: absolute;
        width: 43px;
        left: 85px;
        border: 1.83846px solid #CA2F35;
    }

    .Dinner-bar-active {
        position: absolute;
        width: 44px;
        left: 160px;
        border: 1.83846px solid #CA2F35;
    }

    .Snacks-bar-active {
        position: absolute;
        width: 54px;
        left: 235px;
        border: 1.83846px solid #CA2F35;
    }

    .second-header-save-button {
        font-size: 10px;
        width: 78px;
        padding: 3px;
        float: right;
        margin-top: 12px;
        margin-right: 20px;
    }

    .save-custom-menu-sm {
        width: 175px;
    }

    .menu-header {
        display: block;
    }

    .day-of-week {
        position: absolute;
        display: block;
        left: 122px;
        top: 17px;
        font-size: 14px;
    }

    .day-of-week .up-arrow {
        position: absolute;
        top: 0px;
    }

    .day-of-week .down-arrow {
        position: absolute;
        top: 12px;
    }

    .right-arrow {
        display: none;
    }

    .left-arrow {
        display: none;
    }

    .user-week {
        position: absolute;
        padding: 0 2px;
        top: 17px;
        left: 10px;
        font-size: 14px
    }

    .toggle-sm {
        display: block;
    }

    .toggle-pos {
        position: absolute;
        float: right;
        margin-left: calc(20% - 20px);
    }

    .outer-wrapper .twenty-dishes-logo img {
        padding-left: 5px;
    }

    .print-btns-sm {
        display: flex;
        flex-direction: row;
        justify-content: center;
        padding: 150px 0 0 0px;
        margin-left: -12px;
        width: calc(100%);
    }

    .print-btns-sm .print-icon {
        height: 60px;
        width: 60px;
    }

    .print-btns-custom-sm {
        display: block;
    }

    .second-header-button {
        background-color: white;
        padding: 0 8px;
        margin-bottom: 10px;
        min-width: 190px;
    }

    .recipe-image {
        width: 260px;
        height: 130px;
    }

    .font-day {
        font-size: 15px;
        font-weight: 500;
        display: inline;
    }

    .food-days-main {
        padding: 0;
        margin-top: 10px;
    }

    .food-date {
        display: none;
    }

    .food-date-sm {
        display: inline;
        font-size: 25px;
        padding-right: 10px;
    }

    .food-type {
        font-size: 15px;
    }

    .diet-type {
        font-size: 12px;
        padding: 2px 13px 20px 13px;
    }

    .diet-box-container {
        padding: 0px 3px 17px 5px;
    }

    .diet-img {
        height: 45px;
        width: 45px;
    }

    .diet-row {
        padding-top: 8px;
    }

    .dish-name {
        font-size: 16px;
    }

    .dish-div {
        margin-top: 40px;
    }

    .image-div {
        margin-top: 10px;
    }

    .image-div img {
        /*width: 255px;*/
        width: 100%;
        height: auto;
    }

    .dropdown-content-sm {
        position: relative;
        background-color: white;
        margin-top: 20px;
        margin-left: 10px;
        margin-bottom: 15px;
        border-radius: 5px;
        width: calc(100vw - 14%);
        height: 480px;
    }

    .second-header {
        margin: 0;
    }

    .print-btns {
        display: none;
    }

    .my-footer {
        display: none;
    }

    .save-plan {
        margin-right: 40px;
    }

    .bottom-padding-sm {
        display: block;
        height: 90px;
    }

    .axis {
        display: none;
    }

    .see-recipe {
        width: calc(100vw - 20px);
        height: 550px;
        margin-top: 100px;
        left: 3%;
        top: 5%;
    }

    .see-recipe-btn-pos {
        top: -25px;
        right: 28px;
    }

    .see-recipe-name {
        font-size: 20px;
        top: -10px;
    }

    .see-recipe-button {
        width: 90px;
        height: 25px;
        font-size: 12px;
    }

    .see-info {
        padding-top: 5px;
        padding-bottom: 10px;
        font-size: 15px;
    }

    .padding-left {
        position: relative;
        left: -34px;
    }

    .nutrition-see {
        width: 260px;
    }

    .macros {
        left: 19px;
        top: 37%;
    }

    .food-type-desk {
        font-size: 16px;

    }

    .see-header {
        padding: 10px 25px 0 0;
        margin-top: -178px;
    }

    .see-footer {
        padding-top: 15px;
        padding-bottom: 20px;
        padding-left: 20px;
    }

    .see-img {
        padding: 15px 12px 10px 12px;
        height: 75%;
    }

    .modal-img {
        height: 180px;
        width: 180px;
        top: 5px;
    }

}

@media only screen and (max-width: 374px) and (min-width: 340px) {
    .second-header-save-button {
        font-size: 10px;
        width: 78px;
        padding: 3px;
        float: right;
        margin-top: 12px;
        margin-right: 20px;
    }

    .diet-box-container {
        padding: 0px 3px 18px 5px;
    }

    .diet-img {
        height: 47px;
        width: 47px;
    }

    .save-custom-menu-sm {
        width: 175px;
    }

    .Snacks-bar-active {
        left: 260px;
    }

    .Dinner-bar-active {
        left: 180px;
    }

    .Lunch-bar-active {
        left: 98px;
    }

    .diet-pane {
        left: 240px;
    }

    .macros {
        left: 35px;
        top: 37%;
    }
}

@media only screen and (max-width: 425px) and (min-width: 375px) {
    .main-display {
        display: none;
    }

    .tab-display {
        display: none;
    }

    .bottom-padding-sm {
        display: block;
        height: 80px;
    }

    .mobile-display {
        display: block;
    }

    .load {
        display: block;
    }

    .splash {
        display: none;
    }

    .see-img-sm {
        height: 270px;
    }

    .menu-wrapper-sm {
        position: relative;
        top: 15px;
        left: 8px;
        display: block;
        background: rgba(217, 217, 217, 0.18);
        border: 0.735164px solid rgba(170, 194, 206, 0.5);
        border-radius: 7.35164px;
        height: auto;
        width: calc(107vw - 11%);
    }

    .date-section-sm {
        position: relative;
    }

    .recipe-date-sm {
        font-family: 'Raleway', sans-serif;
        float: left;
        padding: 20px 10px 0 10px;
    }

    .meal-tabs-sm {
        margin-top: 70px;
    }

    .backdrop-adjust {
        height: 11000px;
    }

    .date-controls-sm {
        float: right;
        padding: 20px 8px 0 8px;
    }

    .tab-bar-sm {
        margin-top: 5px;
    }

    .Breakfast-bar-active {
        position: absolute;
        width: 60px;
        left: 12px;
        border: 1.83846px solid #CA2F35;
    }

    .Lunch-bar-active {
        position: absolute;
        width: 43px;
        left: 100px;
        border: 1.83846px solid #CA2F35;
    }

    .Dinner-bar-active {
        position: absolute;
        width: 44px;
        left: 192px;
        border: 1.83846px solid #CA2F35;
    }

    .Snacks-bar-active {
        position: absolute;
        width: 50px;
        left: 282px;
        border: 1.83846px solid #CA2F35;
    }

    .second-header-save-button {
        font-size: 10px;
        width: 78px;
        padding: 3px;
        float: right;
        margin-top: 12px;
        margin-right: 20px;
    }

    .menu-header {
        display: block;
    }

    .day-of-week {
        position: absolute;
        display: block;
        left: 122px;
        top: 17px;
        font-size: 14px;
    }

    .day-of-week .up-arrow {
        position: absolute;
        top: 0px;
    }

    .day-of-week .down-arrow {
        position: absolute;
        top: 12px;
    }

    .right-arrow {
        display: none;
    }

    .left-arrow {
        display: none;
    }

    .user-week {
        position: absolute;
        padding: 0 2px;
        top: 17px;
        left: 10px;
        font-size: 14px
    }

    .toggle-sm {
        display: block;
    }

    .outer-wrapper .twenty-dishes-logo img {
        padding-left: 5px;
    }

    .print-btns-sm {
        display: flex;
        flex-direction: row;
        justify-content: center;
        padding: 150px 0 10px 0px;
        margin-left: -17px;
        width: calc(100%);
    }

    .print-btns-sm .print-icon {
        height: 60px;
        width: 60px;
    }

    .print-btns-custom-sm {
        display: block;
    }

    .second-header-button {
        background-color: white;
        padding: 0 8px;
        margin-bottom: 10px;
        min-width: 190px;
    }

    .save-custom-menu-sm {
        position: relative;
        width: 175px;
        bottom: 30px;
    }

    .recipe-image {
        width: 260px;
        height: 130px;
    }

    .font-day {
        font-size: 15px;
        font-weight: 500;
        display: inline;
    }

    .food-days-main {
        padding: 0;
        margin-top: 10px;
    }

    .food-date {
        display: none;
    }

    .food-date-sm {
        display: inline;
        font-size: 25px;
        padding-right: 10px;
    }

    .food-type {
        font-size: 15px;
    }

    .diet-type {
        font-size: 13px;
        padding: 3px 18px 22px 18px;
    }

    .diet-box-container {
        padding: 10px 3px 20px 8px;
    }

    .diet-img {
        height: 42px;
        width: 42px;
    }

    .diet-row {
        padding-top: 8px;
    }

    .diet-pane {
        position: absolute;
        left: 280px;
    }

    .diet-close {
        position: absolute;
        left: 260px;
    }

    .dish-name {
        font-size: 16px;
    }

    .dish-div {
        margin-top: 40px;
    }

    .image-div {
        margin-top: 10px;
    }

    .image-div img {
        /* width: 310px;
        height:150px;*/
        width: 100%;
        height: auto;
    }

    .dropdown-content-sm {
        position: relative;
        background-color: white;
        margin-top: 20px;
        margin-left: 10px;
        margin-bottom: 15px;
        border-radius: 5px;
        width: calc(100vw - 11%);
        /*width: 340px;*/
        height: 510px;
    }

    .second-header {
        margin: 0;
    }

    .print-btns {
        display: none;
    }

    .my-footer {
        display: none;
    }

    .save-plan {
        margin-right: 40px;
    }

    .axis {
        display: none;
    }

    .see-recipe {
        width: calc(100vw - 20px);
        height: 550px;
        margin-top: 100px;
        left: 3%;
        top: 5%;
    }

    .see-recipe-btn-pos {
        top: -25px;
        right: 28px;
    }

    .see-img {
        padding: 15px 12px 10px 12px;
        height: 90%;
    }

    /*.modal-img{
        height: 210px;
        width: 210px;
    }*/
    .see-recipe-name {
        font-size: 25px;
        top: -18px;
    }

    .see-recipe-button {
        width: 90px;
        height: 25px;
        font-size: 12px;
    }

    .food-type-desk {
        font-size: 18px;
    }

    .see-footer {
        padding-top: 15px;
        padding-bottom: 85px;
        padding-left: 20px;
    }

    .see-info {
        margin-top: -15px;
        padding-top: 0px;
        padding-bottom: 10px;
        font-size: 15px;
    }

    .see-header {
        padding: 0 30px 0 0;
        margin-top: -200px;
    }


    .padding-left {
        position: relative;
        left: -26px;
    }

    .nutrition-see {
        width: 310px;
    }

    .macros {
        left: 49px;
        top: 37%;
    }

    .modal-img {
        height: 250px;
        width: 250px;
    }
}

@media only screen and (max-width: 424px) and (min-width: 395px) {
    .second-header-save-button {
        font-size: 10px;
        width: 78px;
        padding: 3px;
        float: right;
        margin-top: 12px;
        margin-right: 20px;
    }

    .save-custom-menu-sm {
        width: 175px;
    }

    .Snacks-bar-active {
        left: 305px;
    }

    .Dinner-bar-active {
        left: 207px;
    }

    .Lunch-bar-active {
        left: 110px;
    }

    .diet-img {
        height: 42px;
        width: 42px;
    }

    .diet-pane {
        left: 300px;
    }

    .see-img-sm {
        height: 290px;
    }

    .dropdown-content-sm {
        height: 540px;
    }

    .macros {
        left: calc(15% - 20px);
        top: 30%;
        height: 550px;
        width: 327px;
    }

    .nutrition {
        line-height: 1.5;
        font-size: 12px;
        max-width: 250px;
    }
}

@media only screen and (max-width: 600px) and (min-width: 426px) {
    .main-display {
        display: none;
    }

    .mobile-display {
        display: block;
    }

    .print-btns {
        display: none;
    }

    .see-img-sm {
        height: 300px;
    }

    .tab-display {
        display: none;
    }

    .load {
        display: block;
    }

    .splash {
        display: none;
    }

    .menu-wrapper-sm {
        position: relative;
        top: 15px;
        left: 8px;
        display: block;
        background: rgba(217, 217, 217, 0.18);
        border: 0.735164px solid rgba(170, 194, 206, 0.5);
        border-radius: 7.35164px;
        height: auto;
        width: calc(106vw - 10%);
        /*width: 407px;*/
    }

    .date-section-sm {
        position: relative;
    }

    .recipe-date-sm {
        font-family: 'Raleway', sans-serif;
        float: left;
        padding: 20px 10px 0 10px;
    }

    .meal-tabs-sm {
        margin-top: 70px;
    }

    .date-controls-sm {
        float: right;
        padding: 20px 8px 0 8px;
    }

    .tab-bar-sm {
        margin-top: 5px;
    }

    .Breakfast-bar-active {
        position: absolute;
        width: 60px;
        left: 12px;
        border: 1.83846px solid #CA2F35;
    }

    .Lunch-bar-active {
        position: absolute;
        width: 41px;
        left: 113px;
        border: 1.83846px solid #CA2F35;
    }

    .Dinner-bar-active {
        position: absolute;
        width: 44px;
        left: 213px;
        border: 1.83846px solid #CA2F35;
    }

    .Snacks-bar-active {
        position: absolute;
        width: 47px;
        left: 315px;
        border: 1.83846px solid #CA2F35;
    }

    .second-header-save-button {
        font-size: 10px;
        width: 78px;
        padding: 3px;
        float: right;
        margin-top: 12px;
        margin-right: 20px;
    }

    .save-custom-menu-sm {
        width: 175px;
    }

    .menu-header {
        display: block;
    }

    .day-of-week {
        position: absolute;
        display: block;
        left: 122px;
        top: 17px;
        font-size: 14px;
    }

    .day-of-week .up-arrow {
        position: absolute;
        top: 0px;
    }

    .day-of-week .down-arrow {
        position: absolute;
        top: 12px;
    }

    .right-arrow {
        display: none;
    }

    .left-arrow {
        display: none;
    }

    .user-week {
        position: absolute;
        padding: 0 2px;
        top: 17px;
        left: 10px;
        font-size: 14px;
    }

    .toggle-sm {
        display: block;
    }

    .toggle-pos {
        position: absolute;
        float: right;
        margin-left: calc(20% - 20px);
    }

    .outer-wrapper .twenty-dishes-logo img {
        padding-left: 5px;
    }

    .print-btns-sm {
        display: flex;
        flex-direction: row;
        justify-content: center;
        padding: 150px 0 10px 0px;
        margin-left: -20px;
        width: calc(100%);
    }

    .print-btns-sm .print-icon {
        height: 60px;
        width: 60px;
    }

    .print-btns-custom-sm {
        display: block;
    }

    .second-header-button {
        background-color: white;
    }

    .recipe-image {
        width: 100%;
        height: auto;
    }

    .font-day {
        font-size: 15px;
        font-weight: 500;
        display: inline;
    }

    .food-days-main {
        padding: 0;
        margin-top: 10px;
    }

    .food-date {
        display: none;
    }

    .food-date-sm {
        display: inline;
        font-size: 25px;
        padding-right: 10px;
    }

    .food-type {
        font-size: 15px;
    }

    .diet-type {
        font-size: 13px;
        padding: 3px 26px 22px 26px;
    }

    .diet-box-container {
        padding: 10px 3px 20px 9px;
    }

    .diet-img {
        height: 48px;
        width: 48px;
    }

    .diet-row {
        padding-top: 8px;
    }

    .diet-pane {
        position: relative;
        left: 95px;
    }

    .diet-close {
        position: relative;
        right: 0px;
        padding-left: 60px;
    }

    .dish-name {
        font-size: 16px;
    }

    .dish-div {
        margin-top: 40px;
    }

    .image-div {
        margin-top: 12px;
    }

    .image-div img {
        /*width: 340px;
        height:150px;*/
        width: 100%;
        height: auto;
    }

    .dropdown-content-sm {
        position: relative;
        background-color: white;
        margin-top: 20px;
        margin-left: 10px;
        margin-bottom: 15px;
        border-radius: 5px;
        width: calc(100vw - 10%);
        height: 560px;
    }

    .second-header {
        margin: 0;
    }

    .my-footer {
        display: none;
    }

    .save-plan {
        margin-right: 40px;
    }

    .bottom-padding-sm {
        display: block;
        height: 90px;
    }

    .axis {
        display: none;
    }

    .see-recipe {
        width: calc(100vw - 20px);
        height: 550px;
        margin-top: 100px;
        left: 2%;
        top: 4%;
    }

    .see-recipe-btn-pos {
        top: -25px;
        right: 28px;
    }

    .see-recipe-name {
        font-size: 25px;
    }

    .see-recipe-button {
        width: 90px;
        height: 25px;
        font-size: 12px;
    }

    .see-info {
        padding-top: 12px;
        padding-bottom: 10px;
        font-size: 15px;
    }

    .modal-img {
        height: 280px;
        width: 280px;
    }

    .see-header {
        padding: 0 30px 0 0;
        margin-top: -240px;
    }

    .see-footer {
        margin-top: 10px;
        margin-bottom: 50px;
        padding-left: 30px;
    }

    .see-img {
        padding-right: 10px;
    }

    .padding-left {
        position: relative;
        left: -1px;
    }

    .food-type-desk {
    }

    .nutrition-see {
        width: 350px;
    }

    .macros {
        left: 18%;
        top: 37%;
    }

    .second-header-button {
        min-width: 220px;
    }
}

@media only screen and (max-width: 767px) and (min-width: 490px) {
    .second-header-save-button {
        font-size: 10px;
        width: 78px;
        padding: 3px;
        float: right;
        margin-top: 12px;
        margin-right: 20px;
    }

    .save-custom-menu-sm {
        width: 175px;
    }

    .Lunch-bar-active {
        left: 140px;
    }

    .Dinner-bar-active {
        left: 268px;
    }

    .Snacks-bar-active {
        left: 400px;
    }

    .second-header-button {
        padding: 3px 42px;
    }
}

@media only screen and (max-width: 1024px) and (min-width: 768px) {
    .main-display {
        display: none;
    }

    .pad-top {
        margin-top: 50px;
    }

    .mobile-display {
        display: none;
    }

    .mytooltip .tooltiptext {
        left: 80%;
    }

    .print-icon {
        height: 40px;
        width: 40px;
    }

    .second-header-button {
        font-size: 14px;
        position: relative;
        top: -15px;
    }

    .user-week {
        position: relative;
        left: -160px;
        top: 0px;
    }

    .my-footer {
        display: block;
    }

    .load {
        display: block;
    }

    .tab-display {
        display: block;
    }

    .diet-row {
        margin-top: 12px;
    }

    .dropdown-content-sm {
        background-color: white;
        height: 400px;
    }

    .dish-div {
        margin-top: 45px;
    }

    .controls-sm {
        margin-top: -32px;
    }

    .box {
        position: relative;
        top: 20px;
    }

    .menu-wrapper-sm {
        position: relative;
        top: 45px;
        left: 8px;
        display: block;
        background: rgba(217, 217, 217, 0.12);
        border: 0.735164px solid rgba(170, 194, 206, 0.5);
        border-radius: 1.05164px;
        height: fit-content;
        width: 265px;
    }

    .date-section-sm {
        position: relative;
    }

    .bottom-padding-sm {
        display: block;
        height: 200px;
    }

    .recipe-date-sm {
        font-family: 'Raleway', sans-serif;
        float: left;
        padding: 20px 10px 0 10px;
    }

    .meal-tabs-sm {
        margin-top: 70px;
    }

    .image-div {
        margin-top: 10px;
    }

    .image-div img {
        width: 225px;
        height: 140px;
    }

    .date-controls-sm {
        float: right;
        padding: 20px 8px 0 8px;
    }

    .tab-font {
        font-size: 12px;
        cursor: pointer;
    }

    .tab-bar-sm {
        margin-top: 5px;
    }

    .Breakfast-bar-active {
        position: absolute;
        width: 55px;
        left: 12px;
        border: 1.34px solid #CA2F35;
    }

    .Lunch-bar-active {
        position: absolute;
        width: 38px;
        left: 73px;
        border: 1.34px solid #CA2F35;
    }

    .Dinner-bar-active {
        position: absolute;
        width: 38px;
        left: 135px;
        border: 1.34px solid #CA2F35;
    }

    .Snacks-bar-active {
        position: absolute;
        width: 43px;
        left: 198px;
        border: 1.34px solid #CA2F35;
    }

    .menu-container {
        overflow-x: auto;
        height: 700px;
    }

    .second-header-button {
        font-size: 11px;
    }

    .print-btns {
        width: 60%;
    }

    .second-header-save-button {
        font-size: 13px;
        position: relative;
        top: -42px;
        left: 20px;
    }

    .save-plan {
        float: right;
        width: 25%;
    }

    .second-header {
        height: 50px;
    }

    .bottom-padding-sm {
        height: 55vh;
    }

    .diet-type {
        font-size: 11px;
        padding: 1px 12px 17px 12px;
    }

    .diet-box-container {
        padding: 15px 3px 0px 5px;
    }

    .diet-img {
        height: 43px;
        width: 43px;
    }

    /*scroll-bar*/
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        /*box-shadow: inset 0 0 5px grey;
        border-radius: 10px;*/
        background: #D7D7D7;
        box-shadow: inset 4px 4px 13px rgba(0, 0, 0, 0.14);
        border-radius: 10px;
        transform: rotate(90deg);
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #E5DFC3;
        border-radius: 10px;
        transform: rotate(90deg);
    }

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: #DB792F;
    }

    .see-recipe {
        width: calc(85vw - 80px);
        height: 560px;
        margin-top: 100px;
        left: 15%;
    }

    .see-recipe-btn-pos {
        top: -25px;
    }
}

@media only screen and (max-width: 1024px) and (min-width: 902px) {
    .second-header-save-button {
        top: -5px;
    }
}

@media only screen and (max-width: 1099px) and (min-width: 1025px) {
    .column-container {
        padding-top: 2px;
        /*width: calc(18.2vw - 100px);*/
        width: calc(22.1vw - 100px);
    }

    .print-btns {
        width: 50%;
    }

    .print-icon {
        height: 42px;
        width: 42px;
        margin-top: 2px;
    }

    .diet-box-container {
        padding-bottom: 12px;
    }

    .diet-img {
        height: 23px;
        width: 23px;
    }

    .second-header-button {
        font-size: 14px;
        position: relative;
        top: -5px;
    }

    .user-week {
        position: relative;
        left: -75px;
    }

    .data-column-common {
        /*width: calc(18.25vw - 100px);*/
        width: calc(22.1vw - 100px);
        height: auto;
    }

    .pad-top {
        margin-top: 40px;
    }

    .see-recipe {
        top: 90px;
        width: 650px;
    }

    .dropdown-content {
        height: 340px;
    }

    .second-header-button {
        font-size: 11px;
    }

    /* .diet-pane {
         position: absolute;
         top: 0px;
         left: 75px;
     }
 
     .diet-close {
         position: absolute;
         top: 0px;
         right: 10px;
     }
 
     .diet-fav {
         position: relative;
         top: 5px;
         left: -3px;
     }*/
    .diet-pane {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 0px;
    }

    .recipe-image {
        height: 100px;
        width: 100px;
    }

    .diet-row {
        display: flex !important;
        justify-content: right;
        flex-wrap: nowrap !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        --bs-gutter-x: 1.2rem;
        width: 100%;
    }

    .diet-row svg {
        height: 12px;
        width: 12px;
    }
}

@media only screen and (max-width: 1199px) and (min-width: 1100px) {
    .second-header-save-button {
        font-size: 13px;
        float: right;
        margin-top: 8px;
        margin-right: 90px;
    }

    .print-icon {
        height: 42px;
        width: 42px;
        margin-top: 2px;
    }

    .pad-top {
        margin-top: 40px;
    }

    .column-container {
        padding-top: 2px;
        width: calc(21.4vw - 100px);
    }

    .diet-box-container {
        padding-bottom: 12px;
    }

    .diet-img {
        height: 24px;
        width: 24px;
    }

    .data-column-common {
        width: calc(21.4vw - 100px);
        height: auto;
    }

    .second-header-button {
        font-size: 11px;
    }

    .recipe-image {
        height: 100px;
        width: 100px;
    }

    .diet-pane {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 0px;
    }

    .diet-row {
        display: flex !important;
        justify-content: right;
        flex-wrap: nowrap !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        --bs-gutter-x: 1.2rem;
        width: 100%;
    }

    .diet-row svg {
        height: 12px;
        width: 12px;
    }

    .see-recipe {
        width: 750px;
        top: 8%;
        z-index: 1005;
        height: 600px;
    }

    .print-btns {
        width: 40%;
    }

    .user-week {
        position: relative;
        font-size: 12px;
        left: 5%;
    }

    .dropdown-content {
        height: 330px;
    }
}

@media only screen and (max-width: 1228px) and (min-width: 1200px) {
    .print-icon {
        height: 42px;
        width: 42px;
        margin-top: 2px;
    }

    .dropdown-content {
        height: 350px;
    }

    .diet-pane svg {
        height: 15px;
        width: 15px;
    }

    .pad-top {
        margin-top: 40px;
    }

    .see-recipe {
        top: 80px;
        height: 70vh;
    }

    .diet-close svg {
        height: 15px;
        width: 15px;
    }

    .diet-fav svg {
        height: 15px;
        width: 15px;
    }

    .print-btns {
        width: 50%;
    }

    .second-header-button {
        font-size: 14px;
        position: relative;
        top: -5px;
    }

    .user-week {
        position: relative;
        left: -75px;
    }

    .diet-box-container {
        padding-bottom: 12px;
    }

    .diet-pane {
        position: relative;
        top: 0px;
        left: 0px;
        margin-right: 3px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
        margin-right: 2px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 0px;
        margin-right: 4px;
    }

    .diet-row {
        display: flex !important;
        justify-content: right;
        flex-wrap: nowrap !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        --bs-gutter-x: 1.2rem;
        width: 100%;
    }
}

@media only screen and (max-width: 1280px) and (min-width: 1229px) {

    .dropdown-content {
        height: 350px;
    }

    .pad-top {
        margin-top: 40px;
    }

    .diet-pane svg {
        height: 15px;
        width: 15px;
    }

    .diet-close svg {
        height: 15px;
        width: 15px;
    }

    .diet-fav svg {
        height: 15px;
        width: 15px;
    }

    .see-recipe {
        top: 90px;
        padding-bottom: 200px;
    }

    .print-btns {
        width: 50%;
    }

    .second-header-button {
        font-size: 14px;
        position: relative;
        top: -5px;
    }

    .user-week {
        position: relative;
        left: -75px;
    }

    .diet-box-container {
        padding-bottom: 12px;
    }

    .print-icon {
        height: 43px;
        width: 43px;
        margin-top: 3px;
    }

    .diet-pane {
        position: relative;
        top: 0px;
        left: 0px;
        margin-right: 3px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
        margin-right: 2px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 0px;
        margin-right: 4px;
    }

    .diet-row {
        display: flex !important;
        justify-content: right;
        flex-wrap: nowrap !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        --bs-gutter-x: 1.2rem;
        width: 100%;
    }
}

@media only screen and (max-width: 1320px) and (min-width: 1281px) {
    .second-header-save-button {
        font-size: 13px;
        float: right;
        margin-top: 8px;
        margin-right: 90px;
    }

    .print-icon {
        height: 45px;
        width: 45px;
    }

    .column-container {
        padding-top: 2px;
        /*width: calc(18.2vw - 100px);*/
        width: calc(20.8vw - 100px);
    }

    .pad-top {
        margin-top: 40px;
    }

    .data-column-common {
        /*width: calc(18.25vw - 100px);*/
        width: calc(20.8vw - 100px);
        height: auto;
    }

    .second-header-button {
        font-size: 11px;
    }

    .diet-pane {
        position: relative;
        top: 0px;
        left: 0px;
        margin-right: 3px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
        margin-right: 2px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 0px;
        margin-right: 4px;
    }

    .dropdown-toggle {
        font-size: 16px;
        position: relative;
        bottom: 5px;
    }

    .plus-adj {
        margin: -17px 10px 0 0;
    }

    .diet-row {
        display: flex !important;
        justify-content: right;
        flex-wrap: nowrap !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        --bs-gutter-x: 1.2rem;
        width: 100%;
    }

    .diet-row svg {
        height: 15px;
        width: 15px;
    }

    .see-recipe {
        top: 8%;
        z-index: 1005;
        height: 600px;
    }

    .print-btns {
        width: 50%;
    }

    .second-header-button {
        font-size: 14px;
        position: relative;
        top: -5px;
    }

    .user-week {
        position: relative;
        left: -75px;
    }

    .dropdown-content {
        height: 350px;
    }

    .diet-box-container {
        padding-bottom: 13px
    }
}

@media only screen and (min-width: 1320px) {
    .second-header-save-button {
        font-size: 13px;
        float: right;
        margin-top: 15px;
        margin-right: 90px;
    }

    .print-icon {
        height: 45px;
        width: 45px;
    }

    .dropdown-content {
        height: 345px;
    }

    .column-container {
        padding-top: 2px;
        /*width: calc(18.2vw - 100px);*/
        width: calc(19.8vw - 100px);
    }

    .pad-top {
        margin-top: 40px;
    }

    .menu-container {
        margin-left: 90px;
    }

    .data-column-common {
        /*width: calc(18.25vw - 100px);*/
        width: calc(20vw - 100px);
        height: auto;
    }

    .diet-pane {
        position: relative;
        top: 0px;
        left: 0px;
        margin-right: 3px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
        margin-right: 2px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 0px;
        margin-right: 4px;
    }

    .dropdown-toggle {
        font-size: 16px;
        position: relative;
        bottom: 5px;
    }

    .plus-adj {
        margin: -17px 10px 0 0;
    }

    .diet-row {
        display: flex !important;
        justify-content: right;
        flex-wrap: nowrap !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        --bs-gutter-x: 1.2rem;
        width: 100%;
    }

    .diet-row svg {
        height: 15px;
        width: 15px;
    }

    .see-recipe {
        top: 8%;
        z-index: 1005;
        height: 595px;
        left: 23%;
    }

    .print-btns {
        width: 50%;
    }

    .second-header-button {
        font-size: 14px;
        position: relative;
        top: -5px;
    }

    .diet-box-container {
        padding-bottom: 15px;
    }

    .diet-img {
        height: 31px;
        width: 31px;
    }

    .user-week {
        position: relative;
        left: -75px;
    }
}

@media only screen and (min-width: 1440px) {
    .column-container {
        padding-top: 0px;
        width: calc(20.8vw - 108px);
    }

    .data-column-common {
        height: auto;
    }

    .dropdown {
        width: 99%;
    }

    .dropdown-content {
        width: 98%;
        height: 360px;
    }

    .save-plan {
        width: 12.5%;
        padding-top: 6.5px;
    }

    .menu-container {
        margin-left: 78px;
    }

    .second-header {
        border-radius: 12px 0 0 0;
    }

    .diet-box-container {
        padding-left: 6px;
        padding-bottom: 12px;
    }

    .diet-img {
        height: 35px;
        width: 35px;
    }

    .diet-pane {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 0px;
    }

    .dish-name {
        font-size: 16px;
    }

    .diet-row {
        margin: 10px 0 8px 0;
    }

    .diet-row svg {
        height: 15px;
        width: 15px;
    }

    .see-recipe {
        top: 10%;
        height: 600px;
        width: 1000px;
        left: 19.5%;
        padding-bottom: 100px;
    }

    .padding-left {
        padding-left: 50px;
    }

    .see-recipe-ing {
        font-size: 25px;
    }

    .see-recipe-ins {
        font-size: 25px;
    }

    .ing-style {
        font-size: 18px;
    }

    .pad-top {
        margin-top: 40px;
    }

    .ins-style {
        font-size: 18px;
    }

    .print-icon {
        height: 45px;
        width: 45px;
    }
}

@media only screen and (max-width: 1700px) and (min-width: 1490px) {
    .column-container {
        padding-top: 0px;
        width: calc(19.2vw - 100px);
    }

    .data-column-common {
        width: calc(19.2vw - 100px);
    }

    .print-icon {
        height: 50px;
        width: 50px;
    }

    .menu-container {
        /*margin-left: 85px;*/
    }

    .diet-box-container {
        padding: 10px 2px 15px 7px;
    }

    .diet-type {
        font-size: 12px;
        height: 20px;
    }

    .box {
        /*position: relative;   */
        right: 0px;
    }

    .dropdown-content {
        height: 380px;
    }

    .diet-pane {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 0px;
    }

    .image-div img {
        width: 100%;
        height: auto;
    }

    .diet-row {
        margin: 20px 0 15px 0;
    }

    .diet-row svg {
        height: 17px;
        width: 17px;
    }

    .pad-top {
        margin-top: 60px;
        padding-left: 50px;
    }
}

@media only screen and (max-width: 1919px) and (min-width: 1701px) {
    .column-container {
        padding-top: 0px;
        width: calc(18.6vw - 100px);
    }

    .pad-top {
        margin-top: 60px;
        padding-left: 50px;
    }

    .menu-container {
        margin-left: 95px;
    }

    .diet-img {
        height: 37px;
        width: 37px;
    }

    .data-column-common {
        width: calc(19vw - 100px);
        height: auto;
    }

    .dropdown-content {
        height: 350px;
        /*height: auto;*/
    }

    .diet-type {
        font-size: 11px;
        /*font-size: 11px;*/
        padding-top: 3px;
        padding-bottom: 19px;
    }

    .diet-box-container {
        padding: 10px 2px 15px 2px;
    }

    .box {
        /*position: relative;   */
        right: 0px;
    }

    .dropdown {
        font-size: 16px;
        border-radius: 5px;
    }

    .dropdown-content {
        height: 400px;
    }

    .food-type {
        font-size: 11px;
    }

    .dish-name {
        font-size: 16px;
    }

    .image-div img {
        width: 100%;
        height: auto;
    }

    .diet-box-container {
        padding-left: 8px;
    }

    .diet-pane {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 0px;
    }

    .diet-row {
        margin: 20px 0 15px 0;
    }

    .diet-row svg {
        height: 20px;
        width: 20px;
    }

    .see-recipe {
        top: 10%;
        height: 600px;
        width: 1150px;
        height: 670px;
        left: 18%;
    }

    .padding-left {
        padding-left: 160px;
    }
}

@media only screen and (min-width: 1920px) {
    .column-container {
        padding-top: 22px;
        /*width: calc(18.2vw - 100px);*/
        width: calc(18.2vw - 100px);
    }

    .menu-container {
        margin-left: 110px;
    }

    .data-column-common {
        /*width: calc(18.25vw - 100px);*/
        width: calc(18.2vw - 100px);
        height: auto;
    }

    .dropdown {
        width: 100%;
    }

    .save-plan {
        width: 12.5%;
        padding-top: 16px;
    }

    .food-cat {
        /*padding-left: 50px;*/
    }

    .diet-pane {
        position: relative;
        top: 0px;
        left: 5px;
    }

    .diet-close {
        position: relative;
        top: 0px;
        left: 0px;
    }

    .diet-fav {
        position: relative;
        top: 5px;
        left: 10px;
    }

    .diet-row {
        padding: 10px 0 10px 0;
    }

    .diet-row .heart-icon-adj {
        height: 22px;
        width: 22px;
        position: relative;
        top: -3px;
        left: -1px;
    }

    .diet-row svg {
        height: 20px;
        width: 20px;
    }

    .second-header {
        border-radius: 11px 0 0 0;
        height: 65px;
    }

    .second-header-button {
        padding: 0px 12px;
        font-size: 17px;
        border-radius: 17px;
    }

    .print-btns {
        width: 50%;
        margin-top: 5px;
        margin-left: 10px;
    }

    .second-header-button {
        font-size: 18px;
        position: relative;
        top: 0px;
    }

    .user-week {
        position: relative;
        left: -85px;
    }

    .second-header-save-button {
        font-size: 19px;
        font-weight: 500;
        width: 150px;
        border-radius: 23px;
        float: right;
        margin-top: 14px;
        margin-right: 100px;
    }

    .font-day {
        font-size: 15px;
    }

    .food-date {
        font-size: 20px;
    }

    .dropdown {
        font-size: 16px;
        border-radius: 5px;
    }

    .food-type {
        font-size: 12px;
    }

    .dish-name {
        font-size: 17px;
    }

    .image-div {
        margin-top: 15px;
        /*height:160px;*/
    }

    .image-div img {
        /*width: 216px;
        height: 150px; */
        width: 100%;
        height: 100%;
    }

    .dropdown-content {
        height: 460px;
        /*height: auto;*/
    }

    .diet-type {
        font-size: 12px;
        padding: 3px 10px 20px 10px;
    }

    .diet-box-container {
        padding-bottom: 17px;
        padding-top: 10px;
        padding-left: 4px;
    }

    .diet-img {
        height: 45px;
        width: 45px;
    }

    .axis {
        font-size: 21px;
    }

    .nutrition {
        font-size: 16px;
        max-width: 280px;
    }

    .macros-header-text {
        left: 25%;
        top: 3.5%;
        font-size: 20px;
    }

    .macros-header-close {
        padding-top: 45px;
        cursor: pointer;
    }

    .macros-header {
        height: 70px;
    }

    .macros {
        width: 360px;
        height: 600px;
        left: 42%;
    }

    .see-recipe {
        width: 1100px;
        height: 650px;
        left: 22.5%;
        top: 12%;
        padding-bottom: 150px;
    }

    .ing-section {
        padding-left: 20px;
    }

    .see-recipe-button {
        font-size: 18px;
        width: 150px;
        height: 32px;
    }

    .ing-style {
        font-size: 20px;
    }

    .ins-style {
        font-size: 20px;
    }

    .nutrition-see {
        font-size: 20px;
        max-width: 370px;
    }

    .see-recipe-name {
        font-size: 38px;
    }

    .padding-left {
        padding-left: 40px;
    }

    /*.see-header {
        margin-top: 40px;
    }*/
    .nutrition-see .nutrition-title {
        font-size: 28px;
    }

    .see-recipe-btn-pos {
        top: -30px;
        right: 60px;
    }

    .see-recipe-ing {
        font-size: 26px;
    }

    .see-recipe-ins {
        font-size: 26px;
    }

    .pad-top {
        margin-top: 80px;
    }

    .dropdown-toggle {
        font-size: 24px;
    }

    .toggle-adjust {
        right: 20px;
    }

    .food-type {
        font-size: 17px;
    }

    .food-cat {
        padding-left: calc(43% - 80px);
    }

    .plus-adj {
        margin: -22px 10px 0 0;
    }

    .macros {
        box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.5);
    }

    .print-icon {
        height: 50px;
        width: 50px;
    }
}

@media only screen and (min-width: 2000px) and (max-width: 2559px) {
    .column-container {
        padding-top: 22px;
        /*width: calc(18.2vw - 100px);*/
        width: calc(17.2vw - 100px);
    }

    .dropdown-content {
        height: 500px;
    }

    .menu-container {
        margin-left: 110px;
    }

    .diet-img {
        height: 50px;
        width: 50px;
    }

    .diet-box-container {
        padding-bottom: 20px;
    }

    .data-column-common {
        /*width: calc(18.25vw - 100px);*/
        width: calc(17.2vw - 100px);
        height: auto;
    }

    .see-recipe {
        top: 10%;
        height: 78vh;
        width: 1200px;
        left: 26%;
        padding-bottom: 250px;
    }

    .padding-left {
        padding-left: 70px;
    }

    .nutrition-see {
        font-size: 25px;
        max-width: 450px;
    }

    .ins-style {
        font-size: 22px;
    }

    .ing-style {
        font-size: 22px;
    }

    .see-img {
        height: 730px;
    }

    .ing-section {
        padding-left: 25px;
    }

    .see-info span {
        font-size: 18px;
    }

    .see-recipe-name {
        position: relative;
        top: -90px;
    }

    .see-info {
        margin-top: -80px;
    }

    .modal-img {
        height: 450px;
        width: 450px;
    }

    .pad-top {
        margin-top: 110px;
    }
}

@media only screen and (min-width: 2560px) {
    .column-container {
        width: calc(17vw - 100px);
    }

    .see-info span {
        font-size: 20px;
    }

    .data-column-common {
        width: calc(17vw - 100px);
    }

    .save-plan {
        width: 8.5%;
        padding-top: 13px;
    }

    .menu-container {
        margin-left: 120px;
    }

    .see-recipe-name {
        position: relative;
        top: -90px;
    }

    .see-info {
        margin-top: -80px;
    }

    .modal-img {
        height: 450px;
        width: 450px;
    }

    .pad-top {
        margin-top: 110px;
    }

    .print-btns {
        margin-top: 5px;
        width: 30%;
    }

    .user-week {
        left: 13%;
        font-size: 25px;
        padding-top: 5px;
    }

    .second-header-save-button {
        font-size: 19px;
        font-weight: 500;
        width: 150px;
        border-radius: 23px;
        float: right;
        margin-top: 14px;
        margin-right: 100px;
    }

    .font-day {
        font-size: 15px;
    }

    .food-date {
        font-size: 20px;
    }

    .dropdown {
        font-size: 18px;
        border-radius: 5px;
    }

    .food-type {
        font-size: 14px;
    }

    .dish-name {
        font-size: 22px;
    }

    .image-div img {
        width: 100%;
        height: auto;
    }

    .dropdown-content {
        height: 570px;
    }

    .diet-type {
        font-size: 16px;
        padding-top: 3px;
        padding-bottom: 25px;
    }

    .diet-box-container {
        padding-bottom: 22px;
        padding-top: 10px;
    }

    .diet-img {
        height: 55px;
        width: 55px;
    }

    .axis {
        font-size: 21px;
    }

    .diet-row {
        padding: 25px 0 20px 0;
    }

    .diet-row svg {
        height: 25px;
        width: 25px;
    }

    .see-recipe {
        top: 10%;
        height: 80vh;
        width: 1500px;
        left: 21%;
        padding-bottom: 150px;
    }

    .padding-left {
        padding-left: 200px;
    }

    .nutrition-see {
        font-size: 25px;
        max-width: 450px;
    }

    .ins-style {
        font-size: 22px;
    }

    .ing-style {
        font-size: 22px;
    }

    .see-img {
        height: 730px;
    }
}

@media only screen and (min-height: 500px) {
    .see-recipe {
        height: 68.5vh;
    }
}
