using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent;
using TwentyDishes.Client.State.MenuGlobalState;
using TwentyDishes.Client.State.PublicGlobalState;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;
using TwentyDishes.Shared.Enums;

namespace TwentyDishes.Client.Views.Menu;

public partial class Menu : UserBaseComponent
{
    [CascadingParameter(Name = MenuGlobalStateFields.Value)]
    public MenuGlobalStateValue MenuGlobalStateValue {get; set;}

    [CascadingParameter(Name = MenuGlobalStateFields.SetSelectedUserWeek)]
    public EventCallback<UserWeek?> SetSelectedUserWeek {get; set;}

    [CascadingParameter(Name = MenuGlobalStateFields.SetSelectedWeekRecipes)]
    public EventCallback<List<Recipe>?> SetSelectedWeekRecipes {get; set;}

    [CascadingParameter(Name = PublicGlobalStateFields.Value)]
    public PublicGlobalStateValue PublicGlobalStateValue {get; set;}

    private string activeMealType = "Dinner";
    private readonly List<string> activeMealTypes = new();
    private bool checkingUser = true;
    private User currentUser;
    private UserMenu customMenu = new();
    private List<DailyNutritionValues> dailyNutritionValues;
    private string dayContentDisplay;
    private List<Diet> diets;

    private bool initializing = true;
    private bool isBackdrop;
    private bool isCustomMenu;
    private bool isDayDropdownOpen = true;
    private bool isLoading = true;
    private bool isMacrosActive;
    private bool isMenuActive = true;
    private bool isSeeRecipeActive;
    private readonly bool isTabActive = true;
    private bool isWeekDropdownOpen = true;
    private Brand? logoBrand => PublicGlobalStateValue.Brand;
    private readonly Dictionary<int, RecipeNutrition> macros = new();
    private int macrosDate;
    private string macrosDay;

    //private List<string> mealTypes = new List<string>() { "Breakfast", "Lunch", "Dinner", "Snacks" };
    private readonly List<string> mealTypes = new() { "Breakfast", "Lunch", "Dinner" };

    private readonly int menuDayCount = 7;
    private int menuDayIndex = 0;
    private string message;
    private MiscUtility.AlertMessageType messageType;
    private int mobileDayIndex;
    private bool openAddRecipeDialog;
    private (bool openDialog, bool isNewMenu) openCustomMenuDialog = (false, false);
    private bool PdfExportLoading;
    private bool printActivated;
    private bool readyToInitialize = false;
    private int recipeCount = 0;
    private Recipe seeRecipe;
    private (int currentDayindex, string mealType) selectedMeal = (0, string.Empty);
    private UserWeek selectedUserWeek;
    private List<Recipe> selectedWeekRecipes;
    private RecipeNutrition totalNutrition;
    private string userId;
    private string weekContentDisplay;
    public string dietImageName { get; set; }

    private async Task Initialize()
    {
        initializationStarted = true;

        // var subscriptionPageViewed = await SessionStorage.GetItemAsync<bool>("SubscriptionPageViewed");

        // if (!subscriptionPageViewed)
        // {
        //     NavManager.NavigateTo("/subscription");
        //     return;
        // }

        isMacrosActive = false;
        isSeeRecipeActive = false;
        userId = UserGlobalStateValue.UserId;

        // this page can only load if the user has completed the setup workflow
        if (!UserGlobalStateValue.UserIsSetup)
        {
            NavManager.NavigateTo("/subscription");
            return;
        }

        checkingUser = false;

        StateHasChanged();

        dailyNutritionValues =
            await HttpClient.GetFromJsonAsync<List<DailyNutritionValues>>("/api/DailyNutritionValues");
        diets = await HttpClient.GetFromJsonAsync<List<Diet>>("/api/Diets") ?? new List<Diet>();

        currentUser = userGlobalState.FullUserData;

        if (currentUser == null)
        {
            messageType = MiscUtility.AlertMessageType.Error;
            message = "User not found. Please contact you administrator.";
        }

        await InvokeAsync(() => SetMobileDayIndex());
        await GetUserWeek();
        await InvokeAsync(() => GetWeeklyMacros());
        isLoading = false;
        initializationStarted = false;
        initialized = true;
        initializing = false;
    }

    public override async Task GlobalStateLoaded()
    {
        if (!initialized && !initializationStarted) await Initialize();
    }

    private void GetIconByDietName(string dietName)
    {
        dietImageName = dietName + ".svg";
    }

    private async Task AddRecipeToMenu(string recipeId)
    {
        isLoading = true;
        var mealRecipeIds = new List<string>();
        var propertyName =
            $"{selectedUserWeek.StartDate.AddDays(selectedMeal.currentDayindex).ToString("dddd")}{selectedMeal.mealType}";

        if (selectedUserWeek.GetType().GetProperty(propertyName).GetValue(selectedUserWeek, null) != null)
            mealRecipeIds =
                ((IEnumerable<string>)selectedUserWeek.GetType().GetProperty(propertyName)
                    .GetValue(selectedUserWeek, null)).Cast<string>().ToList() ?? new List<string>();

        //Add selected recipe
        if (!mealRecipeIds.Contains(recipeId)) mealRecipeIds.Add(recipeId);

        //Set value in property
        var pi = selectedUserWeek.GetType().GetProperty(propertyName);
        pi.SetValue(selectedUserWeek, mealRecipeIds);

        //Update UserWeek
        currentUser.UserWeeks[
                currentUser.UserWeeks.FindIndex(x =>
                    x.StartDate == selectedUserWeek.StartDate && x.StopDate == selectedUserWeek.StopDate)] =
            selectedUserWeek;
        await GetWeeklyRecipes();
        await InvokeAsync(() => GetWeeklyMacros());
        isLoading = false;
    }

    private string CalculateDailyValue(double value, string nutrientKey)
    {
        var decCalc =
            Math.Round(
                (decimal)(value / dailyNutritionValues.Where(x => x.Name == nutrientKey).Select(x => x.Value)
                    .FirstOrDefault()), 2) * 100;
        return ((int)decCalc).ToString();
    }

    private void CancelCustomMenuDialog()
    {
        isCustomMenu = false;
        openCustomMenuDialog = (false, false);
    }

    private void ClearBackdrop()
    {
        isSeeRecipeActive = false;
        isMacrosActive = false;
        isBackdrop = false;
    }

    private void CloseMacros()
    {
        ClearBackdrop();
    }

    private async Task CloseCustomMenuDialog(bool newMenuStarted, string menuName)
    {
        if (newMenuStarted) isCustomMenu = true;

        customMenu = currentUser.UserMenus.Where(x => x.MenuName == menuName).FirstOrDefault();
        var selectedWeekIdx = currentUser.UserWeeks.IndexOf(currentUser.UserWeeks
            .Where(x => x.StartDate == selectedUserWeek.StartDate).FirstOrDefault());

        if (selectedWeekIdx != -1)
        {
            customMenu.UserWeek.StartDate = currentUser.UserWeeks[selectedWeekIdx].StartDate;
            customMenu.UserWeek.StopDate = currentUser.UserWeeks[selectedWeekIdx].StopDate;
            currentUser.UserWeeks[selectedWeekIdx] = customMenu.UserWeek;
            await SetUserWeek(customMenu.UserWeek.StartDate);
        }

        openCustomMenuDialog = (false, false);
    }

    private void ClosePrint()
    {
        SetPrintStatus(false);
    }

    private List<Recipe> GetCurrentRecipes(int currentDayIndex, string mealType)
    {
        try
        {
            var propertyName = $"{selectedUserWeek.StartDate.AddDays(currentDayIndex).ToString("dddd")}{mealType}";

            if (selectedUserWeek.GetType().GetProperty(propertyName).GetValue(selectedUserWeek, null) != null &&
                selectedWeekRecipes != null)
            {
                var recipeIds =
                    ((IEnumerable<string>)selectedUserWeek.GetType().GetProperty(propertyName)
                        .GetValue(selectedUserWeek, null)).Cast<string>().ToList();
                var result = selectedWeekRecipes.Where(x => recipeIds.Contains(x.Id)).ToList() ?? new List<Recipe>();
                return result;
            }

            return new List<Recipe>();
        }
        catch (NullReferenceException)
        {
            return new List<Recipe>();
        }
    }

    private async Task GetUserWeek()
    {
        // selectedUserWeek = await SessionStorage.GetItemAsync<UserWeek>("SelectedUserWeek");
        selectedUserWeek = MenuGlobalStateValue.SelectedUserWeek;
        if (selectedUserWeek == null)
            await SetUserWeek(DateTimeHelper.GetFirstDateInWeek());
        else
            await GetWeeklyRecipes();
    }

    private void GetWeeklyMacros()
    {
        if (selectedUserWeek != null && selectedWeekRecipes != null && selectedWeekRecipes.Any())
        {
            var dayIndex = 0;
            var props = typeof(UserWeek).GetProperties().ToList();

            if (macros.Any()) macros.Clear();

            foreach (var dayOfWeek in new List<string>
                         { "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday" })
            {
                var dayOfWeekRecipes =
                    props.Where(x => x.GetValue(selectedUserWeek) != null && x.Name.Contains(dayOfWeek))
                        .SelectMany(x => (List<string>)x.GetValue(selectedUserWeek)).ToList();
                var dayOfWeekNutrition = selectedWeekRecipes.Where(x => dayOfWeekRecipes.Contains(x.Id))
                    .Select(x => x.Nutrition).ToList();
                var summedNutrition = new RecipeNutrition();

                summedNutrition.Calcium = dayOfWeekNutrition.Sum(x => x.Calcium);
                summedNutrition.CalciumUnit = dayOfWeekNutrition.Select(x => x.CalciumUnit).FirstOrDefault();
                summedNutrition.Cholesterol = dayOfWeekNutrition.Sum(x => x.Cholesterol);
                summedNutrition.CholesterolUnit = dayOfWeekNutrition.Select(x => x.CholesterolUnit).FirstOrDefault();
                summedNutrition.Energy = dayOfWeekNutrition.Sum(x => x.Energy);
                summedNutrition.EnergyUnit = dayOfWeekNutrition.Select(x => x.EnergyUnit).FirstOrDefault();
                summedNutrition.Fiber = dayOfWeekNutrition.Sum(x => x.Fiber);
                summedNutrition.FiberUnit = dayOfWeekNutrition.Select(x => x.FiberUnit).FirstOrDefault();
                summedNutrition.Iron = dayOfWeekNutrition.Sum(x => x.Iron);
                summedNutrition.IronUnit = dayOfWeekNutrition.Select(x => x.IronUnit).FirstOrDefault();
                summedNutrition.MonoUnsaturatedFat = dayOfWeekNutrition.Sum(x => x.MonoUnsaturatedFat);
                summedNutrition.MonoUnsaturatedFatUnit =
                    dayOfWeekNutrition.Select(x => x.MonoUnsaturatedFatUnit).FirstOrDefault();
                summedNutrition.PolyUnsaturatedFat = dayOfWeekNutrition.Sum(x => x.PolyUnsaturatedFat);
                summedNutrition.PolyUnsaturatedFatUnit =
                    dayOfWeekNutrition.Select(x => x.PolyUnsaturatedFatUnit).FirstOrDefault();
                summedNutrition.Potassium = dayOfWeekNutrition.Sum(x => x.Potassium);
                summedNutrition.PotassiumUnit = dayOfWeekNutrition.Select(x => x.PotassiumUnit).FirstOrDefault();
                summedNutrition.Protein = dayOfWeekNutrition.Sum(x => x.Protein);
                summedNutrition.ProteinUnit = dayOfWeekNutrition.Select(x => x.ProteinUnit).FirstOrDefault();
                summedNutrition.SaturatedFat = dayOfWeekNutrition.Sum(x => x.SaturatedFat);
                summedNutrition.SaturatedFatUnit = dayOfWeekNutrition.Select(x => x.SaturatedFatUnit).FirstOrDefault();
                summedNutrition.Sodium = dayOfWeekNutrition.Sum(x => x.Sodium);
                summedNutrition.SodiumUnit = dayOfWeekNutrition.Select(x => x.SodiumUnit).FirstOrDefault();
                summedNutrition.TotalCarbsByDifference = dayOfWeekNutrition.Sum(x => x.TotalCarbsByDifference);
                summedNutrition.TotalCarbsByDifferenceUnit =
                    dayOfWeekNutrition.Select(x => x.TotalCarbsByDifferenceUnit).FirstOrDefault();
                summedNutrition.TotalFat = dayOfWeekNutrition.Sum(x => x.TotalFat);
                summedNutrition.TotalFatUnit = dayOfWeekNutrition.Select(x => x.TotalFatUnit).FirstOrDefault();
                summedNutrition.TotalSugar = dayOfWeekNutrition.Sum(x => x.TotalSugar);
                summedNutrition.TotalSugarUnit = dayOfWeekNutrition.Select(x => x.TotalSugarUnit).FirstOrDefault();
                summedNutrition.VitaminA = dayOfWeekNutrition.Sum(x => x.VitaminA);
                summedNutrition.VitaminAUnit = dayOfWeekNutrition.Select(x => x.VitaminAUnit).FirstOrDefault();
                summedNutrition.VitaminC = dayOfWeekNutrition.Sum(x => x.VitaminC);
                summedNutrition.VitaminCUnit = dayOfWeekNutrition.Select(x => x.VitaminCUnit).FirstOrDefault();

                macros.Add(dayIndex, summedNutrition);
                dayIndex++;
            }
        }
    }

    private async Task GetWeeklyRecipes()
    {
        var response = await HttpClient.PostAsJsonAsync("/api/WeeklyRecipes", selectedUserWeek);

        if (response.IsSuccessStatusCode)
        {
            selectedWeekRecipes = await response.Content.ReadFromJsonAsync<List<Recipe>>();
            // await SessionStorage.SetItemAsync<List<Recipe>>("SelectedWeekRecipes", selectedWeekRecipes);
            await SetSelectedWeekRecipes.InvokeAsync(selectedWeekRecipes);
        }
        else
        {
            selectedWeekRecipes = new List<Recipe>();
            messageType = MiscUtility.AlertMessageType.Error;
            message = "There is a problem with your menu data. Please contact you administrator.";
        }
    }

    private void OpenAddRecipeDialog(bool openAddRecipeDialog)
    {
        this.openAddRecipeDialog = openAddRecipeDialog;
    }

    private void OpenAddRecipeDialog(bool openAddRecipeDialog, int currentDayIndex, string mealType)
    {
        selectedMeal = (currentDayIndex, mealType);
        this.openAddRecipeDialog = openAddRecipeDialog;
    }

    private void OpenCustomMenuDialog(bool isNewMenu)
    {
        openCustomMenuDialog = (true, isNewMenu);
    }

    private void OpenMacros(int currentDayIndex)
    {
        if (macros != null && macros.ContainsKey(currentDayIndex))
        {
            macrosDay = selectedUserWeek?.StartDate.AddDays(currentDayIndex).ToString("ddd");
            macrosDate = currentDayIndex;
            totalNutrition = macros[currentDayIndex];
            isBackdrop = true;
            isMacrosActive = true;
        }
    }

    private void OpenSeeRecipe(Recipe currentRecipe)
    {
        SetSeeRecipe(currentRecipe);
        isSeeRecipeActive = true;
        isBackdrop = true;
    }

    private void RemoveRecipe(int currentDayIndex, string mealType, Recipe currentRecipe)
    {
        //Retrieve all recipes for meal type for current day
        var propertyName = $"{selectedUserWeek.StartDate.AddDays(currentDayIndex).ToString("dddd")}{mealType}";
        var mealRecipeIds =
            (List<string>)selectedUserWeek.GetType().GetProperty(propertyName).GetValue(selectedUserWeek, null) ??
            new List<string>();

        //Remove selected recipe
        if (mealRecipeIds.Contains(currentRecipe.Id)) mealRecipeIds.Remove(currentRecipe.Id);

        //Set value in property
        var pi = selectedUserWeek.GetType().GetProperty(propertyName);
        pi.SetValue(selectedUserWeek, mealRecipeIds);

        //Update UserWeek
        currentUser.UserWeeks[
                currentUser.UserWeeks.FindIndex(x =>
                    x.StartDate == selectedUserWeek.StartDate && x.StopDate == selectedUserWeek.StopDate)] =
            selectedUserWeek;
    }

    private async Task SaveCustomMenu()
    {
        UserMenu newCustomMenu;

        var customMenuIdx =
            currentUser.UserMenus.IndexOf(currentUser.UserMenus.Where(x => x.MenuName == customMenu.MenuName)
                .FirstOrDefault());
        if (customMenuIdx != -1)
        {
            currentUser.UserMenus[customMenuIdx].UserWeek = selectedUserWeek;
            currentUser.UserMenus[customMenuIdx].LastUpdated = DateTime.UtcNow;
            newCustomMenu = currentUser.UserMenus[customMenuIdx];

            //We only want to save the custom menu, so refresh the user data before saving
            currentUser = await HttpClient.GetFromJsonAsync<User>("/api/User/" + userId);

            if (currentUser.UserMenus == null) currentUser.UserMenus = new List<UserMenu>();

            currentUser.UserMenus.Add(newCustomMenu);
            currentUser.UserMenus = currentUser.UserMenus.OrderBy(x => x.MenuName).ToList();

            await SaveUser(true);
            // await SessionStorage.ClearAsync();
            NavManager.NavigateTo("/");
        }
    }

    private async Task SaveUser(bool displayMessage)
    {
        if (selectedUserWeek.StartDate.Date == DateTimeHelper.GetFirstDateInWeek().Date)
            currentUser.WeeklyRecipeSteps = new List<UserStep>();

        var response = await HttpClient.PutAsJsonAsync("/api/UpdateUser", currentUser);

        if (displayMessage)
        {
            if (response.IsSuccessStatusCode)
            {
                message = "Menu saved successfully!";
                messageType = MiscUtility.AlertMessageType.Success;
                await SetUserWeek(DateTimeHelper.GetFirstDateInWeek());
                await InvokeAsync(() => GetWeeklyMacros());
            }
            else
            {
                message = "An error occurred while trying to save your menu: " + Environment.NewLine +
                          await response.Content.ReadAsStringAsync();
                messageType = MiscUtility.AlertMessageType.Error;
            }
        }
    }

    private void SelectedDay(int day)
    {
        mobileDayIndex = day;
        isDayDropdownOpen = false;
        dayContentDisplay = "none";
    }

    private async Task SelectedWeek(int week)
    {
        if (week == 1)
        {
            await SetUserWeek(selectedUserWeek.StopDate.AddDays(1));
            weekContentDisplay = "none";
            isWeekDropdownOpen = false;
        }
        else
        {
            await SetUserWeek(selectedUserWeek.StartDate.AddDays(-7));
            weekContentDisplay = "none";
            isWeekDropdownOpen = false;
        }
    }

    private void SetBannerMessage(string message)
    {
        this.message = message;
    }

    private void SetBannerMessageType(MiscUtility.AlertMessageType messageType)
    {
        this.messageType = messageType;
    }

    private void SetMobileDayIndex()
    {
        var day = DateTime.Today.DayOfWeek.ToString();
        switch (day)
        {
            case "Sunday":
                mobileDayIndex = 0;
                break;
            case "Monday":
                mobileDayIndex = 1;
                break;
            case "Tuesday":
                mobileDayIndex = 2;
                break;
            case "Wednesday":
                mobileDayIndex = 3;
                break;
            case "Thursday":
                mobileDayIndex = 4;
                break;
            case "Friday":
                mobileDayIndex = 5;
                break;
            case "Saturday":
                mobileDayIndex = 6;
                break;
        }
    }

    private void SetPrintStatus(bool isActive)
    {
        printActivated = isActive;
        if (isActive)
            isMenuActive = false;
        else
            isMenuActive = true;
    }

    private void SetSeeRecipe(Recipe currentRecipe)
    {
        seeRecipe = currentRecipe;
    }

    private async Task SetUserWeek(DateTime weekStartDate)
    {
        isLoading = true;

        //If moving to a different week, transition away from custom menu
        if (isCustomMenu && customMenu.UserWeek.StartDate != weekStartDate)
        {
            isCustomMenu = false;
            customMenu = new UserMenu();
        }

        if (currentUser.UserWeeks.Where(x => x.StartDate.Date.ToUniversalTime() == weekStartDate.Date).Any())
            selectedUserWeek = currentUser.UserWeeks
                .Where(x => x.StartDate.Date.ToUniversalTime() == weekStartDate.Date).FirstOrDefault();
        else
            selectedUserWeek = currentUser.UserWeeks
                .Where(x => x.StartDate.Date.ToUniversalTime() == DateTimeHelper.GetFirstDateInWeek().Date)
                .FirstOrDefault();
        // await SessionStorage.SetItemAsync<UserWeek>("SelectedUserWeek", selectedUserWeek);
        await SetSelectedUserWeek.InvokeAsync(selectedUserWeek);
        await GetWeeklyRecipes();
        isLoading = false;
    }

    private void ShowDayDropdown()
    {
        if (isDayDropdownOpen)
        {
            dayContentDisplay = "none";
            isDayDropdownOpen = false;
        }
        else
        {
            dayContentDisplay = "block";
            isDayDropdownOpen = true;
        }
    }

    private void ShowWeekDropdown()
    {
        if (isWeekDropdownOpen)
        {
            weekContentDisplay = "none";
            isWeekDropdownOpen = false;
        }
        else
        {
            weekContentDisplay = "block";
            isWeekDropdownOpen = true;
        }
    }

    private void TabSwitch(int mealIndex, int day)
    {
        var activeMealType = string.Empty;

        switch (mealIndex)
        {
            case 0:
                activeMealType = "Breakfast";
                break;
            case 1:
                activeMealType = "Lunch";
                break;
            case 2:
                activeMealType = "Dinner";
                break;
            /* case 3:
                activeMealType = "Snacks";
                break;*/
        }

        activeMealTypes[day] = activeMealType;
    }

    private void TabSwitchForMobile(int mealIndex)
    {
        switch (mealIndex)
        {
            case 0:
                activeMealType = "Breakfast";
                break;
            case 1:
                activeMealType = "Lunch";
                break;
            case 2:
                activeMealType = "Dinner";
                break;
            /*case 3:
                activeMealType = "Snacks";
                break;*/
        }
    }

    private async Task ToggleDropdown(string dropdown, string content)
    {
        await JsRuntime.InvokeVoidAsync("toggleDropdown", dropdown, content);
    }

    private async void ToggleFavorite(Recipe currentRecipe)
    {
        if (currentUser.UserFavorites == null)
        {
            currentUser.UserFavorites = new List<RecipePartial>();
            currentUser.UserFavorites.Add(new RecipePartial
            {
                RecipeId = currentRecipe.Id,
                RecipeImageId = currentRecipe.CloudflareImageId,
                RecipeName = currentRecipe.Name
            });
        }
        else
        {
            if (currentUser.UserFavorites.Where(x => x.RecipeId == currentRecipe.Id).Any())
                currentUser.UserFavorites =
                    currentUser.UserFavorites.Where(x => x.RecipeId != currentRecipe.Id).ToList();
            else
                currentUser.UserFavorites.Add(new RecipePartial
                {
                    RecipeId = currentRecipe.Id,
                    RecipeImageId = currentRecipe.CloudflareImageId,
                    RecipeName = currentRecipe.Name
                });
        }

        await SaveUser(false);
    }

    private async Task PrintReport()
    {
        PdfExportLoading = true;

        try
        {
            var response = await HttpClient.PostAsJsonAsync("/api/CreateMenuPdf",
                new CreateMenuPdfRequest
                {
                    UserWeek = null,
                    AdditionalRecipeIds = new List<string> { seeRecipe.Id },
                    WeekStartDate = selectedUserWeek.StartDate,
                    IncludeRecipes = true,
                    IncludeMealPlan = false,
                    IncludePrepGuide = false,
                    IncludeShoppingList = false,
                    Format = MenuPdfFormat.SingleRecipe,
                    BrandId = logoBrand.Id
                }
            );

            var fileContent = await response.Content.ReadAsByteArrayAsync();

            await JsRuntime.InvokeVoidAsync("DownloadFileFromByteArray", "menu.pdf", "application/pdf", fileContent);
        }
        catch (Exception)
        {
        }
        finally
        {
            PdfExportLoading = false;
        }
    }
}