@page "/menu"
@page "/"
@attribute [Authorize]
@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using TwentyDishes.Client.Components.SplashScreen
@using TwentyDishes.Client.Components.TdButton
@using TwentyDishes.Client.Components.Print
@inherits TwentyDishes.Client.Components.BaseComponents.UserBaseComponent.UserBaseComponent

@inject HttpClient HttpClient
@inject IJSRuntime JsRuntime 
@inject NavigationManager NavManager
@inject IAccessTokenProvider AuthorizationService

@if (checkingUser)
{
    <SplashScreen/>
}
else
{
    <div class="main-header">
        <Header/>
    </div>

    @*will have to pass the name of the page where the SideMenu component is being used so that it can put the active Badge on that page icon*@
    <SideMenu ActivePage="menu"/>

    @*Banner*@
    <Banner @bind-MessageType="@messageType" @bind-Message="@message"/>

    @*second header*@
    <div class="second-header">
        <div class="pt-1 ps-2">
            @if (!isCustomMenu)
            {
                <div class="ps-2 print-btns">
                    <div class="mytooltip">
                        <img src="/images/CalendarIcons/CreateCustomWeek.svg" class="print-icon" @onclick="@(() => OpenCustomMenuDialog(true))" alt="none"/>
                        <span class="tooltiptext">Create Custom Week</span>
                    </div>
                    <div class="mytooltip">
                        <img src="/images/CalendarIcons/UseCustomWeek.svg" class="print-icon" @onclick="@(() => OpenCustomMenuDialog(false))">
                        <span class="tooltiptext">Use Custom Week</span>
                    </div>
                    <div class="mytooltip">
                        <img src="/images/CalendarIcons/ExportMealPlan.svg" class="print-icon" @onclick="@(() => SetPrintStatus(true))">
                        <span class="tooltiptext">Save Meal Plan As PDF</span>
                    </div>
                </div>
            }
            <div class="user-week">
                @if (!isLoading && !initializing)
                {
                    @if (!isCustomMenu)
                    {
                        <svg class="left-arrow" @onclick="@(() => SetUserWeek(selectedUserWeek.StartDate.AddDays(-7)))" width="11" height="22" viewBox="0 0 11 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.99949 11L11 19.5559L9.00026 22L0 11L9.00026 0L11 2.44406L3.99949 11Z" fill="#B6B6B6"/>
                        </svg>
                        <span class="axis">
                            @selectedUserWeek.StartDate.Day @selectedUserWeek.StartDate.Date.ToString("MMM") - @selectedUserWeek.StopDate.Day @selectedUserWeek.StopDate.Date.ToString("MMM")
                        </span>
                        <svg class="right-arrow" @onclick="@(() => SetUserWeek(selectedUserWeek.StopDate.AddDays(1)))" width="11" height="22" viewBox="0 0 11 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7.00051 11L0 2.44406L1.99974 0L11 11L1.99974 22L0 19.5559L7.00051 11Z" fill="#B6B6B6"/>
                        </svg>
                        <div class="toggle-sm">
                            <div class="dropdowned-01">
                                <div class="dropdown-toggle" @onclick="ShowWeekDropdown">@selectedUserWeek.StartDate.Day @selectedUserWeek.StartDate.Date.ToString("MMM") - @selectedUserWeek.StopDate.Day @selectedUserWeek.StopDate.Date.ToString("MMM")</div>
                                <div id="myDropdown" class="dropdown-content-2" style=@($"display:{weekContentDisplay};")>
                                    <a @onclick="() => SelectedWeek(1)" class="list-group-item">Next Week</a>
                                    <a @onclick="() => SelectedWeek(-7)" class="list-group-item">Previous Week</a>
                                </div>
                            </div>
                        </div>
                    }
                }
            </div>
            <div class="day-of-week" style="@(isCustomMenu ? "position: relative; left: 20px;" : string.Empty)">
                @if (!isLoading && !initializing)
                {
                    <div class="dropdowned">
                        <div class="dropdown-toggle" @onclick="ShowDayDropdown"> @selectedUserWeek.StartDate.AddDays(mobileDayIndex).DayOfWeek.ToString()</div>
                        <div id="myDropdown" class="dropdown-content-2" style=@($"display:{dayContentDisplay};")>
                            @for (var i = 0; i < menuDayCount; i++)
                            {
                                var loopIndex = i;
                                <a @onclick="() => SelectedDay(loopIndex)" class=@($"list-group-item solo{loopIndex}")>@selectedUserWeek.StartDate.AddDays(loopIndex).DayOfWeek.ToString()</a>
                            }
                        </div>
                    </div>
                }
            </div>
            <div>
                @if (!isCustomMenu)
                {
                    <button class="second-header-save-button" @onclick="@(() => SaveUser(true))"><span class="oi oi-document px-1"></span>Save Plan</button>
                }
                else
                {
                    <button class="second-header-save-button save-custom-menu-sm" style="width: 225px;" @onclick="SaveCustomMenu"><span class="oi oi-document px-1"></span>Save Custom Menu</button>
                }
            </div>
        </div>
    </div>

    @*only for smaller screens*@

    <div class="print-btns-sm">
        @if (!isCustomMenu)
        {
            @* <div><button class="second-header-button" @onclick="@(() => OpenCustomMenuDialog(true))">Create Custom Week</button></div>
            <div><button class="second-header-button" @onclick="@(() => OpenCustomMenuDialog(false))">Use Custom Week</button></div>
            <div><button class="second-header-button" @onclick="@(() => SetPrintStatus(true))">Export Meal Plans</button></div> *@
            <img src="/images/CalendarIcons/CreateCustomWeek.svg" class="print-icon" @onclick="@(() => OpenCustomMenuDialog(true))" alt="none"/>
            <img src="/images/CalendarIcons/UseCustomWeek.svg" class="print-icon" @onclick="@(() => OpenCustomMenuDialog(false))">
            <img src="/images/CalendarIcons/ExportMealPlan.svg" class="print-icon" @onclick="@(() => SetPrintStatus(true))">
        }
    </div>

    @*Print-feature*@
    @if (printActivated && !initializing)
    {
        <Print OnBackToPage="ClosePrint" ActivePage="Menu"/>
    }

    @*MACROS*@
    @if (isMacrosActive && !initializing)
    {
        <div class="macros">
            <div class="macros-header">
                <span class="macros-header-text"><span style="font-weight:bold">@selectedUserWeek?.StartDate.Month.@selectedUserWeek?.StartDate.AddDays(macrosDate).Day</span> <span class="p-1 text-uppercase">@macrosDay</span> MACROS</span>
                <span @onclick="CloseMacros" class="btn-close btn-close-white macros-header-close"></span>
            </div>
            <section class="nutrition">
                <header>
                    <h1 class="nutrition-title">Total Daily Macros</h1>
                </header>
                <ul class="nutrition-facts">
                    <li class="nutrition-note"><span>Amount per serving</span> %Daily Values*</li>
                    <li>
                        <span>
                            <span class="nutrition-facts-label">Calories</span> @totalNutrition.Energy
                        </span> @("Calories from Fat " + (int)(totalNutrition.TotalFat.Value * 9))
                    </li>
                    <li class="nutrition-facts-section">
                        <span>
                            <span class="nutrition-facts-label">Total Fat</span> @(totalNutrition.TotalFat + totalNutrition.TotalFatUnit)
                        </span>
                        @(CalculateDailyValue(totalNutrition.TotalFat.Value, nameof(IngredientNutrition.TotalFat)) + "%")
                        <ul>
                            <li>
                                <span>Saturated Fat @(totalNutrition.SaturatedFat.Value + totalNutrition.SaturatedFatUnit)</span>@(CalculateDailyValue(totalNutrition.SaturatedFat.Value, nameof(IngredientNutrition.SaturatedFat)) + "%")
                            </li>
                            <li>Polyunsaturated Fat @(totalNutrition.PolyUnsaturatedFat.Value + totalNutrition.PolyUnsaturatedFatUnit)</li>
                            <li>Monounsaturated Fat @(totalNutrition.MonoUnsaturatedFat.Value + totalNutrition.MonoUnsaturatedFatUnit)</li>
                        </ul>
                    </li>
                    <li>
                        <span>
                            <span class="nutrition-facts-label">Sodium</span> @(totalNutrition.Sodium.Value + totalNutrition.SodiumUnit)
                        </span>
                        @(CalculateDailyValue(totalNutrition.Sodium.Value, nameof(IngredientNutrition.Sodium)) + "%")
                    </li>
                    <li>
                        <span>
                            <span class="nutrition-facts-label">Potassium</span> @(totalNutrition.Potassium.Value + totalNutrition.PotassiumUnit)
                        </span>
                        @(CalculateDailyValue(totalNutrition.Potassium.Value, nameof(IngredientNutrition.Potassium)) + "%")
                    </li>
                    <li>
                        <span>
                            <span class="nutrition-facts-label">Total Carbohydrate</span> @(totalNutrition.TotalCarbsByDifference.Value + totalNutrition.TotalCarbsByDifferenceUnit)
                        </span>
                        @(CalculateDailyValue(totalNutrition.TotalCarbsByDifference.Value, nameof(IngredientNutrition.TotalCarbsByDifference)) + "%")
                        <ul>
                            <li>
                                <span>Dietary Fiber @(totalNutrition.Fiber.Value + totalNutrition.FiberUnit)</span> @(CalculateDailyValue(totalNutrition.Fiber.Value, nameof(IngredientNutrition.Fiber)) + "%")
                            </li>
                            <li>Sugars @(totalNutrition.TotalSugar.Value + totalNutrition.TotalSugarUnit)</li>
                        </ul>
                    </li>
                    <li>
                        <span>
                            <span class="nutrition-facts-label">Protein</span> @(totalNutrition.Protein.Value + totalNutrition.ProteinUnit)
                        </span> @(CalculateDailyValue(totalNutrition.Protein.Value, nameof(IngredientNutrition.Protein)) + "%")
                    </li>
                </ul>
                <ul class="nutrition-facts">
                    <li>
                        <span>Vitamin A @(totalNutrition.VitaminA.Value + totalNutrition.VitaminAUnit)</span>
                        @(CalculateDailyValue(totalNutrition.VitaminA.Value, nameof(IngredientNutrition.VitaminA)) + "%")
                    </li>
                    <li>
                        <span>Vitamin C @(totalNutrition.VitaminC.Value + totalNutrition.VitaminCUnit)</span>
                        @(CalculateDailyValue(totalNutrition.VitaminC.Value, nameof(IngredientNutrition.VitaminC)) + "%")
                    </li>
                    <li>
                        <span>Calcium @(totalNutrition.Calcium.Value + totalNutrition.CalciumUnit)</span>
                        @(CalculateDailyValue(totalNutrition.Calcium.Value, nameof(IngredientNutrition.Calcium)) + "%")
                    </li>
                    <li>
                        <span>Iron @(totalNutrition.Iron.Value + totalNutrition.IronUnit)</span>
                        @(CalculateDailyValue(totalNutrition.Iron.Value, nameof(IngredientNutrition.Iron)) + "%")
                    </li>
                </ul>
                <footer class="nutrition-note">
                    * Percent Daily Values (DV) are based on a 2,000 calorie diet
                </footer>
            </section>
        </div>
    }

    @*See-Recipe*@
    @if (isSeeRecipeActive)
    {
        <div class="see-recipe">
            @if (seeRecipe != null)
            {
                <div class="float-end p-2 close-pos" @onclick="CloseMacros">
                    <span style="font-size: 20px;" class="btn btn-close"></span>
                </div>
                <div class="see-img">
                    <img class="modal-img" src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, seeRecipe.CloudflareImageId, MiscUtility.ImageUrlSuffixDesktopModal)"
                         alt="@seeRecipe.Name"/>
                </div>
                @* <div class="see-recipe-btn-pos">
                    <button class="see-recipe-button"><i class="oi oi-print p-1"></i>Print</button>
                </div> *@
                <div class="see-header pt-2">
                    <div class="text-center">
                        <span class="see-recipe-name">@seeRecipe.Name</span>
                    </div>
                    <div class="see-info text-center">
                        <span style="color: rgba(202, 47, 53, 1);" class="oi oi-clock"></span><span style="padding:0 5px; font-family: raleway;">Cook in @seeRecipe.CookTime mins</span>
                        <span style="color: rgba(202, 47, 53, 1);" class="oi oi-clock"></span><span style="padding:0 5px; font-family: raleway;">Prep in @seeRecipe.PrepTime mins</span>
                        <span style="color: rgba(202, 47, 53, 1);" class="oi oi-pie-chart"></span>
                        <span style="padding:0 5px; font-family: raleway;">Serves @seeRecipe.Servings @seeRecipe.ServingsUnit</span>
                    </div>
                </div>
                <div class="see-footer">
                    <div class="tdbuttoncover">
                        <TdButton OnClick="PrintReport" Loading="PdfExportLoading" loaderLeftPropertyCSS="-15px">
                            <span class="food-type-desk">Save as PDF</span>
                        </TdButton>
                    </div>
                </div>
                <div class="container pt-2 pad-top">
                    <div class="row">
                        <div style="font-family:Raleway;" class="col-md-6 col-lg-6 col-sm-12 ing-section">
                            <span class="see-recipe-ing">Ingredients</span>
                            @{
                                var b = 1;
                            }
                            <ul>
                                @foreach (var ingr in seeRecipe.Ingredients)
                                {
                                    <li class="ps-2 pt-2 ing-style">@IngredientHelper.FormatIngredientString(ingr, 25)</li>
                                    b++;
                                }
                            </ul>
                            <div class="see-recipe-ins">Instructions</div>
                            @{
                                var a = 1;
                            }
                            @foreach (var instr in seeRecipe.Instructions)
                            {
                                @if (instr != null && !string.IsNullOrWhiteSpace(instr.Text))
                                {
                                    <div class="ps-2 pt-2 ins-style">
                                        <span style="font-family:raleway;" class="group-pill fw-lighter">@a</span>
                                        <span style="">@MiscUtility.ReplaceHtmlTags(instr.Text)</span>
                                    </div>
                                    <br/>
                                    a++;
                                }
                            }
                        </div>
                        <div class="col-md-6 col-lg-6 col-sm-12 mb-3 padding-left">
                            <section class="nutrition-see">
                                <header>
                                    <h1 class="nutrition-title">Recipe Nutrients</h1>
                                </header>
                                <ul class="nutrition-facts">
                                    <li class="nutrition-note"><span>Amount per serving</span> %Daily Values*</li>
                                    <li>
                                        <span>
                                            <span class="nutrition-facts-label">Calories</span> @seeRecipe.Nutrition.Energy @seeRecipe.Nutrition.EnergyUnit
                                        </span> @("Calories from Fat " + (int)(seeRecipe.Nutrition.TotalFat * 9))
                                    </li>
                                    <li class="nutrition-facts-section">
                                        <span>
                                            <span class="nutrition-facts-label">Total Fat</span> @(seeRecipe.Nutrition.TotalFat + seeRecipe.Nutrition.TotalFatUnit)
                                        </span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.TotalFat.Value, nameof(IngredientNutrition.TotalFat)) + "%")
                                        <ul>
                                            <li>
                                                <span>Saturated Fat @(seeRecipe.Nutrition.SaturatedFat + seeRecipe.Nutrition.SaturatedFatUnit)</span> @(CalculateDailyValue(seeRecipe.Nutrition.SaturatedFat.Value, nameof(IngredientNutrition.SaturatedFat)) + "%")
                                            </li>
                                            <li>Polyunsaturated Fat @(seeRecipe.Nutrition.PolyUnsaturatedFat + seeRecipe.Nutrition.PolyUnsaturatedFatUnit)</li>
                                            <li>Monounsaturated Fat @(seeRecipe.Nutrition.MonoUnsaturatedFat + seeRecipe.Nutrition.MonoUnsaturatedFatUnit)</li>
                                        </ul>
                                    </li>
                                    <li>
                                        <span>
                                            <span class="nutrition-facts-label">Sodium</span> @(seeRecipe.Nutrition.Sodium + seeRecipe.Nutrition.SodiumUnit)
                                        </span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.Sodium.Value, nameof(IngredientNutrition.Sodium)) + "%")
                                    </li>
                                    <li>
                                        <span>
                                            <span class="nutrition-facts-label">Potassium</span> @(seeRecipe.Nutrition.Potassium + seeRecipe.Nutrition.PotassiumUnit)
                                        </span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.Potassium.Value, nameof(IngredientNutrition.Potassium)) + "%")
                                    </li>
                                    <li>
                                        <span>
                                            <span class="nutrition-facts-label">Total Carbohydrate</span> @(seeRecipe.Nutrition.TotalCarbsByDifference + seeRecipe.Nutrition.TotalCarbsByDifferenceUnit)
                                        </span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.TotalCarbsByDifference.Value, nameof(IngredientNutrition.TotalCarbsByDifference)) + "%")
                                        <ul>
                                            <li>
                                                <span>Dietary Fiber @(seeRecipe.Nutrition.Fiber + seeRecipe.Nutrition.FiberUnit)</span> @(CalculateDailyValue(seeRecipe.Nutrition.Fiber.Value, nameof(IngredientNutrition.Fiber)) + "%")
                                            </li>
                                            <li>Sugars @(seeRecipe.Nutrition.TotalSugar + seeRecipe.Nutrition.TotalSugarUnit)</li>
                                        </ul>
                                    </li>
                                    <li>
                                        <span>
                                            <span class="nutrition-facts-label">Protein</span> @(seeRecipe.Nutrition.Protein + seeRecipe.Nutrition.ProteinUnit)
                                        </span> @(CalculateDailyValue(seeRecipe.Nutrition.Protein.Value, nameof(IngredientNutrition.Protein)) + "%")
                                    </li>
                                </ul>
                                <ul class="nutrition-facts">
                                    <li>
                                        <span>Vitamin A @(seeRecipe.Nutrition.VitaminA + seeRecipe.Nutrition.VitaminAUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.VitaminA.Value, nameof(IngredientNutrition.VitaminA)) + "%")
                                    </li>
                                    <li>
                                        <span>Vitamin C @(seeRecipe.Nutrition.VitaminC + seeRecipe.Nutrition.VitaminCUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.VitaminC.Value, nameof(IngredientNutrition.VitaminC)) + "%")
                                    </li>
                                    <li>
                                        <span>Calcium @(seeRecipe.Nutrition.Calcium + seeRecipe.Nutrition.CalciumUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.Calcium.Value, nameof(IngredientNutrition.Calcium)) + "%")
                                    </li>
                                    <li>
                                        <span>Iron @(seeRecipe.Nutrition.Iron + seeRecipe.Nutrition.IronUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.Iron.Value, nameof(IngredientNutrition.Iron)) + "%")
                                    </li>
                                </ul>
                                <footer class="nutrition-note">
                                    * Percent Daily Values (DV) are based on a 2,000 calorie diet
                                </footer>
                            </section>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <Loader/>
            }
        </div>
    }

    @*main-display*@
    @if (isMenuActive)
    {
        <div class="main-display">
            <div class="menu-container">
                @if (!isLoading && !initializing)
                {
                    var mealCounter = 0;
                    var recipeCounter = 0;
                    for (var menuDayIndex = 0; menuDayIndex < menuDayCount; menuDayIndex++)
                    {
                        var currentDayIndex = menuDayIndex;
                        <div class="column-container">
                            <div class="data-column-common">
                                <div class="container container-fluid">
                                    <div class="row">
                                        <div class="col-5 food-days-main">
                                            <span class="ps-3 food-date-sm">@selectedUserWeek?.StartDate.AddDays(menuDayIndex).Day</span>
                                            <div class="pt-4 font-day">@selectedUserWeek?.StartDate.AddDays(menuDayIndex).ToString("dddd")</div>
                                        </div>
                                        <div class="pt-3 col-7 food-cat">
                                            <span @onclick="() => OpenMacros(currentDayIndex)" class="food-type">Macro</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="ps-3 food-date">@selectedUserWeek?.StartDate.AddDays(menuDayIndex).Day</div>
                                <div class="minor-pad pt-2">
                                    @foreach (var mealType in mealTypes)
                                    {
                                        var results = GetCurrentRecipes(menuDayIndex, mealType);

                                        var mealDropdownIndex = mealCounter;
                                        <div class="pt-2 dropdown-sm">
                                            <div class=@(results.Count > 0 ? "dropdown pt-2 dropdown-menu-active " : "dropdown pt-2") id=@($"dropdown{mealCounter}")>
                                                <div class="ps-2 pointer" @onclick=@(() => ToggleDropdown($"dropdown{mealDropdownIndex}", $"content{mealDropdownIndex}"))>
                                                    <span>@mealType</span>
                                                    <div class="toggle-adjust">
                                                        <svg @onclick="() => OpenAddRecipeDialog(true, currentDayIndex, mealType)" @onclick:stopPropagation="true" @onclick:preventDefault="true" class="plus-adj" width="12" height="15" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M6.87165 6.93813V0.203613H9.11649V6.93813H15.851V9.18296H9.11649V15.9175H6.87165V9.18296H0.137138V6.93813H6.87165Z" fill="black"/>
                                                        </svg>
                                                        <span class="dropdown-toggle"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            @if (results.Count > 0)
                                            {
                                                foreach (var currentRecipe in GetCurrentRecipes(menuDayIndex, mealType))
                                                {
                                                    <div class=@($"dropdown-content content{mealCounter} hasData")>
                                                        @{
                                                            recipeCounter++;
                                                        }
                                                        <div class="container">
                                                            <div class="row diet-row">
                                                                <div class="diet-container pt-1 col-6">
                                                                    @*<span style="font-size: 11px;">#@recipeCounter</span>*@
                                                                </div>
                                                                <div class="diet-fav col-2">
                                                                    @if (currentUser.UserFavorites != null && currentUser.UserFavorites.Where(x => x.RecipeId == currentRecipe.Id).Any())
                                                                    {
                                                                        <svg @onclick="@(() => ToggleFavorite(currentRecipe))" width="20px" height="20px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--fxemoji heart-icon-adj" preserveAspectRatio="xMidYMid meet">
                                                                            <path fill="#FF473E" d="M476.6 133c-53.7-63.5-148.7-66.5-206.3-9.1l-.2.2c-8 8-20.5 8-28.4 0l-.2-.2c-54.6-54.8-143.3-55-198.1-.4C-3.2 169.8-10.9 244.6 25 299.7c11.2 17.3 25.3 30.6 40.9 40.7l180 145.7c6.4 5.2 15.6 5.2 22.1 0l178.8-145.9c15-10 28.6-23 39.5-39.5c34.1-51.3 30.1-120.7-9.7-167.7z"></path>
                                                                        </svg>
                                                                    }
                                                                    else
                                                                    {
                                                                        <svg width="20" height="19" viewBox="0 0 20 19" @onclick="@(() => ToggleFavorite(currentRecipe))">
                                                                            <use href="images/icons.svg#favorite-inactive"/>
                                                                        </svg>
                                                                    }
                                                                </div>
                                                                <div @onclick="() => OpenSeeRecipe(currentRecipe)" class="pt-1 col-2 diet-pane">
                                                                    <svg width="12" height="14" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M3.83333 3.99996V1.49996C3.83333 1.27895 3.92113 1.06698 4.07741 0.910704C4.23369 0.754423 4.44565 0.666626 4.66667 0.666626H14.6667C14.8877 0.666626 15.0996 0.754423 15.2559 0.910704C15.4122 1.06698 15.5 1.27895 15.5 1.49996V13.1666C15.5 13.3876 15.4122 13.5996 15.2559 13.7559C15.0996 13.9122 14.8877 14 14.6667 14H12.1667V16.5C12.1667 16.96 11.7917 17.3333 11.3275 17.3333H1.33917C1.22927 17.334 1.12033 17.3129 1.0186 17.2713C0.916873 17.2297 0.824361 17.1685 0.746382 17.091C0.668403 17.0136 0.606494 16.9215 0.564212 16.8201C0.52193 16.7186 0.500108 16.6099 0.5 16.5L0.5025 4.83329C0.5025 4.37329 0.8775 3.99996 1.34083 3.99996H3.83333ZM2.16833 5.66663L2.16667 15.6666H10.5V5.66663H2.16833ZM5.5 3.99996H12.1667V12.3333H13.8333V2.33329H5.5V3.99996ZM3.83333 8.16663H8.83333V9.83329H3.83333V8.16663ZM3.83333 11.5H8.83333V13.1666H3.83333V11.5Z" fill="black"/>
                                                                    </svg>
                                                                </div>
                                                                <div class="pt-1 col-2 diet-close" @onclick="() => RemoveRecipe(currentDayIndex, mealType, currentRecipe)">
                                                                    <svg width="10" height="10" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                        <path d="M6.00011 4.82166L10.1251 0.696655L11.3034 1.87499L7.17844 5.99999L11.3034 10.125L10.1251 11.3033L6.00011 7.17832L1.87511 11.3033L0.696777 10.125L4.82178 5.99999L0.696777 1.87499L1.87511 0.696655L6.00011 4.82166Z" fill="black"/>
                                                                    </svg>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="pt-1 col-12 dish-div">
                                                                    <span class="dish-name">
                                                                        @currentRecipe.Name
                                                                    </span>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col-12">
                                                                    <div class="pt-2 image-div" @onclick="() => OpenSeeRecipe(currentRecipe)">
                                                                        <img class="rounded-3 recipe-image" height="100" width="135" src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, currentRecipe.CloudflareImageId, MiscUtility.ImageUrlSuffixThumbnail)"
                                                                             alt="@currentRecipe.Name"/>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="box">
                                                                @foreach (var diet in currentRecipe.Diets)
                                                                {
                                                                    GetIconByDietName(diet);
                                                                    <div class="diet-box-container">
                                                                        @* <div class="diet-type">                                                                        

                                                                        </div> *@
                                                                        <div class="mytooltip">
                                                                            <div class="diet-img">
                                                                                <img src="/images/DietIcons/@dietImageName" alt="@dietImageName">
                                                                            </div>
                                                                            <span class="tooltiptext">@dietImageName.Substring(0, dietImageName.Length - 4)</span>
                                                                        </div>
                                                                    </div>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>
                                                }
                                            }
                                            else
                                            {
                                                <div class=@($"dropdown-content content{mealCounter}") style="display:none;">
                                                    <div class="container">
                                                        <div class="row diet-row">
                                                        </div>
                                                        <div class="row">
                                                            <div class="pt-1 col-12 dish-div">
                                                                <span style="font-family: Raleway; font-weight: lighter;">
                                                                    Looks like you don’t have any recipes selected. Add a recipe now!
                                                                </span>
                                                                <div class="pt-3">
                                                                    <span class="food-type" @onclick="() => OpenAddRecipeDialog(true, currentDayIndex, mealType)">Add</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                        mealCounter++;
                                    }
                                </div>
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div class="load">
                        <Loader/>
                    </div>
                }
            </div>
        </div>

        @*mobile-display*@
        <div class="mobile-display">
            @if (!isLoading && !initializing)
            {
                <div class="menu-wrapper-sm">
                    <div class="date-section-sm">
                        <div class="recipe-date-sm">
                            @selectedUserWeek?.StartDate.Month.@selectedUserWeek?.StartDate.AddDays(mobileDayIndex).Day @selectedUserWeek?.StartDate.AddDays(mobileDayIndex).ToString("ddd")
                        </div>
                        <div class="date-controls-sm">
                            <svg @onclick="() => OpenMacros(mobileDayIndex)" class="ps-2" width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20.0948 23.5835H3.81442C2.88917 23.5835 2.00182 23.2159 1.34757 22.5617C0.693321 21.9074 0.325768 21.0201
                                        0.325768 20.0948V1.48866C0.325768 1.18024 0.448285 0.884459 0.666368 0.666376C0.884452 0.448293 1.18024
                                        0.325775 1.48865 0.325775H17.769C18.0775 0.325775 18.3732 0.448293 18.5913 0.666376C18.8094 0.884459 18.9319
                                        1.18024 18.9319 1.48866V15.4433H23.5835V20.0948C23.5835 21.0201 23.2159 21.9074 22.5617 22.5617C21.9074
                                        23.2159 21.0201 23.5835 20.0948 23.5835ZM18.9319 17.769V20.0948C18.9319 20.4032 19.0544 20.699 19.2725
                                        20.9171C19.4906 21.1352 19.7864 21.2577 20.0948 21.2577C20.4032 21.2577 20.699 21.1352 20.9171 20.9171C21.1352
                                        20.699 21.2577 20.4032 21.2577 20.0948V17.769H18.9319ZM16.6062 21.2577V2.65154H2.65154V20.0948C2.65154 20.4032
                                        2.77405 20.699 2.99214 20.9171C3.21022 21.1352 3.50601 21.2577 3.81442 21.2577H16.6062ZM4.97731
                                        6.1402H14.2804V8.46597H4.97731V6.1402ZM4.97731 10.7917H14.2804V13.1175H4.97731V10.7917ZM4.97731
                                        15.4433H10.7917V17.769H4.97731V15.4433Z" fill="#CA2F35"/>
                            </svg>
                            <svg class="ps-2" width="35" height="35" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg" @onclick="() => OpenAddRecipeDialog(true, mobileDayIndex, activeMealType)">
                                <circle cx="14.8185" cy="13.9546" r="13.9546" fill="black"/>
                            </svg>
                            <svg @onclick="() => OpenAddRecipeDialog(true, mobileDayIndex, activeMealType)" style="position:absolute; right: 16px; top: 32px;" width="10" height="9" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M5.12077 4.25691V0.0705261H6.51623V4.25691H10.7026V5.65237H6.51623V9.83876H5.12077V5.65237H0.934387V4.25691H5.12077Z" fill="white"/>
                            </svg>
                        </div>
                    </div>
                    <div class="meal-tabs-sm">
                        <div class="container">
                            <div class="row">
                                @{
                                    var idx = 0;
                                }
                                @foreach (var mealType in mealTypes)
                                {
                                    var mealIndex = idx;
                                    <div class=@(isTabActive ? "col-3 tab-font tab-font-active" : "col-3 tab-font") @onclick="() => TabSwitchForMobile(mealIndex)">@mealType</div>
                                    {
                                        idx++;
                                    }
                                }
                            </div>
                        </div>
                    </div>
                    <div class="tab-bar-sm">
                        <div class="bar"></div>
                        <div class="@activeMealType-bar-active"></div>
                    </div>
                    @{
                        var recipes = GetCurrentRecipes(mobileDayIndex, activeMealType);
                    }

                    @if (recipes.Count > 0)
                    {
                        @foreach (var currentRecipe in recipes)
                        {
                            <div class="dropdown-content-sm">
                                <div class="container">
                                    <div class="diet-row">
                                        <div>
                                            <span style="font-size: 11px;"></span>
                                        </div>
                                        <div class="controls-sm">
                                            @if (currentUser.UserFavorites != null && currentUser.UserFavorites.Where(x => x.RecipeId == currentRecipe.Id).Any())
                                            {
                                                <svg @onclick="@(() => ToggleFavorite(currentRecipe))" width="22px" style="margin-right: 10px; margin-top: -2px;" height="22px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--fxemoji heart-icon-adj" preserveAspectRatio="xMidYMid meet">
                                                    <path fill="#FF473E" d="M476.6 133c-53.7-63.5-148.7-66.5-206.3-9.1l-.2.2c-8 8-20.5 8-28.4 0l-.2-.2c-54.6-54.8-143.3-55-198.1-.4C-3.2 169.8-10.9 244.6 25 299.7c11.2 17.3 25.3 30.6 40.9 40.7l180 145.7c6.4 5.2 15.6 5.2 22.1 0l178.8-145.9c15-10 28.6-23 39.5-39.5c34.1-51.3 30.1-120.7-9.7-167.7z"></path>
                                                </svg>
                                            }
                                            else
                                            {
                                                <svg width="20" height="19" viewBox="0 0 20 19" style="margin-right: 8px;" @onclick="@(() => ToggleFavorite(currentRecipe))">
                                                    <use href="images/icons.svg#favorite-inactive"/>
                                                </svg>
                                            }
                                            <svg @onclick="() => OpenSeeRecipe(currentRecipe)" style="margin-right: 8px;" width="18" height="20" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M3.83333 3.99996V1.49996C3.83333 1.27895 3.92113 1.06698 4.07741 0.910704C4.23369 0.754423 4.44565 0.666626 4.66667 0.666626H14.6667C14.8877 0.666626 15.0996 0.754423 15.2559 0.910704C15.4122 1.06698 15.5 1.27895 15.5 1.49996V13.1666C15.5 13.3876 15.4122 13.5996 15.2559 13.7559C15.0996 13.9122 14.8877 14 14.6667 14H12.1667V16.5C12.1667 16.96 11.7917 17.3333 11.3275 17.3333H1.33917C1.22927 17.334 1.12033 17.3129 1.0186 17.2713C0.916873 17.2297 0.824361 17.1685 0.746382 17.091C0.668403 17.0136 0.606494 16.9215 0.564212 16.8201C0.52193 16.7186 0.500108 16.6099 0.5 16.5L0.5025 4.83329C0.5025 4.37329 0.8775 3.99996 1.34083 3.99996H3.83333ZM2.16833 5.66663L2.16667 15.6666H10.5V5.66663H2.16833ZM5.5 3.99996H12.1667V12.3333H13.8333V2.33329H5.5V3.99996ZM3.83333 8.16663H8.83333V9.83329H3.83333V8.16663ZM3.83333 11.5H8.83333V13.1666H3.83333V11.5Z" fill="black"/>
                                            </svg>
                                            <svg @onclick="() => RemoveRecipe(mobileDayIndex, activeMealType, currentRecipe)" style="margin-right: 10px;" width="17" height="25" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M6.00011 4.82166L10.1251 0.696655L11.3034 1.87499L7.17844 5.99999L11.3034 10.125L10.1251 11.3033L6.00011 7.17832L1.87511 11.3033L0.696777 10.125L4.82178 5.99999L0.696777 1.87499L1.87511 0.696655L6.00011 4.82166Z" fill="black"/>
                                            </svg>
                                        </div>
                                        <div class="controls-bar"></div>
                                    </div>
                                    <div class="">
                                        <div class="pt-1 dish-div">
                                            <span class="dish-name">
                                                @currentRecipe.Name
                                            </span>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <div @onclick="() => OpenSeeRecipe(currentRecipe)" class="pt-2 see-img-sm">
                                                <img class="rounded-3 modal-image" src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, currentRecipe.CloudflareImageId, MiscUtility.ImageUrlSuffixPublic)"
                                                     alt="@currentRecipe.Name"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex flex-row">
                                        <div class="box">
                                            @foreach (var diet in currentRecipe.Diets)
                                            {
                                                GetIconByDietName(diet);
                                                <div class="diet-box-container">
                                                    @*  <div class="diet-type">
                                                    <span>
                                                        @diet
                                                    </span>
                                                </div> *@
                                                    <div class="diet-img">
                                                        <img src="/images/DietIcons/@dietImageName" alt="@dietImageName">
                                                    </div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="dropdown-content-empty-sm">
                            <div class="container">
                                <div class="diet-row">
                                    <div>
                                        <span style="font-size: 11px;"></span>
                                    </div>

                                    <div class="controls-bar"></div>
                                </div>
                                <div class="">
                                    <div class="pt-2 dish-div">
                                        <span class="dish-name">
                                            Looks like you don’t have any recipes selected. Add a recipe now!
                                        </span>
                                    </div>
                                    <div class="pt-4">
                                        <span class="food-type" @onclick="() => OpenAddRecipeDialog(true, mobileDayIndex, activeMealType)">Add</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="load">
                    <Loader/>
                </div>
            }
        </div>

        @*Tab/Laptop-display*@
        <div class="tab-display">
            <div class="menu-container">
                @if (!isLoading && !initializing)
                {
                    for (var menuDayIndex = 0; menuDayIndex < menuDayCount; menuDayIndex++)
                    {
                        var currentDayIndex = menuDayIndex;
                        activeMealTypes.Add(mealTypes[0]);
                        <div class="menu-wrapper-sm">
                            <div class="date-section-sm">
                                <div class="recipe-date-sm">
                                    @selectedUserWeek?.StartDate.Month.@selectedUserWeek?.StartDate.AddDays(currentDayIndex).Day @selectedUserWeek?.StartDate.AddDays(currentDayIndex).ToString("ddd")
                                </div>
                                <div class="date-controls-sm">
                                    <svg class="ps-2" width="25" height="25" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M20.0948 23.5835H3.81442C2.88917 23.5835 2.00182 23.2159 1.34757 22.5617C0.693321 21.9074 0.325768 21.0201 0.325768 20.0948V1.48866C0.325768 1.18024 0.448285 0.884459 0.666368 0.666376C0.884452 0.448293 1.18024 0.325775 1.48865 0.325775H17.769C18.0775 0.325775 18.3732 0.448293 18.5913 0.666376C18.8094 0.884459 18.9319 1.18024 18.9319 1.48866V15.4433H23.5835V20.0948C23.5835 21.0201 23.2159 21.9074 22.5617 22.5617C21.9074 23.2159 21.0201 23.5835 20.0948 23.5835ZM18.9319 17.769V20.0948C18.9319 20.4032 19.0544 20.699 19.2725 20.9171C19.4906 21.1352 19.7864 21.2577 20.0948 21.2577C20.4032 21.2577 20.699 21.1352 20.9171 20.9171C21.1352 20.699 21.2577 20.4032 21.2577 20.0948V17.769H18.9319ZM16.6062 21.2577V2.65154H2.65154V20.0948C2.65154 20.4032 2.77405 20.699 2.99214 20.9171C3.21022 21.1352 3.50601 21.2577 3.81442 21.2577H16.6062ZM4.97731 6.1402H14.2804V8.46597H4.97731V6.1402ZM4.97731 10.7917H14.2804V13.1175H4.97731V10.7917ZM4.97731 15.4433H10.7917V17.769H4.97731V15.4433Z" fill="#CA2F35"/>
                                    </svg>
                                    <svg class="ps-2" width="28" height="28" viewBox="0 0 29 28" fill="none" xmlns="http://www.w3.org/2000/svg" @onclick="() => OpenAddRecipeDialog(true, currentDayIndex, activeMealTypes[currentDayIndex])">
                                        <circle cx="14.8185" cy="13.9546" r="13.9546" fill="black"/>
                                    </svg>
                                    <svg style="position:absolute; right: 13.6px; top: 30px;" width="10" height="9" viewBox="0 0 11 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M5.12077 4.25691V0.0705261H6.51623V4.25691H10.7026V5.65237H6.51623V9.83876H5.12077V5.65237H0.934387V4.25691H5.12077Z" fill="white"/>
                                    </svg>
                                </div>
                            </div>
                            <div class="meal-tabs-sm">
                                <div class="container">
                                    <div class="row">
                                        @{
                                            var idx = 0;
                                        }
                                        @foreach (var mealType in mealTypes)
                                        {
                                            var mealIndex = idx;
                                            <div class="col-3 tab-font tab-font-active" @onclick="() => TabSwitch(mealIndex, currentDayIndex)">@mealType</div>
                                            {
                                                idx++;
                                            }
                                        }
                                    </div>
                                </div>
                            </div>
                            <div class="tab-bar-sm">
                                <div class="bar"></div>
                                <div class="@activeMealTypes[currentDayIndex]-bar-active"></div>
                            </div>
                            @{
                                var recipes = GetCurrentRecipes(currentDayIndex, activeMealTypes[currentDayIndex]);
                            }
                            @if (recipes.Count > 0)
                            {
                                @foreach (var currentRecipe in recipes)
                                {
                                    <div class="dropdown-content-sm">
                                        <div class="container">
                                            <div class="diet-row">
                                                <div>
                                                    <span style="font-size: 11px;"></span>
                                                </div>
                                                <div class="controls-sm">
                                                    @if (currentUser.UserFavorites != null && currentUser.UserFavorites.Where(x => x.RecipeId == currentRecipe.Id).Any())
                                                    {
                                                        <svg @onclick="@(() => ToggleFavorite(currentRecipe))" style="margin-right: 10px; margin-top:-3px;" width="21px" height="21px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--fxemoji heart-icon-adj" preserveAspectRatio="xMidYMid meet">
                                                            <path fill="#FF473E" d="M476.6 133c-53.7-63.5-148.7-66.5-206.3-9.1l-.2.2c-8 8-20.5 8-28.4 0l-.2-.2c-54.6-54.8-143.3-55-198.1-.4C-3.2 169.8-10.9 244.6 25 299.7c11.2 17.3 25.3 30.6 40.9 40.7l180 145.7c6.4 5.2 15.6 5.2 22.1 0l178.8-145.9c15-10 28.6-23 39.5-39.5c34.1-51.3 30.1-120.7-9.7-167.7z"></path>
                                                        </svg>
                                                    }
                                                    else
                                                    {
                                                        <svg width="20" height="19" viewBox="0 0 20 19" style="margin-right: 10px;" @onclick="@(() => ToggleFavorite(currentRecipe))">
                                                            <use href="images/icons.svg#favorite-inactive"/>
                                                        </svg>
                                                    }
                                                    <svg @onclick="() => OpenSeeRecipe(currentRecipe)" style="margin-right: 8px;" width="18" height="20" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M3.83333 3.99996V1.49996C3.83333 1.27895 3.92113 1.06698 4.07741 0.910704C4.23369 0.754423 4.44565 0.666626 4.66667 0.666626H14.6667C14.8877 0.666626 15.0996 0.754423 15.2559 0.910704C15.4122 1.06698 15.5 1.27895 15.5 1.49996V13.1666C15.5 13.3876 15.4122 13.5996 15.2559 13.7559C15.0996 13.9122 14.8877 14 14.6667 14H12.1667V16.5C12.1667 16.96 11.7917 17.3333 11.3275 17.3333H1.33917C1.22927 17.334 1.12033 17.3129 1.0186 17.2713C0.916873 17.2297 0.824361 17.1685 0.746382 17.091C0.668403 17.0136 0.606494 16.9215 0.564212 16.8201C0.52193 16.7186 0.500108 16.6099 0.5 16.5L0.5025 4.83329C0.5025 4.37329 0.8775 3.99996 1.34083 3.99996H3.83333ZM2.16833 5.66663L2.16667 15.6666H10.5V5.66663H2.16833ZM5.5 3.99996H12.1667V12.3333H13.8333V2.33329H5.5V3.99996ZM3.83333 8.16663H8.83333V9.83329H3.83333V8.16663ZM3.83333 11.5H8.83333V13.1666H3.83333V11.5Z" fill="black"/>
                                                    </svg>
                                                    <svg @onclick="() => RemoveRecipe(currentDayIndex, activeMealTypes[currentDayIndex], currentRecipe)" style="margin-right: 10px;" width="17" height="25" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M6.00011 4.82166L10.1251 0.696655L11.3034 1.87499L7.17844 5.99999L11.3034 10.125L10.1251 11.3033L6.00011 7.17832L1.87511 11.3033L0.696777 10.125L4.82178 5.99999L0.696777 1.87499L1.87511 0.696655L6.00011 4.82166Z" fill="black"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="dish-div">
                                                <span class="dish-name">
                                                    @currentRecipe.Name
                                                </span>
                                            </div>
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="pt-2 image-div" @onclick="() => OpenSeeRecipe(currentRecipe)">
                                                        <img class="rounded-3 recipe-image" height="100" width="135" src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, currentRecipe.CloudflareImageId, MiscUtility.ImageUrlSuffixThumbnail)"
                                                             alt="@currentRecipe.Name"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex flex-row">
                                                <div class="box">
                                                    @foreach (var diet in currentRecipe.Diets)
                                                    {
                                                        GetIconByDietName(diet);
                                                        <div class="diet-box-container">
                                                            @*  <div class="diet-type">
                                                                <span>
                                                                    @diet
                                                                </span>
                                                            </div> *@
                                                            <div class="diet-img">
                                                                <img src="/images/DietIcons/@dietImageName" alt="@dietImageName">
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="dropdown-content-empty-tab">
                                    <div class="container">
                                        <div class="diet-row">
                                            <div>
                                                <span style="font-size: 11px;"></span>
                                            </div>
                                        </div>
                                        <div class="">
                                            <div class="pt-2 dish-div">
                                                <span class="dish-name">
                                                    Looks like you don’t have any recipes selected. Add a recipe now!
                                                </span>
                                            </div>
                                            <div class="pt-4">
                                                <span class="food-type" @onclick="() => OpenAddRecipeDialog(true, currentDayIndex, activeMealTypes[currentDayIndex])">Add</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                }
                else
                {
                    <div class="load">
                        <Loader/>
                        <div class="bottom-padding-sm"></div>
                    </div>
                }
            </div>
        </div>
    }

    <div class="bottom-padding-sm"></div>

    @if (openAddRecipeDialog)
    {
        <AddRecipe CurrentUser="@currentUser" Diets="@diets" LogoBrand="@logoBrand"
                   OnClose="OpenAddRecipeDialog" OnRecipeAdded="AddRecipeToMenu" OnSeeRecipe="@(args => OpenSeeRecipe(args))" OnMessageChanged="SetBannerMessage" OnMessageTypeChanged="SetBannerMessageType"/>
    }

    @if (openCustomMenuDialog.openDialog)
    {
        <CustomMenuDialog CurrentUser="@currentUser" IsNewMenu="@openCustomMenuDialog.isNewMenu"
                          OnDialogCanceled="CancelCustomMenuDialog" OnDialogClosed="@(val => CloseCustomMenuDialog(val.Item1, val.Item2))" OnMessageChanged="SetBannerMessage" OnMessageTypeChanged="SetBannerMessageType"/>
    }

    @*Footer component*@
}