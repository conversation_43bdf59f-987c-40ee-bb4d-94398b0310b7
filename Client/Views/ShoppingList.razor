@page "/shopping-list"

@attribute [Authorize]

@using System.Text
@using TwentyDishes.Client.Components.ExportMenuPdfButton
@using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent
@using TwentyDishes.Client.State.MenuGlobalState

@inherits UserBaseComponent

@inject HttpClient HttpClient

<div class="main-header">
    <Header />
</div>

<SideMenu ActivePage="shopping-list" />
 
<Banner @bind-MessageType="@messageType" @bind-Message="@message" />

<div class="second-header">    
     <ExportMenuPdfButton 
        UserWeek="selectedUserWeek" 
        WeekStartDate="selectedUserWeek?.StartDate" 
        IncludeMealPlan="false" 
        IncludePrepGuide="false" 
        IncludeRecipes="false"
        AdditionalIngredients="@(currentUser?.ShoppingListItems?.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate).ToList())"
    >
        <button class="second-header-button"><span class="oi oi-print" style="padding: 0 5px;"></span>Save Shopping List as PDF</button>
    </ExportMenuPdfButton>    
</div>


    <div class="gethelp-section">
        <div class="select-panel">
            <div class="sl-header">
                <span class="sl-header-text">Shopping Categories</span>
            </div>
            <div class="sl-bar"></div>
            <div class="select-pos">
                @{
                    int counter = 0;
                }
                @foreach (string sc in shoppingCategories)
                {
                    int loopCounter = counter;
                    <div onclick="@(() => SetSelectedCategory(sc, loopCounter))" class=@($"select-item {activeItems[loopCounter]}")>
                        <span>@sc</span>
                    </div>
                    counter++;
                    activeItems.Add(" ");
                }
            </div>
        </div>
        <div class="selected-section">
            <div class="cat-header">
                <div class="header-content">
                <div class="cat-text-pos"><h2 class="cat-header-text">@selectedShoppingCategory</h2></div>
                @* @if (!isLoading) {<ExportMenuPdfButton WeekRecipeIdsByDay="selectedUserWeek?.ToRecipeIdsByDay()" WeekStartDate="selectedUserWeek?.StartDate" IncludeMealPlan="false" IncludePrepGuide="false" IncludeRecipes="false"><span class="oi oi-print" style="padding-right: 10px;"></span>Export Shopping List</ExportMenuPdfButton>} *@
                </div>
                <hr class="sep" />
            </div>
            @if (selectedShoppingCategory == IngredientHelper.AdditionalShoppingItems)
            {
                <div class="add-item-container">
                    <div class="add-item-header">
                        <span>Add additional items you need from the store</span>
                    </div>
                    <div class="add-item-inputs">
                        <div class="add-item-name">
                            <label>Ingredient:</label>
                            <input class="add-item-ingr-input" type="text" @bind-value="@newShoppingItem" />
                        </div>
                        <div class="add-item-value-container">
                            <label>Amount:</label>
                            <SfNumericTextBox TValue="decimal" Min="0.01m" Max="1000" Width="225px" CssClass="add-item-value" @bind-Value="@newShoppingItemValue" style="border-color: #000;"></SfNumericTextBox>
                        </div>
                        <div class="add-item-unit">
                            <label>Unit:</label>
                            <input class="add-item-unit-input" type="text" @bind-value="@newShoppingItemUnit" />
                        </div>
                    </div>
                    <div class="add-item-btn-container">
                        <button class="btn-add" @onclick="AddCustomItem">ADD</button>
                    </div>
                </div>
                <hr class="add-item-hr" />
            }
            @if (summedIngredients != null && !isLoading)
            {
                List<SummedIngredient> categoryData = GetCategoryData(selectedShoppingCategory);
                if (categoryData == null || !categoryData.Any())
                {
                    <div class="cat-ingr">
                        <span>No ingredients for this category.</span>
                    </div>
                }
                else
                {
                    <div class="cat-ingr">
                        @foreach (SummedIngredient cd in categoryData)
                        {
                            <span class="formatted-ingr @(ItemIsCompleted(cd) ? "item-completed" : string.Empty)" @onclick=@(async () => await ToggleItemCompletion(cd))>@(FormatShoppingListItem(cd))</span>
                            if (cd.IsCustom)
                            {
                                <a><img class="img-delete" src="images/delete.png" title="Delete" @onclick="@(() => RemoveCustomItem(cd))" /></a>
                            }
                            <br />
                        }
                    </div>
                }
            }
            else
            {
                <div class="load">
                    <Loader />
                </div>
            }
        </div>
    </div>

    <div class="shopping-sm">
        <div class="header-shopping-sm">
            <h5>Shopping List</h5>
            <div class="export-btn-adj">
                <ExportMenuPdfButton 
                    UserWeek="selectedUserWeek" 
                    WeekStartDate="selectedUserWeek?.StartDate" 
                    IncludeMealPlan="false" 
                    IncludePrepGuide="false" 
                    IncludeRecipes="false"
                    AdditionalIngredients="@(currentUser?.ShoppingListItems?.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate).ToList())"
                >
                    <span class="food-type">Save as PDF</span>
                </ExportMenuPdfButton>
            </div>
        </div>

        <div class="menu-wrapper-sm">
            <div class="select-wrap">
                <label for="categories">Choose Category:</label>
                <div class="select-container" @onclick="ToggleCategorySelection">
                    <span style="font-family: 'Raleway', sans-serif;">Select<i class="dropdown-toggle tog-pos"></i></span>
                </div>
                <div class="option-container" style=@($"display: {categorySelectionDisplay};")>
                    @{
                        int counterSm = 0;
                    }
                    @foreach (string sc in shoppingCategories)
                    {
                        int loopCounter = counterSm;
                        <div @onclick="@(() => SetSelectedCategory(sc, loopCounter))" class="option-wrap">
                            <span class="option-style">@sc</span>
                            <hr />
                        </div>
                    }
                </div>
            </div>
            <hr />
            @if (selectedShoppingCategory == IngredientHelper.AdditionalShoppingItems)
            {
                <div class="add-item-container">
                    <div class="add-item-header">
                        <span>Add additional items you need from the store</span>
                    </div>
                    <div class="add-item-inputs">
                        <div class="add-item-name">
                            <label>Ingredient:</label>
                            <input class="add-item-ingr-input" type="text" @bind-value="@newShoppingItem" />
                        </div>
                        <div class="add-item-value-container">
                            <label>Amount:</label>
                            <SfNumericTextBox TValue="decimal" Min="0.01m" Max="1000" Width="225px" CssClass="add-item-value" @bind-Value="@newShoppingItemValue" style="border-color: #000;"></SfNumericTextBox>
                        </div>
                        <div class="add-item-unit">
                            <label>Unit:</label>
                            <input class="add-item-unit-input" type="text" @bind-value="@newShoppingItemUnit" />
                        </div>
                    </div>
                    <div class="add-item-btn-container">
                        <button class="btn-add" @onclick="AddCustomItem">ADD</button>
                    </div>
                </div>
                <hr class="add-item-hr" />
            }
            @if (summedIngredients != null && !isLoading)
            {
                List<SummedIngredient> categoryData = GetCategoryData(selectedShoppingCategory);
                if (categoryData == null || !categoryData.Any())
                {
                    if (selectedShoppingCategory == IngredientHelper.AdditionalShoppingItems)
                    {
                        <br />
                    }
                    else
                    {
                        <div class="select-cat">
                            <h6>@selectedShoppingCategory</h6>
                        </div>
                        <div class="cat-ingr">
                            <span class="pt-2">No ingredients for this category.</span>
                        </div>
                    }
                }
                else
                {
                    <div class="select-cat @(selectedShoppingCategory == IngredientHelper.AdditionalShoppingItems ? "add-items-sm-top" : string.Empty)">
                        <h6>@selectedShoppingCategory</h6>
                    </div>
                    <div class="cat-ingr @(selectedShoppingCategory == IngredientHelper.AdditionalShoppingItems ? "add-items-sm-top" : string.Empty)">
                        @foreach (SummedIngredient cd in categoryData)
                        {
                            <span class="formatted-ingr @(ItemIsCompleted(cd) ? "item-completed" : string.Empty)" @onclick=@(async () => await ToggleItemCompletion(cd))>@(FormatShoppingListItem(cd))</span>
                            if (cd.IsCustom)
                            {
                                <a><img class="img-delete" src="images/delete.png" title="Delete" @onclick="@(() => RemoveCustomItem(cd))" /></a>
                            }
                            <br />
                        }
                    </div>
                }
            }
            else
            {
                <div class="load">
                    <Loader />
                </div>
            }
        </div>
    </div>

@code {
    private List<string> activeItems = new List<string>() { "select-item-selected" };
    private string categorySelectionDisplay = "none";
    private User currentUser;
    Dictionary<string, string> ingrShoppingCategories;
    private bool isCategorySelectionOpen = true;
    private bool isLoading = true;
    private MathHelper mathHelper = new MathHelper();
    string message;
    MiscUtility.AlertMessageType messageType;
    private string newShoppingItem = string.Empty;
    private string newShoppingItemUnit = string.Empty;
    private decimal newShoppingItemValue = 1m;
    private string selectedShoppingCategory = string.Empty;
    private UserWeek selectedUserWeek;
    private List<Recipe> selectedWeekRecipes;
    private List<ShoppingListItem> selectedWeekShoppingItems = new List<ShoppingListItem>();
    private List<string> shoppingCategories = new List<string>();
    private List<SummedIngredient> summedIngredients;

    [CascadingParameter(Name = MenuGlobalStateFields.Value)]
    public MenuGlobalStateValue MenuGlobalStateValue {get; set;}

    [CascadingParameter(Name = MenuGlobalStateFields.SetShoppingListItems)]
    public EventCallback<List<ShoppingListItem>?> SetShoppingListitems {get; set;}

    public async Task Initialize()
    {
        currentUser = userGlobalState.FullUserData;
        // selectedWeekRecipes = await SessionStorage.GetItemAsync<List<Recipe>>("SelectedWeekRecipes");
        selectedWeekRecipes = MenuGlobalStateValue.SelectedWeekRecipes;
        shoppingCategories = await HttpClient.GetFromJsonAsync<List<string>>("/api/ShoppingCategories") ?? new List<string>();
        // selectedUserWeek = await SessionStorage.GetItemAsync<UserWeek>("SelectedUserWeek");
        selectedUserWeek = MenuGlobalStateValue.SelectedUserWeek;
        await FormatShoppingListData(false);
        selectedShoppingCategory = shoppingCategories.FirstOrDefault();
        await CheckForOldShoppingLists();
        isLoading = false;
    }

    public override async Task GlobalStateLoaded()
    {
        if (!initialized && !initializationStarted)
        {
            await Initialize();
        }
    }

    private async Task AddCustomItem()
    {
        if (!string.IsNullOrWhiteSpace(newShoppingItem))
        {
            isLoading = true;
            //Adjust nonsense values
            if (newShoppingItemValue <= 0)
            {
                newShoppingItemValue = 1;
            }

            ShoppingListItem newItem = new ShoppingListItem()
                {
                    Amount = new MathHelper().RealToFraction(newShoppingItemValue, 0.01m).ToString(),
                    AmountUnit = newShoppingItemUnit,
                    IngredientName = newShoppingItem + "-" + IngredientHelper.AdditionalShoppingItems,
                    IsCustom = true,
                    ScheduleDate = selectedUserWeek.StartDate
                };
            bool itemExists = currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate && x.IngredientName.ToUpper() == newItem.IngredientName.ToUpper() && x.AmountUnit == newItem.AmountUnit).Any();

            if (!itemExists)
            {
                currentUser.ShoppingListItems.Add(newItem);
            }
            else
            {
                ShoppingListItem existingItem = currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate && x.IngredientName.ToUpper() == newItem.IngredientName.ToUpper() && x.AmountUnit == newItem.AmountUnit).FirstOrDefault();
                int existingItemIdx = currentUser.ShoppingListItems.FindIndex(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate && x.IngredientName.ToUpper() == newItem.IngredientName.ToUpper() && x.AmountUnit == newItem.AmountUnit);
                decimal combinedAmount = MathHelper.FractionToDecimal(existingItem.Amount) + MathHelper.FractionToDecimal(newItem.Amount);
                existingItem.Amount = new MathHelper().RealToFraction(combinedAmount, 0.01m).ToString();
                currentUser.ShoppingListItems[existingItemIdx] = existingItem;
            }

            await SaveUser();

            newShoppingItem = string.Empty;
            newShoppingItemUnit = string.Empty;
            newShoppingItemValue = 1;

            await FormatShoppingListData(true);
            selectedShoppingCategory = IngredientHelper.AdditionalShoppingItems;
            StateHasChanged();
            isLoading = false;
        }
    }

    private async Task CheckForOldShoppingLists()
    {
        //Delete shopping list items that are 2 weeks old or older, so that we can save space in the database
        if (currentUser.ShoppingListItems != null)
        {
            int itemsRemoved = currentUser.ShoppingListItems.RemoveAll(x => x.ScheduleDate <= DateTime.Now.Date.AddDays(-14));
            if (itemsRemoved > 0)
            {
                await SaveUser();
            }
        }
    }


    private async Task FormatShoppingListData(bool fromCustom)
    {
        await GetIngrShoppingCategories();
        await SumTotalIngredients(fromCustom);
    }

    private string FormatShoppingListItem(SummedIngredient si)
    {
        if (si.IsCustom)
        {
            //Clone object to break object reference
            SummedIngredient siClone = new SummedIngredient()
                {
                    Amount = si.Amount,
                    AmountUnit = si.AmountUnit,
                    IngredientName = si.IngredientName.Replace("-" + IngredientHelper.AdditionalShoppingItems, string.Empty, StringComparison.InvariantCultureIgnoreCase),
                    IsCustom = si.IsCustom
                };
            return IngredientHelper.FormatIngredientString(siClone, 25);
        }
        else
        {
            return IngredientHelper.FormatIngredientString(si, 25);
        }
    }

    private List<SummedIngredient> GetCategoryData(string shoppingCategory)
    {
        List<string> ingrInCategory = ingrShoppingCategories.Where(x => x.Value == shoppingCategory).Select(x => x.Key.ToUpper()).ToList();
        return summedIngredients.Where(x => ingrInCategory.Contains(x.IngredientName.ToUpper())).Select(x => x).ToList();
    }

    private async Task GetIngrShoppingCategories()
    {
        HttpResponseMessage response = await HttpClient.PostAsJsonAsync<List<Recipe>>("/api/IngredientShoppingCategories", selectedWeekRecipes);

        if (response.IsSuccessStatusCode)
        {
            ingrShoppingCategories = await response.Content.ReadFromJsonAsync<Dictionary<string, string>>() ?? new Dictionary<string, string>();

            //Add custom additional items
            if (currentUser.ShoppingListItems != null && currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate) != null)
            {
                foreach (ShoppingListItem sli in currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate))
                {
                    if (!ingrShoppingCategories.ContainsKey(sli.IngredientName))
                    {
                        ingrShoppingCategories.Add(sli.IngredientName, IngredientHelper.AdditionalShoppingItems);
                    }
                }
            }
        }
        else
        {
            message = await response.Content.ReadAsStringAsync();
            messageType = MiscUtility.AlertMessageType.Error;
        }
    }

    private bool ItemIsCompleted(SummedIngredient si)
    {
        return currentUser.ShoppingListItems.Where(x => x.ScheduleDate == selectedUserWeek.StartDate && x.IngredientName.ToUpper() == si.IngredientName.ToUpper() && x.AmountUnit == si.AmountUnit)
                                            .Select(x => x.IsCompleted)
                                            .FirstOrDefault();
    }

    private async Task RemoveCustomItem(SummedIngredient si)
    {
        isLoading = true;
        bool itemExists = currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate && x.IngredientName.ToUpper() == si.IngredientName.ToUpper() && x.AmountUnit == si.AmountUnit).Any();

        if (itemExists)
        {
            int existingItemIdx = currentUser.ShoppingListItems.FindIndex(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate && x.IngredientName.ToUpper() == si.IngredientName.ToUpper() && x.AmountUnit == si.AmountUnit);
            currentUser.ShoppingListItems.Remove(currentUser.ShoppingListItems[existingItemIdx]);
            await SaveUser();
        }

        await FormatShoppingListData(true);
        selectedShoppingCategory = IngredientHelper.AdditionalShoppingItems;
        StateHasChanged();
        isLoading = false;
    }

    private async Task ResetWeeklyShoppingListItems(bool fromCustom)
    {
        selectedWeekShoppingItems = new List<ShoppingListItem>();

        foreach (SummedIngredient si in summedIngredients)
        {
            ShoppingListItem sli = new ShoppingListItem()
                {
                    Amount = mathHelper.RealToFraction(si.Amount, 0.01m).ToString(),
                    AmountUnit = si.AmountUnit,
                    IngredientName = si.IngredientName,
                    IsCompleted = false,
                    IsCustom = si.IsCustom,
                    ScheduleDate = selectedUserWeek.StartDate
                };
            selectedWeekShoppingItems.Add(sli);
        }

        // await SessionStorage.SetItemAsync("ShoppingListItems", selectedWeekShoppingItems);
        await SetShoppingListitems.InvokeAsync(selectedWeekShoppingItems);

        if (currentUser.ShoppingListItems == null)
        {
            currentUser.ShoppingListItems = new List<ShoppingListItem>();
        }

        //Add the whole collection because no shopping list items exist for the selected date
        if (!currentUser.ShoppingListItems.Where(x => x.ScheduleDate == selectedUserWeek.StartDate).Any())
        {
            foreach (ShoppingListItem sli in selectedWeekShoppingItems)
            {
                currentUser.ShoppingListItems.Add(sli);
            }
            await SaveUser();
        }
        else if (currentUser.ShoppingListItems.Where(x => x.ScheduleDate == selectedUserWeek.StartDate && !selectedWeekShoppingItems.Select(x => x.IngredientName).Contains(x.IngredientName)).Any() && !fromCustom)
        {
            //Something changed in the shopping list, so remove all items from the selected date and re-add everything from that date. Don't remove custom items.
            currentUser.ShoppingListItems.RemoveAll(x => x.ScheduleDate == selectedUserWeek.StartDate && !x.IsCustom);
            foreach (ShoppingListItem sli in selectedWeekShoppingItems)
            {
                currentUser.ShoppingListItems.Add(sli);
            }
            await SaveUser();
        }
    }

    private async Task SaveUser()
    {
        HttpResponseMessage response = await HttpClient.PutAsJsonAsync<User>("/api/UpdateUser", currentUser);

        if (!response.IsSuccessStatusCode)
        {
            message = "An error occurred while trying to save your menu: " + Environment.NewLine + await response.Content.ReadAsStringAsync();
            messageType = MiscUtility.AlertMessageType.Error;
        }
    }

    private void SetSelectedCategory(string selectedCategory, int count)
    {
        selectedShoppingCategory = selectedCategory;
        for (int i = 0; i < activeItems.Count; i++)
        {
            activeItems[i] = " ";
        }
        activeItems[count] = "select-item-selected";
        StateHasChanged();
        ToggleCategorySelection();
    }

    private async Task SumTotalIngredients(bool fromCustom)
    {
        summedIngredients = IngredientHelper.SumTotalIngredients(selectedWeekRecipes);

        //Add custom additional items
        if (currentUser.ShoppingListItems != null && currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate) != null)
        {
            foreach (ShoppingListItem sli in currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate))
            {
                SummedIngredient si = new SummedIngredient()
                    {
                        Amount = MathHelper.FractionToDecimal(sli.Amount),
                        AmountUnit = sli.AmountUnit,
                        IngredientName = sli.IngredientName,
                        IsCustom = sli.IsCustom
                    };
                summedIngredients.Add(si);
            }
        }

        //See if shopping list items already exist in the Session
        selectedWeekShoppingItems = MenuGlobalStateValue.ShoppingListItems;

        //If the shopping list items in the Session are from another date, Reset the list in Session
        if (selectedWeekShoppingItems == null ||
            (selectedWeekShoppingItems.Any() && selectedWeekShoppingItems.First().ScheduleDate != selectedUserWeek.StartDate))
        {
            await ResetWeeklyShoppingListItems(fromCustom);
            return;
        }

        //If an ingredient is missing from the list or an amount has changed, then reset the shopping list items in Session
        foreach (SummedIngredient si in summedIngredients)
        {
            if (!selectedWeekShoppingItems.Where(x => x.IngredientName.ToUpper() == si.IngredientName.ToUpper()).Any()
                || selectedWeekShoppingItems.Where(x => x.IngredientName == si.IngredientName && x.AmountUnit == si.AmountUnit).FirstOrDefault().Amount != mathHelper.RealToFraction(si.Amount, 0.01m).ToString())
            {
                await ResetWeeklyShoppingListItems(fromCustom);
                return;
            }
        }
    }

    private void ToggleCategorySelection()
    {
        if (isCategorySelectionOpen)
        {
            isCategorySelectionOpen = false;
            categorySelectionDisplay = "none";
        }
        else
        {
            isCategorySelectionOpen = true;
            categorySelectionDisplay = "block";
        }
    }

    private async Task ToggleItemCompletion(SummedIngredient si)
    {
        int sliIndex = currentUser.ShoppingListItems.IndexOf(currentUser.ShoppingListItems.Where(x => x.ScheduleDate == selectedUserWeek.StartDate && x.IngredientName.ToUpper() == si.IngredientName.ToUpper() && x.AmountUnit == si.AmountUnit).FirstOrDefault());
        if (sliIndex != -1)
        {
            currentUser.ShoppingListItems[sliIndex].IsCompleted = !currentUser.ShoppingListItems[sliIndex].IsCompleted;
        }
        await SaveUser();
        StateHasChanged();
    }
}