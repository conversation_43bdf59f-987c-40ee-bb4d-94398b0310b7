@page "/prep-guide"
@attribute [Authorize]

@using System.Text
@using TwentyDishes.Client.Components.ExportMenuPdfButton
@using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent
@using TwentyDishes.Client.State.MenuGlobalState

@inherits UserBaseComponent

@inject HttpClient HttpClient

<div class="main-header">
    <Header />
</div>

<SideMenu ActivePage="prep-guide" />

<div class="second-header">
    <ExportMenuPdfButton UserWeek="selectedUserWeek" WeekStartDate="selectedUserWeek?.StartDate" IncludeMealPlan="false" IncludeRecipes="false" IncludeShoppingList="false"><button class="second-header-button"><span class="oi oi-print" style="padding: 0 5px;"></span>Save Prep Guide as PDF</button></ExportMenuPdfButton>
</div>

    <div class="prep-guide-main">
        <div class="prep-text-pos">
            <span class="prep-text">Prep Guide</span>
            <div class="export-btn-adj">
            <ExportMenuPdfButton UserWeek="selectedUserWeek" WeekStartDate="selectedUserWeek?.StartDate" IncludeMealPlan="false" IncludeRecipes="false" IncludeShoppingList="false"><span class="food-type">Save as PDF</span></ExportMenuPdfButton>
            </div>
            @* @if (!isLoading) {<ExportMenuPdfButton WeekRecipeIdsByDay="selectedUserWeek?.ToRecipeIdsByDay()" WeekStartDate="selectedUserWeek?.StartDate" IncludeMealPlan="false" IncludeRecipes="false" IncludeShoppingList="false"><span class="oi oi-print" style="padding-right: 10px;"></span>Export Prep Guide</ExportMenuPdfButton>} *@
        </div>
        @if (selectedWeekRecipes != null)
        {
            <div class="prep-section"> 
                <div class="prep-info">
                    <span class="info-heading">
                        Take out the ingredients for this week’s recipes:
                    </span>
                    <div class="info-mise">
                        @foreach (WeeklyRecipeItem wri in currentUser.WeeklyRecipePrepItems)
                        {
                            <div class="info-para-sec">
                                @if (selectedUserWeek.StartDate.Date == DateTimeHelper.GetFirstDateInWeek().Date)
                                {
                                    <input class="cursor-pointer" type="checkbox" checked="@wri.IsCompleted" @onchange="@(args => ToggleIngrPrepCompletion(wri, args))" />
                                    <span class="p-3 @(wri.IsCompleted ? "item-completed" : string.Empty)">@FormatMiseEnPlaceItem(wri)</span>
                                }
                                else
                                {
                                    <input disabled type="checkbox" title="You can only complete items for the current week." />
                                    <span class="p-3">@FormatMiseEnPlaceItem(wri)</span>
                                }
                            </div>
                        }
                    </div>
                </div>
                <div class="prep-info">
                    <span class="info-heading">
                        Label your storage bags and/or jars with these recipes:
                    </span>
                    <div class="info-para">
                        @foreach (RecipePrepStorage rps in currentUser.WeeklyRecipePrepStorage)
                        {
                            <div class="info-para-sec">
                                @if (selectedUserWeek.StartDate.Date == DateTimeHelper.GetFirstDateInWeek().Date)
                                {
                                    <input class="cursor-pointer" type="checkbox" checked="@rps.IsCompleted" @onchange="@(args => ToggleRecipeCompletion(rps, args))" />
                                    <span class="p-3 @(rps.IsCompleted ? "item-completed" : string.Empty)">@rps.RecipeName</span>
                                }
                                else
                                {
                                    <span class="p-3">@rps.RecipeName</span>
                                    <input disabled type="checkbox" title="You can only complete items for the current week." />
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
            <div class="prep-table">
                <div class="billing-hist-header">
                    <span class="header-item-250">Instructions</span>
                    <span class="header-item-200">Recipes & Ingredients</span>
                    <span class="header-item-80">Done</span>
                </div>
                @foreach (KeyValuePair<string, List<string>> step in assembledSteps)
                {
                    <div class="prep-table-data">
                        <div class="prep-instructions">
                            <span class="smt"> 
                                @step.Key @*step description*@
                            </span>
                        </div>
                        <div class="prep-recipe">
                            <div>
                                @* @string.Join(", ", step.Value) *@ @*list of recipes that share the same step, concatenated - comma separated*@
                            @{                                
                                UnorderedRecipeInstructions(step.Value);
                            }
                                <ul>
                                   @foreach (var item in RecipesInstructionList)
                                   {
                                    <li>@item</li>
                                   }                                   
                                </ul>
                            </div>
                        </div>
                        <div class="prep-done">
                            @if (selectedUserWeek?.StartDate.Date == DateTimeHelper.GetFirstDateInWeek().Date)
                            {
                                bool isChecked = currentUser.WeeklyRecipeSteps.Where(x => x.Description == step.Key).Select(x => x.IsCompleted).FirstOrDefault();
                                <input type="checkbox"
                                       class="cursor-pointer"
                                       checked="@isChecked"
                                @onchange="@((args) => ToggleStepCompletion(step.Key, args))" />
                            }
                            else
                            {
                                <input disabled type="checkbox" title="You can only complete items for the current week." />
                            }
                        </div>
                    </div>
                }
            </div>
            <div class="table-header-sm">
                <h2>Prep-Guide Table</h2>            
            </div>
            @foreach (KeyValuePair<string, List<string>> step in assembledSteps)
            {
                <div class="prep-table-sm">
                    <div class="data-row">
                        <h6 class="fw-bold">Instructions:</h6>
                        <div class="para-sm">
                            <h6>
                                @step.Key
                            </h6>
                        </div>
                    </div>
                    <div class="data-row">
                        <h6 class="fw-bold">Recipe & Ingredients:</h6>
                        <div class="para-sm">
                            <h6>
                                @string.Join(", ", step.Value)
                            </h6>
                        </div>
                    </div>
                    <div class="data-row">
                        <h6 class="fw-bold">Done</h6>
                        <div>
                            @if (selectedUserWeek.StartDate.Date == DateTimeHelper.GetFirstDateInWeek().Date)
                            {
                                bool isChecked = currentUser.WeeklyRecipeSteps.Where(x => x.Description == step.Key).Select(x => x.IsCompleted).FirstOrDefault();
                                <input type="checkbox"
                                       checked="@isChecked"
                                @onchange="@((args) => ToggleStepCompletion(step.Key, args))" />
                            }
                            else
                            {
                                <input disabled type="checkbox" />
                            }
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div style="height:1000px;">
                <Loader />
            </div>
        }
    </div>

@code {
    private Dictionary<string, List<string>> assembledSteps = new Dictionary<string, List<string>>();
    private User currentUser;
    private bool isLoading = true;
    private MathHelper mathHelper = new MathHelper();
    private UserWeek selectedUserWeek;
    private List<Recipe> selectedWeekRecipes;
    private List<SummedIngredient> summedIngredients;
    public List<string> RecipesInstructionList { get; set; } = new List<string>();

    [CascadingParameter(Name = MenuGlobalStateFields.Value)]
    public MenuGlobalStateValue MenuGlobalStateValue {get; set;}

    public async Task Initialize()
    {
        initializationStarted = true;
        isLoading = true;

        currentUser = userGlobalState.FullUserData;
        // selectedUserWeek = await SessionStorage.GetItemAsync<UserWeek>("SelectedUserWeek");
        selectedUserWeek = MenuGlobalStateValue.SelectedUserWeek;
        // selectedWeekRecipes = await SessionStorage.GetItemAsync<List<Recipe>>("SelectedWeekRecipes");
        selectedWeekRecipes = MenuGlobalStateValue.SelectedWeekRecipes;
        await InvokeAsync(() => SumTotalIngredients());
        await InvokeAsync(() => AssembleSteps());
        await PrepareChecklist();

        isLoading = false;
        initialized = true;
        initializationStarted = false;
    }

    public override async Task GlobalStateLoaded()
    {
        if (!initialized && !initializationStarted)
        {
            await Initialize();
        }
    }

    private void AssembleSteps()
    {
        //Looking for the a generic note that is common in many recipes and adding it as the first key in the dictionary, so it gets sorted at the top of the dictionary
        string genericNote = selectedWeekRecipes.Where(x => x.Steps.Where(y => y.Description.Contains("Please Note:")).Any()).Select(x => x.Steps.Select(y => y.Description).FirstOrDefault()).FirstOrDefault();
        if (!string.IsNullOrWhiteSpace(genericNote))
        {
            assembledSteps.Add(genericNote, new List<string>());
        }

        //Assemble dictionary
        foreach (Recipe recipe in selectedWeekRecipes)
        {
            foreach (Step step in recipe.Steps)
            {
                string stepDesc = step.Description.Replace("[", string.Empty).Replace("]", string.Empty);

                if (!assembledSteps.ContainsKey(stepDesc))
                {
                    List<string> recipeList = new List<string>() { recipe.Name };
                    assembledSteps.Add(stepDesc, recipeList);
                }
                else
                {
                    if (!assembledSteps[stepDesc].Contains(recipe.Name))
                    {
                        assembledSteps[stepDesc].Add(recipe.Name);
                    }
                }
            }
        }
        //Sort recipe names in dictionary
        foreach (KeyValuePair<string, List<string>> values in assembledSteps)
        {
            assembledSteps[values.Key].OrderBy(x => x);
        }
    }
    private void UnorderedRecipeInstructions(List<string> ins)
    {
        RecipesInstructionList.Clear();
        foreach (var item in ins)
        {
            RecipesInstructionList.Add(item);
        }
    }
    private void CompileMiseEnPlace()
    {
        if (currentUser.WeeklyRecipePrepItems == null || !currentUser.WeeklyRecipePrepItems.Any() || currentUser.WeeklyRecipePrepItems.First().ScheduleDate.Date != DateTimeHelper.GetFirstDateInWeek().Date)
        {
            currentUser.WeeklyRecipePrepItems = new List<WeeklyRecipeItem>();

            foreach (SummedIngredient si in summedIngredients)
            {
                currentUser.WeeklyRecipePrepItems.Add(new WeeklyRecipeItem()
                    {
                        Amount = mathHelper.RealToFraction(si.Amount, 0.01m).ToString(),
                        AmountUnit = si.AmountUnit,
                        IngredientName = si.IngredientName,
                        IsCompleted = false,
                        ScheduleDate = selectedUserWeek.StartDate
                    });
            }
        }
    }

    private string FormatMiseEnPlaceItem(WeeklyRecipeItem weeklyRecipeItem)
    {
        StringBuilder sb = new StringBuilder(25);

        if (!string.IsNullOrWhiteSpace(weeklyRecipeItem.Amount) && weeklyRecipeItem.Amount != "0")
        {
            sb.Append(weeklyRecipeItem.Amount)
              .Append(" ")
              .Append(weeklyRecipeItem.AmountUnit)
              .Append(" ")
              .Append(weeklyRecipeItem.IngredientName);

            return MiscUtility.ReplaceHtmlTags(sb.ToString());
        }
        else
        {
            return MiscUtility.ReplaceHtmlTags(weeklyRecipeItem.IngredientName);
        }
    }

    private void GetWeeklyRecipePrepStorage()
    {
        if (currentUser.WeeklyRecipePrepStorage == null || !currentUser.WeeklyRecipePrepStorage.Any() || currentUser.WeeklyRecipePrepStorage.First().ScheduleDate.Date != DateTimeHelper.GetFirstDateInWeek().Date)
        {
            currentUser.WeeklyRecipePrepStorage = selectedWeekRecipes.Select(x => new RecipePrepStorage { RecipeName = x.Name, ScheduleDate = selectedUserWeek.StartDate })
                                                                 .OrderBy(x => x.RecipeName).ToList();
        }
    }

    private bool GetWeeklyRecipeSteps()
    {
        bool wasUpdated = false;
        //Only modify WeeklyRecipeSteps if it is null or if the date of the records is not current. Checklists from the past will be erased.
        if (currentUser.WeeklyRecipeSteps == null || !currentUser.WeeklyRecipeSteps.Any() || currentUser.WeeklyRecipeSteps.First().WeekStartDate.Date != DateTimeHelper.GetFirstDateInWeek().Date)
        {
            currentUser.WeeklyRecipeSteps = new List<UserStep>();

            foreach (Recipe recipe in selectedWeekRecipes)
            {
                foreach (Step step in recipe.Steps)
                {
                    UserStep userStep = new UserStep()
                        {
                            Description = step.Description.Replace("[", string.Empty).Replace("]", string.Empty),
                            Priority = step.Priority,
                            WeekStartDate = selectedUserWeek.StartDate.Date
                        };
                    currentUser.WeeklyRecipeSteps.Add(userStep);
                }
            }
            wasUpdated = true;
        }
        return wasUpdated;
    }

    private async Task PrepareChecklist()
    {
        bool stepsUpdated = GetWeeklyRecipeSteps();
        await InvokeAsync(() => CompileMiseEnPlace());
        await InvokeAsync(() => GetWeeklyRecipePrepStorage());

        //Only save the checklist for the current week. Not historic or future weeks.
        if (stepsUpdated && selectedUserWeek.StartDate.Date == DateTimeHelper.GetFirstDateInWeek().Date)
        {
            await UpdateUser();
        }
    }

    private void SumTotalIngredients()
    {
        summedIngredients = IngredientHelper.SumTotalIngredients(selectedWeekRecipes);
    }

    private async Task ToggleIngrPrepCompletion(WeeklyRecipeItem weeklyRecipeItem, ChangeEventArgs args)
    {
        List<int> modifiedIndexes = currentUser.WeeklyRecipePrepItems.Select((value, index) => new { value, index })
                                                                       .Where(x => x.value.IngredientName.ToUpper() == weeklyRecipeItem.IngredientName.ToUpper()
                                                                              && x.value.AmountUnit == weeklyRecipeItem.AmountUnit
                                                                              && x.value.ScheduleDate == weeklyRecipeItem.ScheduleDate)
                                                                       .Select(x => x.index).ToList();

        foreach (int idx in modifiedIndexes)
        {
            if (idx != -1)
            {
                currentUser.WeeklyRecipePrepItems[idx].IsCompleted = (bool)args.Value;
            }
        }
        await UpdateUser();
    }

    private async Task ToggleRecipeCompletion(RecipePrepStorage recipePrepStorage, ChangeEventArgs args)
    {
        List<int> modifiedIndexes = currentUser.WeeklyRecipePrepStorage.Select((value, index) => new { value, index })
                                                                       .Where(x => x.value.RecipeName == recipePrepStorage.RecipeName)
                                                                       .Select(x => x.index).ToList();

        foreach (int idx in modifiedIndexes)
        {
            if (idx != -1)
            {
                currentUser.WeeklyRecipePrepStorage[idx].IsCompleted = (bool)args.Value;
            }
        }
        await UpdateUser();
    }

    private async Task ToggleStepCompletion(string stepDesc, ChangeEventArgs args)
    {
        List<int> modifiedIndexes = currentUser.WeeklyRecipeSteps.Select((value, index) => new { value, index })
                                                                     .Where(x => x.value.Description == stepDesc)
                                                                     .Select(x => x.index).ToList();

        foreach (int idx in modifiedIndexes)
        {
            if (idx != -1)
            {
                currentUser.WeeklyRecipeSteps[idx].IsCompleted = (bool)args.Value;
            }
        }
        await UpdateUser();
    }

    private async Task UpdateUser()
    {
        isLoading = true;
        HttpResponseMessage response = await HttpClient.PutAsJsonAsync<User>("/api/UpdateUser", currentUser);
        isLoading = false;
    }
}