using Microsoft.JSInterop;
using TwentyDishes.Client.Helpers;

namespace TwentyDishes.Client.Services.BreakingErrorService
{
    public class BreakingErrorService : IBreakingErrorService
    {
        public IJSRuntime _jSRuntime { get; set; } = default!;

        public BreakingErrorService(IJSRuntime jSRuntime)
        {
            _jSRuntime = jSRuntime;
        }

        public async Task ShowBreakingError(BreakingError error, string? errorDetailedMessage = null, string? additionalErrorDetail = null)
        {
            await _jSRuntime.InvokeVoidAsync("ShowBreakingError", error.ErrorCode.ToString(), error.Title, error.Description, errorDetailedMessage, additionalErrorDetail);
        }
    }
}