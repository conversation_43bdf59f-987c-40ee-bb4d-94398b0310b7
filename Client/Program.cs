using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Syncfusion.Blazor;
using System.Text.Json;
using TwentyDishes.Client;
using TwentyDishes.Client.Services;
using TwentyDishes.Client.Services.BreakingErrorService;
using TwentyDishes.Client.Services.ServiceInterfaces;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Mgo+DSMBaFt6QHFqVkNrXVNbdV5dVGpAd0N3RGlcdlR1fUUmHVdTRHRbQlljQH9QckBnXHlccn0=;Mgo+DSMBPh8sVXJxS0d+X1RPd11dXmJWd1p/THNYflR1fV9DaUwxOX1dQl9nSXpTf0RgXXpadXJWTmU=;Mgo+DSMBMAY9C3t2UFhhQlJBfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTX5XdkxiWntbdHFTRWhZ;MzIxMjcxMkAzMjM1MmUzMDJlMzBVb1R2aGpEOERidVFqYXoyV0pwd1JFbHFqeXY4Mk04VTcxMEMvbmI4cHkwPQ==");

builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

builder.Services.AddTransient<CustomAuthorizationMessageHandler>();

//Configure HttpClient for API calls that don't require an access token. This helps avoid the problem with AccessTokenNotAvailableExceptions
builder.Services.AddHttpClient<HttpClientAnon>(client => client.BaseAddress = new Uri((builder.HostEnvironment.IsDevelopment() ? builder.Configuration["API_Prefix"] : builder.HostEnvironment.BaseAddress)!));
//Configure HttpClient for Auth0 use, includes bearer token config
builder.Services.AddHttpClient<HttpClientAuth0>(client => client.BaseAddress = new Uri(builder.Configuration["Auth0:Authority"]!))
                .AddHttpMessageHandler(sp =>
                {
                    CustomAuthorizationMessageHandler messageHandler = sp.GetService<CustomAuthorizationMessageHandler>()
                    .ConfigureHandler(
                        authorizedUrls: new[] { builder.Configuration["Auth0:Authority"] }!
                    );
                    return messageHandler;
                });
//Configure HttpClient for 20 Dishes use, includes bearer token config
builder.Services.AddHttpClient("MainClient", client => client.BaseAddress = new Uri(builder.HostEnvironment.IsDevelopment() ? builder.Configuration["API_Prefix"] : builder.HostEnvironment.BaseAddress))
                .AddHttpMessageHandler(sp =>
                {
                    CustomAuthorizationMessageHandler messageHandler = sp.GetService<CustomAuthorizationMessageHandler>()
                    .ConfigureHandler(
                        authorizedUrls: new[] { builder.HostEnvironment.IsDevelopment() ? builder.Configuration["API_Prefix"] : builder.HostEnvironment.BaseAddress },
                        null,
                        null,
                        "X-app-Authorization"
                    );
                    return messageHandler;
                });
builder.Services.AddScoped(sp => sp.GetRequiredService<IHttpClientFactory>().CreateClient("MainClient"));
builder.Services.AddHttpClient("StaticFileClient", client => client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress));
builder.Services.AddHttpClient("Auth0Client", client => client.BaseAddress = new Uri(builder.Configuration["Auth0:Authority"]));
builder.Services.AddOidcAuthentication(options =>
{
    builder.Configuration.Bind("Auth0", options.ProviderOptions);
    options.ProviderOptions.ResponseType = "code";
    options.ProviderOptions.DefaultScopes.Add("email");
    options.ProviderOptions.AdditionalProviderParameters.Add("audience", builder.Configuration["Auth0:Audience"]);
});
builder.Services.AddSyncfusionBlazor();
builder.Services.AddBlazoredLocalStorage(config =>
{
    config.JsonSerializerOptions.DictionaryKeyPolicy = JsonNamingPolicy.CamelCase;
    config.JsonSerializerOptions.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull;
    config.JsonSerializerOptions.IgnoreReadOnlyProperties = true;
    config.JsonSerializerOptions.PropertyNameCaseInsensitive = true;
    config.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    config.JsonSerializerOptions.ReadCommentHandling = JsonCommentHandling.Skip;
    config.JsonSerializerOptions.WriteIndented = false;
});

builder.Services.AddScoped(typeof(ISubscriptionService), typeof(SubscriptionService));
builder.Services.AddScoped(typeof(IUserService), typeof(UserService));
builder.Services.AddScoped(typeof(IBreakingErrorService), typeof(BreakingErrorService));

await builder.Build().RunAsync();

//Make first connection to Cosmos DB to help with startup time. Cosmos DB fetches the address routing table during the first connection.
HttpClient startupClient = new HttpClient()
{
    BaseAddress = new Uri(builder.HostEnvironment.IsDevelopment() ? builder.Configuration["API_Prefix"] : builder.HostEnvironment.BaseAddress)
};
await startupClient.GetAsync("/api/OpenAsync");

