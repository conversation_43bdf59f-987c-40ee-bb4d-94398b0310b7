@using Newtonsoft.Json.Linq
@using TwentyDishes.Client.Services.ServiceInterfaces
@using TwentyDishes.Client.Components.SplashScreen
@using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent

@inherits UserBaseComponent

@inject HttpClient HttpClient
@inject HttpClientAuth0 HttpClientAuth0
@inject IJSRuntime JSRuntime
@inject Blazored.LocalStorage.ILocalStorageService LocalStorage
@inject NavigationManager NavManager
@inject ISubscriptionService SubscriptionService
@inject HttpClientAnon HttpClientAnon
@inject IConfiguration Config
@inject IUserService UserService

<Banner @bind-MessageType="@messageType" @bind-Message="@message" />

@if (loading || creatingSubScription) {
    <SplashScreen />
}
@if (!(loading || creatingSubScription) && logoBrand != null)
{
    <div class="subsc-sm">
        <div class="subsc-wrap">
            <div class="img-sm">
                @if (!string.IsNullOrWhiteSpace(logoBrand.LogoCloudflareId))
                {
                    <img class="logo-sm" src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, logoBrand.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logoBrand.PageTitle)" />
                }
                <img class="top-right-img" src="/images/Img-topright-sm.png" />
                <img class="bottom-img" src="/images/Img-bottom-sm.png" />
            </div>

            <div class="subsc-content-sm">
                @if (subscriptionScreen < 2)
                {
                    <h1>Select Your <span>Plan</span></h1>
                    <div class="subsc-container">
                        <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Free ? "subsc-item-selected" : "subsc-item")>
                            <input id="sub-free-sm" type="radio" name="subscriptions-sm"
                                   checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Free)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Free))" />
                            <span class="checkmark"></span>
                            <label class="label-style" for="sub-free-sm">Free Plan - With Ads</label>
                        </div>
                        <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Monthly ? "subsc-item-selected" : "subsc-item")>
                            <input id="sub-monthly-sm" type="radio" name="subscriptions-sm"
                                   checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Monthly)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Monthly))" />
                            <span class="checkmark"></span>
                            <label class="label-style" for="sub-monthly-sm">Standard Plan - No Ads - $7  / Month <span class="smallsuperscript">+tax</span></label>
                        </div>
                    </div>
                    @if (PlanSelectOnly is not null && PlanSelectOnly.Value == true) {
                        <div class="button-container">
                            <button class="btn-secondary btn-subsc" @onclick="@HandleSelectPlanOnlyNext">Next</button>
                        </div>
                    }
                    else if (selectedSubscription != SubscriptionHelper.SubscriptionType.Free)
                    {
                        <div class="button-container">
                            <button class="btn-secondary btn-subsc" @onclick="@(() => HandleStepChange(subscriptionScreen+1))">Next</button>
                        </div>
                    }
                    else
                    {
                        <div class="button-container">
                            <button class="btn-secondary btn-pay-choice" @onclick="CreateSubscriptionPlan">Submit</button>
                        </div>
                    }
                }
                else if (subscriptionScreen == 2)
                {
                    <h1>Select Your <span>Plan</span></h1>
                    <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Quarterly ? "subsc-item-selected" : "subsc-item")>
                        <input id="sub-quarterly-sm" type="radio" name="subscriptions-sm"
                               checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Quarterly)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Quarterly))" />
                        <span class="checkmark"></span>
                        <label class="label-style" for="sub-quarterly-sm">Quarter - $18 / Quarter <span class="smallsuperscript">+tax</span> (<strong>SAVE 14%</strong>)</label>
                    </div>
                    <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Annual ? "subsc-item-selected" : "subsc-item")>
                        <input id="sub-annual-sm" type="radio" name="subscriptions-sm"
                               checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Annual)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Annual))" />
                        <span class="checkmark"></span>
                        <label class="label-style" for="sub-annual-sm">Annual - $60 / Year <span class="smallsuperscript">+tax</span> (<strong>SAVE 29%</strong>)</label>
                    </div>
                    <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Monthly ? "subsc-item-selected" : "subsc-item")>
                        <input id="sub-monthly-sm" type="radio" name="subscriptions-sm"
                               checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Monthly)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Monthly))" />
                        <span class="checkmark"></span>
                        <label class="label-style" for="sub-monthly-sm">Monthly - $7 / Month <span class="smallsuperscript">+tax</span></label>
                    </div>
                    <div class="button-container">
                        <button class="btn-secondary btn-subsc" @onclick="@(() => HandleStepChange(subscriptionScreen+1))">Next</button>
                        <button class="btn-back" @onclick="@(() => subscriptionScreen--)">Go back to previous screen</button>
                    </div>

                }                
            </div>
        </div>
    </div>

    <div class="subsc-main">
        @if (subscriptionScreen < 2)
        { 
            <div class="subsc-content">
                <div class="twenty-dishes-logo">
                    @if (!string.IsNullOrWhiteSpace(logoBrand.LogoCloudflareId))
                    {
                        <img src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, logoBrand.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logoBrand.PageTitle)" />
                    }
                </div>
                <h1>Select Your <span>Plan</span></h1>
                <div class="subsc-container">
                    <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Free ? "subsc-item-selected" : "subsc-item")>
                        <input id="sub-free" type="radio" name="subscriptions"
                               checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Free)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Free))" />
                        <span class="checkmark"></span>
                        <label class="label-style" for="sub-free">Free Plan - With Ads</label>
                    </div>
                    <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Monthly ? "subsc-item-selected" : "subsc-item")>
                        <input id="sub-monthly" type="radio" name="subscriptions"
                               checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Monthly)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Monthly))" />
                        <span class="checkmark"></span>
                        <label class="label-style" for="sub-monthly">Standard Plan - No Ads - $7 / Month <span class="smallsuperscript">+tax</span></label>
                    </div>
                </div>
                @if (PlanSelectOnly is not null && PlanSelectOnly.Value == true) {
                    <div class="button-container">
                        <button class="btn-secondary btn-subsc" @onclick="@HandleSelectPlanOnlyNext">Next</button>
                    </div>
                }
                else if (selectedSubscription != SubscriptionHelper.SubscriptionType.Free)
                {
                    <div class="button-container">
                        <button class="btn-secondary btn-subsc" @onclick="@(() => HandleStepChange(subscriptionScreen+1))">Next</button>
                    </div>
                }
                else
                {
                    <div class="button-container">
                        <button class="btn-secondary btn-pay-choice" @onclick="CreateSubscriptionPlan">Submit</button>
                    </div>
                }
            </div>
        }
        else if (subscriptionScreen == 2)
        {
            <div class="subsc-content">
                <div class="twenty-dishes-logo">
                    @if (!string.IsNullOrWhiteSpace(logoBrand.LogoCloudflareId))
                    {
                        <img src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, logoBrand.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logoBrand.PageTitle)" />
                    }
                </div>
                <h1>Select Your <span>Plan</span></h1>
                <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Monthly ? "subsc-item-selected" : "subsc-item")>
                    <input id="sub-monthly" type="radio" name="subscriptions"
                           checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Monthly)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Monthly))" />
                    <span class="checkmark"></span>
                    <label class="label-style" for="sub-monthly">Pay Monthly - $7 / Month <span class="smallsuperscript">+tax</span></label>
                </div>
                <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Quarterly ? "subsc-item-selected" : "subsc-item")>
                    <input id="sub-quarterly" type="radio" name="subscriptions"
                           checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Quarterly)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Quarterly))" />
                    <span class="checkmark"></span>
                    <label class="label-style" for="sub-quarterly">Pay Quarterly - $18 / Quarter <span class="smallsuperscript">+tax</span> (<strong>SAVE 14%</strong>)</label>
                </div>
                <div class=@(selectedSubscription == SubscriptionHelper.SubscriptionType.Annual ? "subsc-item-selected" : "subsc-item")>
                    <input id="sub-annual" type="radio" name="subscriptions"
                           checked="@(selectedSubscription == SubscriptionHelper.SubscriptionType.Annual)" @onchange="@(() => SetSubscriptionType(SubscriptionHelper.SubscriptionType.Annual))" />
                    <span class="checkmark"></span>
                    <label class="label-style" for="sub-annual">Pay Annually - $60 / Year <span class="smallsuperscript">+tax</span> (<strong>SAVE 29%</strong>)</label>
                </div>

                <div class="button-container">
                    @* upgrade *@
                    @if (user?.SubscriptionProvider == SubscriptionHelper.PaymentSystem.Paddle.ToString()) {
                        <button class="btn-secondary btn-subsc" @onclick="@HandleSubscriptionChange">Change Subscription</button>
                    }
                    @* new sub *@
                    else {
                        <button class="btn-secondary btn-subsc" @onclick="@(() => HandleStepChange(subscriptionScreen+1))">Next</button>
                    }
                    <button class="btn-back" @onclick="@(() => HandleStepChange(subscriptionScreen-1))">Go back to previous screen</button>
                </div>
            </div>
        }       
    </div>


    if(subscriptionScreen > 2)
    {
        <div class="subsc-content">
            <div class="twenty-dishes-logo">
                @if (!string.IsNullOrWhiteSpace(logoBrand.LogoCloudflareId))
                {
                    <img src="@MiscUtility.GetCloudflareImageUrl(logoBrand.BaseUrl, logoBrand.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logoBrand.PageTitle)" />
                }
            </div>
            <h1 class="h1-pay-choice"><span>Subscription</span> Payment</h1>
           
            <div class="button-container">
                @if (!purchaseStarted)
                {
                    <button class="btn-back" id="checkout-back-btn" @onclick="@(() => HandleStepChange(subscriptionScreen-1))" disabled="@purchaseStarted">Go back to previous screen</button>
                    <button class="btn-secondary btn-subsc btn-subsc-sm" @onclick="@(() => HandleStepChange(subscriptionScreen-1))" disabled="@purchaseStarted">Go back to previous screen</button>
                }
            </div>
        </div>
    }
}
