using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Newtonsoft.Json;
using TwentyDishes.Client.State.UserGlobalState;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;
using Microsoft.AspNetCore.Components.Authorization;
using TwentyDishes.Client.Services.BreakingErrorService;
using TwentyDishes.Client.Helpers;
using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent;

namespace TwentyDishes.Client.Components.SubscriptionSelection
{
    public partial class SubscriptionSelection : UserBaseComponent
    {
        [Inject]
        public IBreakingErrorService _breakingErrorService { get; set; } = default!;

        private bool loading => doingSetup || (userGlobalState.Loading && !(PlanSelectOnly is not null && PlanSelectOnly.Value));
        private bool doingSetup = true;
        private bool creatingSubScription;
        private Brand logoBrand = new Brand();
        private string message;
        private MiscUtility.AlertMessageType messageType;
        private bool planPreselected;
        private SubscriptionHelper.PaymentSystem? selectedPaymentSystem { get; set; } = null;
        private SubscriptionHelper.SubscriptionType? selectedSubscription { get; set; } = null;
        private int subscriptionScreen = 1;
        private string userFirstName;
        private string userId;
        private string userLastName;
        private User? user;
        private UserSubscriptionDetail? userSubDetail;
        private bool firstRenderComplete;
        private bool checkoutActive;
        private bool purchaseStarted;

        private bool initialized;
        private bool initializationStarted;
        private bool shouldInitialize;

        [Parameter]
        public EventCallback OnSubscriptionSelected { get; set; }

        [Parameter]
        public bool? PlanSelectOnly { get; set; }

        [Parameter]
        public SubscriptionHelper.SubscriptionType? InitialPlanSelection { get; set; }

        [CascadingParameter]
        private Task<AuthenticationState>? AuthenticationState { get; set; }

        private string? result;
        private DotNetObjectReference<SubscriptionSelection>? objRef;

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                objRef = DotNetObjectReference.Create(this);

                await JSRuntime.InvokeVoidAsync("SetDotNetObjectRef", "SubscriptionSelectionObjRef", objRef);
            }

            await InvokeAsync(() => InitiatingCheckout());

        }

        public async Task InitiatingCheckout()
        {
            if (subscriptionScreen == 3 && !checkoutActive)
            {
                checkoutActive = true;

                var paddleProductId = selectedSubscription == SubscriptionHelper.SubscriptionType.Monthly
                    ? Config["PaddleMonthlyId"]
                    : selectedSubscription == SubscriptionHelper.SubscriptionType.Quarterly
                        ? Config["PaddleQuarterlyId"]
                        : selectedSubscription == SubscriptionHelper.SubscriptionType.Annual
                            ? Config["PaddleYearlyId"]
                            : "";

                try
                {
                    await JSRuntime.InvokeVoidAsync(
                        "Paddle.Checkout.open",
                         new
                         {
                             items = new Object[] { new { priceId = paddleProductId, quantity = 1 } },
                             settings = new
                             {
                                 displayMode = "modal",
                                 frameTarget = "checkout-container",
                                 frameInitialHeight = "450",
                                 frameStyle = "position: relative; width: 100%;"
                             },
                             customer = new
                             {
                                 email = userGlobalState.UserEmail
                             }
                         }
                    );
                }
                catch (Exception exception)
                {
                    await _breakingErrorService.ShowBreakingError(BreakingErrors.PaddleCheckoutFailedToOpen, exception.Message);
                }
            }
        }

        private async Task Initialize()
        {
            initializationStarted = true;

            try
            {
                HttpResponseMessage response = await HttpClientAnon.Client.PostAsJsonAsync<string>("/api/BrandByName", NavManager.BaseUri.Replace("https://", string.Empty).Replace("http://", string.Empty).Replace("/", string.Empty));

                if (response.IsSuccessStatusCode)
                {
                    logoBrand = await response.Content.ReadFromJsonAsync<Brand>() ?? new Brand();
                }

                var authState = await AuthenticationState!;

                if (authState?.User?.Identity is not null && authState.User.Identity.IsAuthenticated)
                {
                    userFirstName = userGlobalState.FullUserData?.FirstName ?? string.Empty;
                    userId = userGlobalState.UserId ?? string.Empty;
                    userLastName = userGlobalState.FullUserData?.LastName ?? string.Empty;
                    user = userGlobalState.FullUserData;
                    userSubDetail = userGlobalState.UserSubscriptionDetail;
                }

                // if there's an initial selection provided then the subscription might be getting created automatically without user input
                if ((PlanSelectOnly is null || !PlanSelectOnly.Value) && selectedSubscription is not null)
                {
                    if (selectedSubscription == SubscriptionHelper.SubscriptionType.Free)
                    {
                        await CreateSubscriptionPlan();
                    }
                    else
                    {
                        HandleStepChange(2);
                    }
                }

                doingSetup = false;
                initializationStarted = false;
                initialized = true;
            }
            catch (Exception exception)
            {
                await _breakingErrorService.ShowBreakingError(BreakingErrors.SubscriptionSelectionFailedToInitialize, exception.Message);
            }
        }

        public override async Task SetParametersAsync(ParameterView parameters)
        {
            // parameters.SetParameterProperties(this);

            if (parameters.TryGetValue<SubscriptionHelper.SubscriptionType?>(nameof(InitialPlanSelection), out var initialPlanSelection))
            {
                if (!firstRenderComplete && initialPlanSelection is not null)
                {
                    SetSubscriptionType(initialPlanSelection.Value);
                }
            }

            if (parameters.TryGetValue<UserGlobalStateValue>(nameof(UserGlobalStateValue), out var userGlobalStateValue))
            {
                if (((userGlobalStateValue is not null && userGlobalStateValue.Loaded) || (PlanSelectOnly is not null && PlanSelectOnly.Value)) && !initialized && !initializationStarted)
                {
                    shouldInitialize = true;
                }
            }

            firstRenderComplete = true;

            await base.SetParametersAsync(parameters);
        }

        protected override async Task OnParametersSetAsync()
        {
            await base.OnParametersSetAsync();

            if (shouldInitialize)
            {
                shouldInitialize = false;

                await Initialize();
            }
        }

        private async Task CreateSubscriptionPlan()
        {
            if (selectedPaymentSystem is not null && selectedSubscription is not null && userGlobalState.UserEmail is not null && userGlobalState.UserId is not null)
            {
                creatingSubScription = true;

                StateHasChanged();

                try
                {
                    UserSubscription userSubscription = new UserSubscription()
                    {
                        PaymentSystem = selectedPaymentSystem.Value,
                        SubscriptionType = selectedSubscription.Value,
                        UserFirstName = userFirstName,
                        UserId = userId,
                        UserLastName = userLastName
                    };

                    await SubscriptionService.CreateSubscriptionPlan(false, userSubscription, userGlobalState.UserEmail, userGlobalState.UserId);
                }
                catch (Exception exception)
                {
                    await _breakingErrorService.ShowBreakingError(BreakingErrors.SubscriptionSelectionFailedToCreateSubscription, exception.Message, "While requesting a subscription creation from the back-end API the request failed.");
                }

                await OnSubscriptionSelected.InvokeAsync();
            }
        }

        private void SetPaymentSystem(SubscriptionHelper.PaymentSystem paymentSystem)
        {
            selectedPaymentSystem = paymentSystem;
        }

        private void SetSubscriptionType(SubscriptionHelper.SubscriptionType subscriptionType)
        {
            selectedSubscription = subscriptionType;

            if (selectedSubscription == SubscriptionHelper.SubscriptionType.Free)
            {
                SetPaymentSystem(SubscriptionHelper.PaymentSystem.Free);
            }
            else
            {
                SetPaymentSystem(SubscriptionHelper.PaymentSystem.Paddle);
            }
        }

        private void HandleSelectPlanOnlyNext()
        {
            NavManager.NavigateTo($"/subscription?plan={(selectedSubscription == SubscriptionHelper.SubscriptionType.Free ? "free" : "monthly")}");
        }

        private void HandleStepChange(int stepIndex)
        {
            subscriptionScreen = stepIndex;
            checkoutActive = false;
        }

        private async Task HandleSubscriptionChange()
        {
            if (selectedSubscription is not null)
            {
                // change the item used in the existing subscription

                creatingSubScription = true;

                StateHasChanged();

                try
                {
                    await HttpClient.PatchAsync($"/api/ModifyUserSubscription/{user?.Id}/{user?.SubscriptionId}", new StringContent(JsonConvert.SerializeObject(new ModifySubscriptionRequest() { SubscripionType = selectedSubscription.Value }), null, "application/json"));
                }
                catch (Exception exception)
                {
                    await _breakingErrorService.ShowBreakingError(BreakingErrors.SubscriptionSelectionFailedToChangeSubscription, exception.Message);
                }

                await OnSubscriptionSelected.InvokeAsync();
            }
        }

        [JSInvokable]
        public async Task HandleCheckoutEvent(string eventName)
        {
            if (eventName == "checkout.completed")
            {
                await Task.Delay(2000);

                await CreateSubscriptionPlan();
            }
            else if (eventName == "checkout.payment.initiated")
            {
                purchaseStarted = true;

                StateHasChanged();
            }
        }

        public void Dispose()
        {
            objRef?.Dispose();
        }
    }
}