.subsc-main {
    height: 100vh;
    width: calc(100vw);
    background-color: rgba(219, 121, 47, 0);
    background-image: url('images/background-image.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
 
.subsc-content {
    position: absolute;
    width: calc(100% - 70%);
    height: auto;
    border-radius: 12px;
    top: 53%;
    left: 75%;
    transform: translate(-50%, -50%);
}

.smallsuperscript {
    vertical-align: super;
    font-size: 75%;
}

h1 {
    margin: 5px;
    font-family: Ralew<PERSON>;
    font-weight: 600;
    font-size: 46px;
    line-height: 54px;
}

    h1 span {
        color: var(--salesButtonOrange);
    }
     
.h1-pay-choice {
    padding: 15px;
}

.twenty-dishes-logo img {
    position: relative;
    width: 400px;
    height: auto;
}

.twenty-dishes-logo {
    position: relative;
    left: -10px;
    top: -20px;
}

.subsc-container {
    padding-top: 20px;
}

.subsc-item {
    position: relative;
    width: 486px;
    margin: 15px 5px;
    padding: 15px 5px;
    border-style: solid;
    border-width: 1px;
    border-radius: 6px;
    border-color: rgba(0, 0, 0, 0.19);
    cursor: pointer;
}

.subsc-item-selected {
    position: relative;
    width: 486px;
    margin: 15px 5px;
    padding: 15px 5px;
    border-style: solid;
    border-width: 1px;
    border-radius: 6px;
    border-color: var(--salesButtonOrange);
    background-color: white;
    color: var(--salesButtonOrange);
    cursor: pointer;
}

.subsc-item input {
    position: absolute;
    width: 40px;
    top: 17px;
    z-index: 1;
    opacity: 0;
    cursor: pointer;
}

.subsc-item-selected input {
    position: absolute;
    width: 40px;
    top: 17px;
    z-index: 1;
    opacity: 0;
    cursor: pointer;
}

.checkmark {
    position: absolute;
    top: 18px;
    left: 20px;
    height: 24px;
    width: 24px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 50%;
}

.subsc-item:hover input ~ .checkmark {
    background-color: #ccc;
}

.subsc-item input:checked ~ .checkmark {
    background-color: rgba(219, 121, 47, 1);
}

.subsc-item input:checked ~ .label-style {
    color: rgba(219, 121, 47, 1);
}

.subsc-item-selected:hover input ~ .checkmark {
    background-color: #ccc;
}

.subsc-item-selected input:checked ~ .checkmark {
    background-color: rgba(219, 121, 47, 1);
    
}

.subsc-item-selected input:checked ~ .label-style {
    color: rgba(219, 121, 47, 1);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.subsc-item input:checked ~ .checkmark:after {
    display: block;
}

.subsc-item-selected input:checked ~ .checkmark:after {
    display: block;
}

/* Style the indicator (dot/circle) */
.subsc-item .checkmark:after {
    top: 6px;
    left: 6px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
}

.subsc-item-selected .checkmark:after {
    top: 6px;
    left: 6px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
}

.label-style {
    margin-left: 60px;
    color: rgba(0, 0, 0, 0.3);
    font-family: 'Lato';
    font-size: 20px;
    cursor: pointer;
}
.label-style-selected {
    margin-left: 60px;
    color: var(--salesButtonOrange);
    font-family: 'Lato';
    font-size: 20px;
    cursor: pointer;
}
.subsc-item input {
    margin: 5px;
}

.payment-choices {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: center;
    margin: 0 auto;
    transform: translate(-12%, 10%);
}

.payment-choice {
    position: relative;
    height: 147px;
    width: 250px;
    padding: 20px;
    margin-right: 20px;
    border-style: solid;
    border-width: 1px;
    border-radius: 6px;
    border-color: rgba(0, 0, 0, 0.19);
    cursor: pointer;
}

    .payment-choice .checkmark {
        position: absolute;
        left: 212px;
        top: 9px;
    }

.payment-choice-selected .checkmark {
    position: absolute;
    left: 212px;
    top: 9px;
}

.payment-choice:hover input ~ .checkmark {
    background-color: #ccc;
}

.payment-choice input:checked ~ .checkmark {
    background-color: rgba(219, 121, 47, 1);
}

.payment-choice .checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.payment-choice input:checked ~ .checkmark:after {
    display: block;
}

.payment-choice .checkmark:after {
    top: 6px;
    left: 6px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
}

.payment-choice-selected {
    position: relative;
    height: 147px;
    width: 250px;
    padding: 20px;
    background-color: white;
    margin-right: 20px;
    border-style: solid;
    border-width: 1px;
    border-radius: 6px;
    border-color: var(--salesButtonOrange);
    cursor: pointer;
}

    .payment-choice-selected:hover input ~ .checkmark {
        background-color: #ccc;
    }

    .payment-choice-selected input:checked ~ .checkmark {
        background-color: rgba(219, 121, 47, 1);
    }

    .payment-choice-selected .checkmark:after {
        content: "";
        position: absolute;
        display: none;
    }

    .payment-choice-selected .checkmark:after {
        top: 6px;
        left: 6px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: white;
    }

    .payment-choice-selected input:checked ~ .checkmark:after {
        display: block;
    }

.payment-choice input {
    position: relative;
    width: 30px;
    bottom: 8px;
    left: 10px;
    float: right;
    z-index: 1;
    opacity: 0;
}

.payment-choice-selected input {
    position: relative;
    width: 30px;
    bottom: 8px;
    left: 10px;
    float: right;
    z-index: 1;
    opacity: 0;
}

.button-container {
    position: relative;
    display: flex;
    flex-direction: column;
    margin: 5px;
    padding-top: 20px;
}

.btn-subsc {
    height: 60px;
    width: 486px;
    border-width: 0;
    border-radius: 32px;
    font-size: 18px;
    color: #fff;
    font-family: 'Raleway';
    cursor: default;
}
    .btn-subsc:hover {
        background-color: rgba(219, 121, 47, 0.90);
    }
.btn-pay-choice {
    height: 60px;
    width: 486px;
    margin: 10px;
    border-width: 0;
    border-radius: 32px;
    font-size: 18px;
    color: #fff;
    font-family: 'Raleway';
    cursor: default;
}
    .btn-pay-choice:hover {
        background-color: rgba(219, 121, 47, 0.90);
    }
.btn-back {
    height: 60px;    
    margin: 10px;
    border-width: 0;
    font-family: Raleway;
    font-weight: 500;
    font-size: 18px;
    line-height: 21.13px;
    text-align: center;
    background-color: rgba(0,0,0,0.0);
    color: var(--salesButtonOrange);
}
.checkout-container {    
    height: 500px;
    overflow-y: scroll;
}
.subsc-sm {
    display: none;
}
.btn-subsc-sm{
    display: none;
}
@media only screen and (max-width: 759px) and (min-width: 300px) {
    .subsc-sm {
        display: block;
    }
    #checkout-back-btn {
        display: none;
    }
    .btn-subsc-sm{
        display: block;
        font-size: 15px;
    }
    .twenty-dishes-logo{
        display:none;
    }
    .subsc-content{
        position:absolute;
        top: 120px;
        left:0;
        width:400px;
        transform: translate(0,0);
    }
    .subsc-main {
        display: none;
    }    
    .h1-pay-choice{
        font-size:26px;
        padding: 0;
        margin: -10px 0 0 10px;
        width: 300px;
    }
    .payment-container{
        position:relative;
        top:-35px;
    }
    .subsc-content-sm {
        width: 100%;
        position: absolute;
        top: 20%;
        margin: 0 auto                
    }
    .subsc-content{
        top: 20%;
        width:calc(100% - 5%);
    }
        .subsc-content-sm h1 {
            font-size: 28px;
            padding-left: 5px;
        }

        .subsc-content-sm label {
            font-size: 16px;
        }

    .subsc-item {
        width: calc(100% - 5%);
    }
    
    .subsc-item-selected {
        width: calc(100% - 5%);
    }

    .subsc-wrap {
        background: rgba(219, 121, 47, 0.23);
        height: 100vh;
    }

    .top-right-img {
        float: right;
    }
    .checkout-container{
        height: 55vh;
        width: 100%;
        margin-left: 5px;
    }
    .logo-sm {
        position: absolute;
        float: left;
        height: 60px;
        width: calc(100% - 32%);
        margin: 30px 0 0 3px;
    }

    .btn-subsc {
        width: 100%;
        height: 40px;
    }

    .btn-pay-choice {
        width: calc(100% - 6%);
        height: 40px;
    }

    .btn-back {
        width: 100%;
        margin: 20px 0 0 5px;        
        height: 20px;
    }

    .bottom-img {
        position: absolute;
        bottom: 0;
        width: 100vw;
    }

    .payment-choices {
        padding: 0;
        margin-left: calc(15vw - 10%);
        flex-direction: column;
        justify-content: space-between;
        width: 200px;
    }

    .pay-item {
        padding: 0 0 20px 0;
        width: 200px;
    }

    .subsc-item-selected input:checked ~ .checkmark {
        background-color: rgba(219, 121, 47, 1);
    }
}
@media only screen and (max-width: 1000px) and (min-width: 760px) {
    h1 {
        font-size: 25px;
    }

    .twenty-dishes-logo img {
        left: 0px;
        width: 280px;
        height: 65px;
    }    
    .h1-pay-choice {
        position: relative;
        left: -10px;
        font-size: 28px;
        width: 350px;
        
    }   
    .subsc-content{
        left: 66% !important;
    }
    .label-style {
        font-size: 12px;
    }    
    .subsc-item-selected {
        width: 350px;
        margin-top: 0px;
    }

    .subsc-item {
        width: 350px;
    }

    .subsc-content {       
        height: 450px;
        top: 45%;        
    }
    .checkout-container{
        height: 280px;
        width: 360px;
    }
    .btn-subsc {
        width: 350px;
        height: 45px;
    }

    .payment-choices {
        left: -15%;
    }

    .btn-pay-choice {
        margin-top: 15px;
        margin-left: 20px;
        width: 300px;
        height: 45px;
    }    
    .twenty-dishes-logo {
        left: -2px;
        top: 5px;
    }

    .btn-back {
        margin-left: 25px;
        font-size: 15px;
        width: 300px;
    }

    .payment-choice {
        width: 200px;
    }

    .payment-choice-selected {
        width: 200px;
    }
    .payment-choice input {
        left: 9px;
    }

    .payment-choice .checkmark {
        left: 160px;
        top: 6px;
    }

    .payment-choice-selected input {
        left: 9px;
    }

    .payment-choice-selected .checkmark {
        left: 160px;
        top: 6px;
    }
}

@media only screen and (max-width: 1100px) and (min-width: 1001px) {
    h1 {
        font-size: 22px;
    }    
    .h1-pay-choice {
        position: relative;
        font-size: 30px;
        width: 360px;
        left: -30px;
    }

    .twenty-dishes-logo img {        
       width: 300px;
       left: -20px;
    }

    .label-style {
        font-size: 12px;
    }

    .subsc-item-selected {
        width: 320px;
        margin-top: 0px;
    }

    .subsc-item {
        width: 320px;
    }
    .subsc-content{
        left: 70% !important;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
    }
    .checkout-container{
        height: 270px !important;
    }
    .btn-subsc {
        width: 320px;
        height: 45px;
    }

    .payment-choices {
        left: -15%;
    }

    .btn-pay-choice {
        margin-top: 25px;
        margin-left: 10px;
        width: 300px;
        height: 45px;
    }    

    .twenty-dishes-logo {
        left: -2px;
        top: 5px;
    }

    .btn-back {
        width: 300px;
        margin-left: 10px;
    }

    .payment-choice {
        width: 200px;
    }

    .payment-choice-selected {
        width: 200px;
    }

    .payment-choice input {
        left: 9px;
    }

    .payment-choice .checkmark {
        left: 160px;
        top: 6px;
    }

    .payment-choice-selected input {
        left: 9px;
    }

    .payment-choice-selected .checkmark {
        left: 160px;
        top: 6px;
    }
}

@media only screen and (max-width: 1300px) and (min-width: 1101px) {
    h1 {
        font-size: 25px;
    }

    .h1-pay-choice {
        font-size: 33px;
        width: 370px;
        position: relative;
        left: -10px;
    }


    .label-style {
        font-size: 12px;
    }
    .checkout-container{
        height: 260px !important;
    }
    .subsc-item-selected {
        width: 350px;
        margin-top: 0px;
    }

    .subsc-item {
        width: 350px;
    }

    .subsc-content {
        top: 12% !important;
        left: 60% !important;
    }

    .btn-subsc {
        width: 350px;
        height: 45px;
    }

    .payment-choices {
        left: -15%;
    }

    .btn-pay-choice {
        margin-left: 32px;
        width: 300px;
        height: 45px;
    }

    .twenty-dishes-logo img {
        width: 320px;
        height: 70px;
    }

    .twenty-dishes-logo {
        left: -2px;
        top: 5px;
    }

    .btn-back {
        margin-left: -20px;
        font-size: 15px;
        
    }

    .payment-choice {
        width: 200px;
    }

    .payment-choice-selected {
        width: 200px;
    }
   
    .payment-choice input {
        left: 9px;
    }

    .payment-choice .checkmark {
        left: 160px;
        top: 6px;
    }  

    .payment-choice-selected input {
        left: 9px;
    }

    .payment-choice-selected .checkmark {
        left: 160px;
        top: 6px;
    }
}

@media only screen and (min-width: 1300px) and (max-width: 1579px) {
    .payment-choice {
        width: 200px;
    }

    .h1-pay-choice {
        width: calc(100%);
        margin-left: 0px;
        font-size: 34px;
    }

    .payment-choices {
    }
    .checkout-container{
        height: 250px !important;
    }
    .payment-choice-selected {
        width: 200px;
    }

    .btn-pay-choice {
        width: 400px;
    }

    .btn-back {
        width: 300px;
        height:0;
        position:relative;
        margin:20px 0 0 0;
        left: 80px;
    }    
   
    .subsc-content{        
        left: 56% !important;
        top: 8% !important;
    }
}

@media only screen and (max-width: 1919px) and (min-width: 1580px) {
    .btn-pay-choice {
        margin-top: 30px;
    }

    .h1-pay-choice {
        position: relative;
        left: -20px;
        width: 600px;
    }
    .subsc-content{
        left: 62% !important;
        top: 10% !important;
    }
    .twenty-dishes-logo img {
        left: 0px;
        top: 20px;
    }
    .checkout-container{
        height: 250px !important;
    }
    .btn-back{
        margin-left: -50px;
    }
}
@media only screen and (min-width: 1920px){
    .btn-back{
        margin-left: -50px;
    }
    .subsc-content {
        left: 62% !important;
        top: 10% !important;
    }
    .checkout-container {
        height: 300px !important;
    }
}
@media only screen and (min-width: 2560px) {
    .btn-back {
        margin-left: -250px;
    }
    .checkout-container{
        margin-left: -150px;
    }
    .subsc-content {
        left: 62% !important;
        top: 25% !important;
    }

    .checkout-container {
        height: 330px !important;
    }
}
@media only screen and (min-width: 1024px) and (max-width: 2560px) {
    .payment-choices {
        left: 70px;
    }

    /*.btn-back {
        margin-left: -20px;
        padding: 0;
        height: 0;
        margin-top: 20px;
    }
*/
    .subsc-content {
        height: auto;
        transform: none;
        top: 25%;
        left: 62%;
    }

    .checkout-container {
        height: 350px;
    }
}

