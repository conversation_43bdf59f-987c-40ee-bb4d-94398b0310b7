using Microsoft.AspNetCore.Components;
using TwentyDishes.Client.State.UserGlobalState;
using TwentyDishes.Client.Views;

namespace TwentyDishes.Client.Components.BaseComponents.UserBaseComponent
{
    public class UserBaseComponent : ComponentBase
    {
        public bool userStateLoading = true;
        public bool userStateLoaded = false;
        public bool globalStateLoading => userStateLoading;
        public UserGlobalStateValue userGlobalState => UserGlobalStateValue;

        public bool initialized = false;
        public bool initializationStarted = false;

        private bool userStateHasReloaded = false;

        [CascadingParameter(Name = UserGlobalStateFields.Value)]
        public required UserGlobalStateValue UserGlobalStateValue { get; set; }

        public override async Task SetParametersAsync(ParameterView parameters)
        {
            if (parameters.TryGetValue<UserGlobalStateValue>(nameof(UserGlobalStateValue), out var userGlobalStateValue))
            {
                if (userGlobalStateValue is not null && userGlobalStateValue.Loaded && (UserGlobalStateValue is null || !UserGlobalStateValue.Loaded))
                {
                    userStateHasReloaded = true;
                }
            }

            await base.SetParametersAsync(parameters);
        }

        protected override async Task OnParametersSetAsync()
        {
            if (userStateHasReloaded)
            {
                userStateHasReloaded = false;

                await UserStateLoaded();
                await GlobalStateLoaded();
            }
        }

        public virtual async Task UserStateLoaded()
        {

        }

        public virtual async Task GlobalStateLoaded()
        {

        }
    }
}