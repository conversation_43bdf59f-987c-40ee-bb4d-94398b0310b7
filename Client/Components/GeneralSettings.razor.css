.main-container {
    padding: 20px;
}

.user-info-h3 {
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    line-height: 21px;
    padding: 15px 0 15px 10px;
}

input {
    height: 54px;
    padding-left: 25px;
    color: rgba(0, 0, 0, 0.15);
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    border:none;
}
input:focus{
    color: rgba(219, 121, 47, 1);  
    border: 1px solid rgba(219, 121, 47, 1);
    outline:0px;
}

.info-container {
    display: flex;
    flex-direction: column;
    padding: 15px 0 0 10px;
}


.info-label {    
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    padding-bottom: 10px;    
}

.info-input {
    border: 1px solid;
    border-radius: 4px;
}

.info-first-name {
    display: flex;
    flex-direction: column;
    padding-bottom: 35px;
    width: 470px;
}

.info-last-name {
    display: flex;
    flex-direction: column;
    padding-bottom: 35px;
    width: 470px;
}

.info-email {
    display: flex;
    flex-direction: column;
    padding-bottom: 35px;
    width: 470px;
}

.info-reset-pass {
   padding: 0 0 20px 5px;
}

.text-pass {
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    text-decoration-line: underline;
    color: #CA2F35;
    cursor:pointer;
}

.btn-save-container {
    width: 198px;
    height: 55px;
}

.btn-save {
    background: #CA2F35;
    color: #fff;
    border: 1px solid #CA2F35;
    border-radius: 55px;
    width: 198px;
    height: 60px;
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    line-height: 21px;
}

@media only screen and (max-width: 370px) and (min-width: 320px) {
    input{        
        width: 240px;
    }
    .info-first-name{
        width: 240px;
    }
    .info-email{
        width: 240px;
    }
    .info-last-name{
        width: 240px;
    }
    .btn-save{
        font-size: 15px;
        width: 160px;
        height: 60px;
    }
}
@media only screen and (max-width: 425px) and (min-width: 375px) {
    input {
        width: 260px;
    }

    .info-first-name {
        width: 260px;
    }

    .info-email {
        width: 260px;
    }

    .info-last-name {
        width: 260px;
    }

    .btn-save {
        font-size: 15px;
        width: 160px;
        height: 60px;
    }
}
@media only screen and (max-width: 600px) and (min-width: 425px) {
    input {
        width: 300px;
    }

    .info-first-name {
        width: 300px;
    }

    .info-email {
        width: 300px;
    }

    .info-last-name {
        width: 300px;
    }

    .btn-save {
        font-size: 15px;
        width: 160px;
        height: 60px;
    }
}