@attribute [Authorize]
@inject HttpClient HttpClient

<div class="add-recipe-main">
    <div class="add-recipe-header">
        <div class="add-btn-container">
            <button class="btn-add-search" style="@(selectedMode == RecipeAddMode.Search ? "color: #393939; font-weight: 700;" : "color: #858585; font-weight: 600;")" @onclick="@(() => SetMode(RecipeAddMode.Search))">Add Recipe</button>
        </div>
        <div class="fav-btn-container">
            <button class="btn-fav-selection" style="@(selectedMode == RecipeAddMode.Favorites ? "color: #393939; font-weight: 700;" : "color: #858585; font-weight: 600;")" @onclick="@(() => SetMode(RecipeAddMode.Favorites))">Favorite Recipes</button>
        </div>
        <div class="close-dialog">
            <svg width="20" height="20" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" @onclick="CloseDialog">
                <path d="M6.00011 4.82166L10.1251 0.696655L11.3034 1.87499L7.17844 5.99999L11.3034 10.125L10.1251 11.3033L6.00011 7.17832L1.87511 11.3033L0.696777 10.125L4.82178 5.99999L0.696777 1.87499L1.87511 0.696655L6.00011 4.82166Z" fill="black" />
            </svg>
        </div>
    </div>

    @if (selectedMode == RecipeAddMode.Search)
    {
        <div class="search-container">
            <div class="search-input-container">
                <input @ref="searchTextRef" class="search-input" type="text" placeholder="Search..." @bind-value="@searchText" @onkeypress="OnInputEnter" />
                <button class="search-sm" @onclick="SearchRecipes">
                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 8 8"><path fill="currentColor" d="M3.5 0C1.57 0 0 1.57 0 3.5S1.57 7 3.5 7c.59 0 1.17-.14 1.66-.41a1 1 0 0 0 .13.13l1 1a1.02 1.02 0 1 0 1.44-1.44l-1-1a1 1 0 0 0-.16-.13c.27-.49.44-1.06.44-1.66c0-1.93-1.57-3.5-3.5-3.5zm0 1C4.89 1 6 2.11 6 3.5c0 .66-.24 1.27-.66 1.72l-.03.03a1 1 0 0 0-.13.13c-.44.4-1.04.63-1.69.63c-1.39 0-2.5-1.11-2.5-2.5s1.11-2.5 2.5-2.5z" /></svg>
                </button>
            </div>
            <div class="btn-search-container">
                <button type="submit" class="btn-search" @onclick="SearchRecipes">Search Recipes</button>
            </div>
        </div>
        <div class="diet-container">
            <div class="diets">
                @foreach (Diet diet in Diets)
                {
                    <button class="diet" style="@(dietFilter.Contains(diet.Name) ? "background-color: var(--lightAccentYellow);" : string.Empty)" @onclick="@(() => ApplyDietFilter(diet.Name))">@diet.Name</button>
                }
            </div>
        </div>
        @if (!isLoading)
        {
            <div class="recipes-results">
                @if (firstSearchDone && !filteredRecipeResults.Any())
                {
                    <div>@("No results found for \'\''" + searchText + "\'\''")</div>
                }
                else
                {
                    @foreach (RecipePartial rp in filteredRecipeResults)
                    {
                        <div class="recipe-container">
                            <img class="rounded-3 recipe-image" height="270" width="272" src="@MiscUtility.GetCloudflareImageUrl(LogoBrand.BaseUrl, rp.RecipeImageId, MiscUtility.ImageUrlSuffixPublic)"
                                 alt="@rp.RecipeName" @onclick="@(() => GetRecipe(rp.RecipeId))" />
                            <div class="recipe-name-container">
                                <span class="recipe-name">@rp.RecipeName</span>
                                <div class="bar"></div>
                                <div class="toggle-fav-container">
                                    @if (CurrentUser.UserFavorites != null && CurrentUser.UserFavorites.Any() && CurrentUser.UserFavorites.Where(x => x.RecipeId == rp.RecipeId).Any())
                                    {
                                        <svg @onclick="@(() => ToggleFavorite(rp))" width="30px" height="30px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--fxemoji heart-icon-adj" preserveAspectRatio="xMidYMid meet"><path fill="#FF473E" d="M476.6 133c-53.7-63.5-148.7-66.5-206.3-9.1l-.2.2c-8 8-20.5 8-28.4 0l-.2-.2c-54.6-54.8-143.3-55-198.1-.4C-3.2 169.8-10.9 244.6 25 299.7c11.2 17.3 25.3 30.6 40.9 40.7l180 145.7c6.4 5.2 15.6 5.2 22.1 0l178.8-145.9c15-10 28.6-23 39.5-39.5c34.1-51.3 30.1-120.7-9.7-167.7z"></path></svg>
                                    }
                                    else
                                    {
                                        <svg width="30" height="29" viewBox="0 0 20 19" @onclick="@(() => ToggleFavorite(rp))"><use href="images/icons.svg#favorite-inactive" /></svg>
                                    }
                                </div>
                                <div class="btn-add-container">
                                    <button class="btn-add" @onclick="@(() => AddRecipeToMenu(rp.RecipeId))">Add</button>
                                </div>
                            </div>
                        </div>
                    }
                }
            </div>
        }
        else
        {
            <div class="loader">
                <Loader />
            </div>
        }
    }
    else
    {
        @if (CurrentUser.UserFavorites != null)
        {
            @if (!isLoading)
            {
                <div class="favorites-container">
                    <div class="recipes-results">
                        @foreach (RecipePartial rp in CurrentUser.UserFavorites)
                        {
                            <div class="recipe-container">
                                <img class="rounded-3 recipe-image" height="272" width="270" src="@MiscUtility.GetCloudflareImageUrl(LogoBrand.BaseUrl, rp.RecipeImageId, MiscUtility.ImageUrlSuffixPublic)"
                                     alt="@rp.RecipeName" @onclick="@(() => GetRecipe(rp.RecipeId))" />
                                <div class="recipe-name-container">
                                    <label class="recipe-name">@rp.RecipeName</label>
                                    <div class="bar"></div>
                                    <div class="toggle-fav-container">
                                        @if (CurrentUser.UserFavorites.Where(x => x.RecipeId == rp.RecipeId).Any())
                                        {
                                            <svg @onclick="@(() => ToggleFavorite(rp))" width="25px" height="25px" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--fxemoji" preserveAspectRatio="xMidYMid meet"><path fill="#FF473E" d="M476.6 133c-53.7-63.5-148.7-66.5-206.3-9.1l-.2.2c-8 8-20.5 8-28.4 0l-.2-.2c-54.6-54.8-143.3-55-198.1-.4C-3.2 169.8-10.9 244.6 25 299.7c11.2 17.3 25.3 30.6 40.9 40.7l180 145.7c6.4 5.2 15.6 5.2 22.1 0l178.8-145.9c15-10 28.6-23 39.5-39.5c34.1-51.3 30.1-120.7-9.7-167.7z"></path><path fill="#FF6E83" d="M58.7 242.6c-.6 0-1.3 0-1.9-.1c-12.9-1.1-22.5-12.4-21.5-25.3c3.8-45.9 36.5-83.5 81.5-93.5c12.6-2.8 25.2 5.1 28 17.8c2.8 12.6-5.1 25.2-17.8 28c-24.8 5.5-42.9 26.3-45 51.6c-1 12.2-11.2 21.5-23.3 21.5z"></path></svg>
                                        }
                                        else
                                        {
                                            <svg width="20" height="20" viewBox="0 0 20 19" @onclick="@(() => ToggleFavorite(rp))"><use href="images/icons.svg#favorite-inactive" /></svg>
                                        }
                                    </div>
                                    <div class="btn-add-container">
                                        <button class="btn-add" @onclick="@(() => AddRecipeToMenu(rp.RecipeId))">Add</button>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
            else
            {
                <div class="loader">
                    <Loader />
                </div>
            }
        }
    }
</div>

@code {
    private List<string> dietFilter = new List<string>();
    private List<RecipePartial> filteredRecipeResults = new List<RecipePartial>();
    private bool isLoading;
    private List<RecipePartial> recipeResults = new List<RecipePartial>();
    private string searchText;
    private ElementReference searchTextRef;
    private RecipeAddMode selectedMode = RecipeAddMode.Search;
    private Recipe selectedRecipe;
    private bool firstSearchDone;

    private enum RecipeAddMode
    {
        Search,
        Favorites
    }

    [Parameter]
    public EventCallback<bool> OnClose { get; set; }

    [Parameter]
    public EventCallback<string> OnMessageChanged { get; set; }

    [Parameter]
    public EventCallback<MiscUtility.AlertMessageType> OnMessageTypeChanged { get; set; }

    [Parameter]
    public EventCallback<string> OnRecipeAdded { get; set; }

    [Parameter]
    public EventCallback<Recipe> OnSeeRecipe { get; set; }

    [Parameter]
    public User CurrentUser { get; set; }

    [Parameter]
    public Brand LogoBrand { get; set; }

    [Parameter]
    public List<Diet> Diets { get; set; }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await searchTextRef.FocusAsync();
        }
    }

    private async Task AddFavorite(RecipePartial recipe)
    {
        if (!CurrentUser.UserFavorites.Where(x => x.RecipeId == recipe.RecipeId).Any())
        {
            CurrentUser.UserFavorites.Add(recipe);
        }
        else
        {
            CurrentUser.UserFavorites.Remove(recipe);
        }

        await UpdateUser();
    }

    private async Task AddRecipeToMenu(string recipeId)
    {
        await OnRecipeAdded.InvokeAsync(recipeId);
        await CloseDialog();
    }

    private void ApplyDietFilter(string dietName)
    {
        if (dietFilter.Contains(dietName))
        {
            dietFilter.Remove(dietName);
        }
        else
        {
            dietFilter.Add(dietName);
        }
        FilterRecipeResults();
    }

    private async Task CloseDialog()
    {
        await OnClose.InvokeAsync(false);
    }

    private void FilterRecipeResults()
    {
        filteredRecipeResults.Clear();

        if (dietFilter.Any())
        {
            foreach (RecipePartial rp in recipeResults)
            {
                bool containsDiet = false;

                foreach (string diet in rp.RecipeDiets)
                {
                    if (dietFilter.Contains(diet))
                    {
                        containsDiet = true;
                        break;
                    }
                }

                if (containsDiet)
                {
                    filteredRecipeResults.Add(rp);
                }
            }
        }
        else
        {
            foreach (RecipePartial rp in recipeResults)
            {
                filteredRecipeResults.Add(rp);
            }
        }
    }

    private async Task GetRecipe(string recipeId)
    {
        selectedRecipe = await HttpClient.GetFromJsonAsync<Recipe>("/api/Recipe/" + recipeId);
        await OnSeeRecipe.InvokeAsync(selectedRecipe);
    }

    private async Task OnInputEnter(KeyboardEventArgs e)
    {
        if (e.Code == "Enter" || e.Code == "NumpadEnter")
        {
            isLoading = true;
            //Delay is here because of a timing issue causing the searchText to be null on the first entry
            await Task.Delay(100);
            await SearchRecipes();
        }
    }

    private async Task SearchRecipes()
    {
        firstSearchDone = true;

        isLoading = true;
        HttpResponseMessage response = await HttpClient.PostAsJsonAsync<string>("/api/PartialRecipesByName", searchText);

        if (response.IsSuccessStatusCode)
        {
            recipeResults = await response.Content.ReadFromJsonAsync<List<RecipePartial>>() ?? new List<RecipePartial>();
            FilterRecipeResults();
        }
        else
        {
            await OnMessageChanged.InvokeAsync("An error occurred searching for recipes: " + Environment.NewLine + await response.Content.ReadAsStringAsync());
            await OnMessageTypeChanged.InvokeAsync(MiscUtility.AlertMessageType.Error);
        }
        isLoading = false;
    }

    private async Task SetMode(RecipeAddMode mode)
    {
        selectedMode = mode;
        if (selectedMode == RecipeAddMode.Search)
        {
            await Task.Delay(100);
            await searchTextRef.FocusAsync();
        }
    }

    private async void ToggleFavorite(RecipePartial currentRecipe)
    {
        if (CurrentUser.UserFavorites == null)
        {
            CurrentUser.UserFavorites = new List<RecipePartial>();
            CurrentUser.UserFavorites.Add(new RecipePartial()
                {
                    RecipeId = currentRecipe.RecipeId,
                    RecipeImageId = currentRecipe.RecipeImageId,
                    RecipeName = currentRecipe.RecipeName
                });
        }
        else
        {
            if (CurrentUser.UserFavorites.Where(x => x.RecipeId == currentRecipe.RecipeId).Any())
            {
                CurrentUser.UserFavorites = CurrentUser.UserFavorites.Where(x => x.RecipeId != currentRecipe.RecipeId).ToList();
            }
            else
            {
                CurrentUser.UserFavorites.Add(new RecipePartial()
                    {
                        RecipeId = currentRecipe.RecipeId,
                        RecipeImageId = currentRecipe.RecipeImageId,
                        RecipeName = currentRecipe.RecipeName
                    });
            }
        }
        await UpdateUser();
    }

    private async Task UpdateUser()
    {
        isLoading = true;
        HttpResponseMessage response = await HttpClient.PutAsJsonAsync<User>("/api/UpdateUser", CurrentUser);
        isLoading = false;

        if (!response.IsSuccessStatusCode)
        {
            await OnMessageChanged.InvokeAsync("An error occurred saving data.");
            await OnMessageTypeChanged.InvokeAsync(MiscUtility.AlertMessageType.Error);
        }
        await InvokeAsync(StateHasChanged);
    }
}