.background {
  background-image: url("images/background-image.png");
  height: 100vh;
  width: 100%;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
}

.wrapper {
  position: absolute;
  background-color: white;
  box-shadow: rgba(0, 0, 0, 0.05);
  width: calc(100% - 55%);
  height: auto;
  border-radius: 12px;
  top: 50%;
  left: 73%;
  transform: translate(-50%, -50%);
  right: 70px;
  padding: 50px;
  font-size: 30px;
}

@media screen and (max-width: 600px) {
  .wrapper {
    width: calc(100% - 10%);
    border-radius: 15px;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 20px;
    font-size: 20px;
  }
}
