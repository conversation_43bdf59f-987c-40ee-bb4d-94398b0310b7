@attribute [Authorize]

@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@using Newtonsoft.Json.Linq
@using TwentyDishes.Shared.Entities
@using System.Security.Claims;
@using TwentyDishes.Client.State.MenuGlobalState

@inject HttpClient HttpClient
@inject HttpClientAuth0 HttpClientAuth0
@inject NavigationManager NavManager

@if (CurrentUser != null && !isLoading)
{
    <div class="plan-settings">
        <p class="setup-text">
            How many people are in your family?
        </p>
        <div class="setup-numeric">
            <SfNumericTextBox TValue="byte" Min="1" @bind-Value="CurrentUser.FamilySize"></SfNumericTextBox>
        </div>
        <p class="setup-text">
            Choose Your Plan
        </p>

        <div class="diet-input-container">
            <div class="diet-input-box" @onclick="ToggleDietDropdown">
                <span class="toggle-adj dropdown-toggle"></span>
                <span class="selected-text">
                    @if (CurrentUser.UserDiets.Any())
                    {                        
                        @foreach (string item in CurrentUser.UserDiets)
                        {
                            <img class="diet-dropdown-icon" src=@($"/images/DietIcons/{GetIconByDietName(item)}") />
                            <span class="diet-items">@item</span>
                            break;
                        }                      
                    }
                    else
                    {
                        <span>Please Select a Diet..</span>
                    }
                </span>
            </div>

            <div class="diet-input-dropdown" style=@(isDropdownOpen == false ? "display: none" : "display: block")>
                @foreach (var diet in diets)
                {
                    <div class="diet-items-container" @onclick=@(() => SetDiet(diet.Name))>

                        <img class="diet-dropdown-icon" src=@($"/images/DietIcons/{GetIconByDietName(diet.Name)}") />
                        <span class="diet-items">@diet.Name</span>

                    </div>
                }
            </div>
        </div>

        <p class="setup-text">
            Are there any food groups that you prefer to avoid? Check all that apply.
        </p>
        <div class="chk-group">
            @foreach (string fc in FoodCategories)
            {
                <div class="chk-inner-wrapper">
                    <input type="checkbox" checked="@(CurrentUser.UserFoodCategoryExclusions.Contains(fc))" @onchange="@(() => SetFoodCategory(fc))" />
                    <label>@fc</label>
                </div>
            }
        </div>
        <p class="setup-text">
            Search for any additional ingredients that you would like to exclude.
        </p>
        <div class="add-text">
            Please note that by excluding ingredients you may not receive the
            complete provided meal plan each week. This is due to the fact that
            our meal plans do not take into account individual restrictions and
            recipes may contain some of these items. These recipes will no
            longer show on your weekly prepared meal plan.
        </div>
        <div class="search-ingr">
            <SfAutoComplete TValue="string" TItem="string" Placeholder="Type Here" DataSource="@Ingredients" ValueChanged="@((args) => SetIngredientException(args))" />
        </div>
        <div class="ingr-exc-list">
            <SfListView DataSource="CurrentUser.UserIngredientExclusions">
                <ListViewFieldSettings TValue="string"></ListViewFieldSettings>
                <ListViewTemplates TValue="string">
                    <Template>
                        <div class="listView-template" @onclick="@(() => RemoveIngredientException(context))">
                            <span>@context</span>
                            <a class="ps-3">
                                <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.99999 5.586L11.95 0.636002L13.364 2.05L8.41399 7L13.364 11.95L11.95 13.364L6.99999 8.414L2.04999 13.364L0.635986 11.95L5.58599 7L0.635986 2.05L2.04999 0.636002L6.99999 5.586Z" fill="#DB792F" />
                                </svg>
                            </a>
                        </div>
                    </Template>
                </ListViewTemplates>
            </SfListView>
        </div>
        <div class="meal-settings">
            <p class="setup-text">
                Meal Settings
            </p>
            <div class="meal-selection">
                <div>
                    <input type="checkbox" @bind-value="CurrentUser.MealSelection.BreakfastEnabled" checked="@CurrentUser.MealSelection.BreakfastEnabled" />
                    <label>Generate Breakfast Recipes</label>
                </div>
                <div>
                    <input type="checkbox" @bind-value="CurrentUser.MealSelection.LunchEnabled" checked="@CurrentUser.MealSelection.LunchEnabled" />
                    <label>Generate Lunch Recipes</label>
                </div>
                <div>
                    <input type="checkbox" @bind-value="CurrentUser.MealSelection.DinnerEnabled" checked="@CurrentUser.MealSelection.DinnerEnabled" />
                    <label>Generate Dinner Recipes</label>
                </div>
                @*<div>
                    <input type="checkbox" @bind-value="CurrentUser.MealSelection.SnacksEnabled" checked="@CurrentUser.MealSelection.SnacksEnabled" />
                    <label>Generate Snack Recipes</label>
                </div>*@
            </div>
        </div>
    @*<div class="print-settings">
            <p class="setup-text">
                Print Settings
            </p>
            <div class="chk-print-one-rec">
                <input type="checkbox" @bind-value="CurrentUser.PrintOneRecipePerPage" checked="@CurrentUser.PrintOneRecipePerPage" />
                <label class="ps-2">Print one recipe per page</label>
            </div>
            <div class="print-size-wrapper">
                <label class="lbl-print-font-size">Print Font Size</label>
                <div class="setup-numeric">
                    <SfNumericTextBox TValue="byte" Min="8" Max="40" @bind-Value="CurrentUser.PrintFontSize"></SfNumericTextBox>
                </div>
            </div>
        </div>*@
        <div button-wrapper>
            <button class="btn-secondary save-button" @onclick="CancelEdit">Cancel</button>
            <button class="btn-primary save-button" @onclick="SaveSettings">Save & Finish</button>
        </div>
    </div>
}

@code {
    private byte familySize;
    private bool foodCategoriesSelected;
    private bool isLoading = true;
    private string nameFirst;
    private string nameLast;
    private UserMealSelection originalMealSelection = new UserMealSelection();
    private List<Diet> diets = new List<Diet>();
    private byte printFontSize;
    private bool printOneRecPerPage;
    public bool isDropdownOpen;
    private byte setupStep;
    private string userId;

    [Parameter]
    public User CurrentUser { get; set; }

    [Parameter]
    public List<Diet> Diets { get; set; }

    [Parameter]
    public List<string> FoodCategories { get; set; }

    [Parameter]
    public List<string> Ingredients { get; set; }

    [Parameter]
    public EventCallback<string> OnMessageChanged { get; set; }

    [Parameter]
    public EventCallback<MiscUtility.AlertMessageType> OnMessageTypeChanged { get; set; }

    [Parameter]
    public EventCallback<Settings.MemberSetting> OnSettingsClosed { get; set; }

    [CascadingParameter(Name = MenuGlobalStateFields.SetSelectedUserWeek)]
    public EventCallback<UserWeek?> SetSelectedUserWeek {get; set;}

    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(100);
        diets = await HttpClient.GetFromJsonAsync<List<Diet>>("/api/Diets") ?? new List<Diet>();
        await InvokeAsync(() => isLoading = false);
        await InvokeAsync(() => isDropdownOpen = false);
    }

    protected override async Task OnParametersSetAsync()
    {
        await Task.Delay(100);
        await InvokeAsync(() => SetOriginalMealSelection());
    }

    private async Task CancelEdit()
    {
        await OnSettingsClosed.InvokeAsync(Settings.MemberSetting.General);
    }

    private void RemoveIngredientException(string args)
    {
        if (CurrentUser.UserIngredientExclusions.Contains(args))
        {
            CurrentUser.UserIngredientExclusions.Remove(args);
        }
    }

    private async Task SaveSettings()
    {
        await InvokeAsync(() => isLoading = true);
        //Regenerate user weeks if the meal selection has changed from its original selection
        if (originalMealSelection.BreakfastEnabled != CurrentUser.MealSelection.BreakfastEnabled || originalMealSelection.DinnerEnabled != CurrentUser.MealSelection.DinnerEnabled
            || originalMealSelection.LunchEnabled != CurrentUser.MealSelection.LunchEnabled || originalMealSelection.SnacksEnabled != CurrentUser.MealSelection.SnacksEnabled)
        {
            CurrentUser.UserWeeks = new List<UserWeek>();
            // await SessionStorage.RemoveItemAsync("SelectedUserWeek");
            await SetSelectedUserWeek.InvokeAsync(null);
        }

        HttpResponseMessage response = await HttpClient.PutAsJsonAsync<User>("/api/UpdateUser", CurrentUser);
        await InvokeAsync(() => isLoading = false);

        if (response.IsSuccessStatusCode)
        {
            await OnMessageChanged.InvokeAsync("Preferences saved successfully!");
            await OnMessageTypeChanged.InvokeAsync(MiscUtility.AlertMessageType.Success);
        }
        else
        {
            await OnMessageChanged.InvokeAsync("An error occurred saving your Preferences.");
            await OnMessageTypeChanged.InvokeAsync(MiscUtility.AlertMessageType.Error);
        }

        await OnSettingsClosed.InvokeAsync(Settings.MemberSetting.General);
    }

    private void SetDiet(string dietName)
    {
        //Add/Remove user diets based on what's already in the list
        // if (CurrentUser.UserDiets.Where(x => x == dietName).Any())
        // {
        //     CurrentUser.UserDiets.Remove(CurrentUser.UserDiets.Where(x => x == dietName).Select(x => x).First());
        // }
        // else
        // {
        //     CurrentUser.UserDiets.Add(dietName);
        // }

        // Restriciting to user to select only one diet, hence storing only one diet.
        CurrentUser.UserDiets.Clear();
        CurrentUser.UserDiets.Add(dietName);
        isDropdownOpen = false;
    }

    private void SetFoodCategory(string foodCategoryName)
    {
        //Add/Remove user food categories based on what's already in the list
        if (CurrentUser.UserFoodCategoryExclusions.Contains(foodCategoryName))
        {
            CurrentUser.UserFoodCategoryExclusions.Remove(foodCategoryName);
        }
        else
        {
            CurrentUser.UserFoodCategoryExclusions.Add(foodCategoryName);
        }
    }


    private void ToggleDietDropdown()
    {
        if (isDropdownOpen == true)
        {
            isDropdownOpen = false;
        }
        else
        {
            isDropdownOpen = true;
        }

    }


    private string GetIconByDietName(string dietName)
    {
        return dietName + ".svg";
    }

    private void SetIngredientException(string args)
    {
        if (!CurrentUser.UserIngredientExclusions.Contains(args) && !string.IsNullOrWhiteSpace(args))
        {
            CurrentUser.UserIngredientExclusions.Add(args);
        }
    }

    private void SetOriginalMealSelection()
    {
        originalMealSelection.BreakfastEnabled = CurrentUser.MealSelection.BreakfastEnabled;
        originalMealSelection.DinnerEnabled = CurrentUser.MealSelection.DinnerEnabled;
        originalMealSelection.LunchEnabled = CurrentUser.MealSelection.LunchEnabled;
        originalMealSelection.SnacksEnabled = CurrentUser.MealSelection.SnacksEnabled;
    }
}

