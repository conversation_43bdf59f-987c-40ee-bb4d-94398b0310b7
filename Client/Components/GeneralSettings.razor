@attribute [Authorize]

@using Newtonsoft.Json.Linq
@using System.Security.Claims;
@using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent

@inherits UserBaseComponent

@inject HttpClient HttpClient
@inject HttpClientAuth0 HttpClientAuth0

<div class="main-container">
    <Banner @bind-MessageType="@messageType" @bind-Message="@message" />
    <h3 class="user-info-h3">User Information</h3>
    <hr />
    @if (currentUser != null)
    {
        <div class="info-container">
            <div class="info-first-name">
                <label class="info-label">First Name</label>
                <input class="info-input" @bind-value="@currentUser.FirstName" />
            </div>
            <div class="info-last-name">
                <label class="info-label">Last Name</label>
                <input class="info-input" @bind-value="@currentUser.LastName" />
            </div>
            <div class="info-email">
                <label class="info-label">Email</label>
                <input class="info-input" @bind-value="@currentUser.EmailAddress" readonly />
            </div>
            <div class="btn-save-container">
                <button class="btn-save" @onclick="SaveSettings"><i class="oi oi-document px-2"></i> Save Changes</button>
            </div>
        </div>
    }
</div>

@code {
    private User currentUser;
    private string message;
    private MiscUtility.AlertMessageType messageType;
    private string userId;

    [Parameter]
    public EventCallback<string> OnMessageChanged { get; set; }

    [Parameter]
    public EventCallback<MiscUtility.AlertMessageType> OnMessageTypeChanged { get; set; }

    public async Task Initialize()
    {
        userId = userGlobalState.UserId;
        currentUser = userGlobalState.FullUserData;
    }

    public override async Task GlobalStateLoaded()
    {
        if (!initialized && !initializationStarted)
        {
            await Initialize();
        }
    }

    private async Task SaveSettings()
    {
        HttpResponseMessage response = await HttpClient.PutAsJsonAsync<User>("/api/UpdateUser", currentUser);

        if (response.IsSuccessStatusCode)
        {
            await OnMessageChanged.InvokeAsync("General Settings updated successfully!");
            await OnMessageTypeChanged.InvokeAsync(MiscUtility.AlertMessageType.Success);
        }
        else
        {
            await OnMessageChanged.InvokeAsync("An error occurred saving your General Settings.");
            await OnMessageTypeChanged.InvokeAsync(MiscUtility.AlertMessageType.Error);
        }
    }
}