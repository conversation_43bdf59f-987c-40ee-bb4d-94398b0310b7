.sub-info-main {
    padding: 0 20px;
}

.subsc-data-header {
    display: flex;
    flex-direction: row;
    padding: 10px 0 10px 0px;
}

.subsc-info-item-container {
    padding: 20px 20px 0 0;
}

.subsc-table-sm {
    display: none;
}

.subsc-info-item {
    font-size: 18px;
    font-weight: 600;
    border: none;
    background-color: #fff;
    color: rgba(0, 0, 0, 0.4);
    font-family: 'Raleway';
}

.subsc-info-item-focused {
    color: var(--vibrantRed);
}

.subsc-plan {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #E5DFC3;
    border-radius: 10px;
    color: var(--bs-body-color);
    padding: 15px 20px;
    width: 390px;
    height: 280px;
}

.smallsuperscript {
    vertical-align: super;
    font-size: 50%;
}

h6 {
    font-size: 16px;
    font-weight: 500;
    line-height: 19px;
    /*text-shadow: 1px 1px #212121;*/
}

.subsc-plan-price {
    font-family: 'Oswald';
    font-style: normal;
    font-weight: 600;
    font-size: 42px;
    line-height: 62px;
}

.payment-system {
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 19px;
    padding-left: 5px;
    padding-top: 7px;
    padding-bottom: 5px;    
}

.active-plan {
    position: relative;
    background-color: #fff;
    border-radius: 32px;
    color: #d1c382;
    padding: 5px 0;
    width: 121px;
    height: 34px;
    text-align: center;
    top: 5px;
    
}
    .active-plan:hover {
        background-color: #f7f7f7;
    }

.billing-hist-header {
    display: flex;
    font-family: 'Raleway';
    justify-content: space-between;
    background: rgba(170, 194, 206, 0.2);
    border-radius: 4px;
    padding: 15px 30px 5px 0px;
    margin-bottom: 15px;
    margin-top: 30px;
    /*max-width: 1000px;*/
    width: auto;
    height: 55px;
}

.header-item-150 {
    width: 150px;
    text-align: center;
}

.header-item-130 {
    width: 130px;
    text-align: center;
}

.header-item-250 {
    width: 250px;
    text-align: center;
}

.toggle-pill {
    width: 30px;
    height: 30px;
    background: rgba(202, 47, 53, 0.1);
    border-radius: 4px;
}

.billing-hist-data {
    list-style: none;
    width: auto;
    padding: 27px 20px 0 0;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(170, 194, 206, 0.5);
    border-radius: 4px;
    height: 76px;
    font-family: 'Raleway';
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 19px;
    margin-top: 17px;
}

.billing-hist-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.billing-hist-membership {
    width: 150px;
    text-align: center;
}

.billing-hist-date {
    width: 150px;
    text-align: center;
}

.billing-hist-amount {
    width: 150px;
    text-align: right;
    padding-right: 50px;
}

.billing-hist-status {
    width: 150px;
    padding: 5px 0 0 0;
    text-align: center;
}

.billing-hist-invoice-num {
    width: 250px;
    text-align: center;
}

.billing-hist-payment-method {
    width: 150px;
    text-align: center;
}

.change-subscription {
    position: relative;
    top: 30px;
}

.btn-change-subsc {
    position: relative;
    width: 200px;
    background-color: var(--vibrantRed);
    border: 1px solid var(--vibrantRed);
    border-radius: 55px;
    cursor: default;
    font-family: Raleway;
    font-weight: 600;
    color: #fff;
    padding: 10px;
}
    .btn-change-subsc:hover {
        background-color: #bb131b;
    }

@media only screen and (max-width: 375px) and (min-width: 320px) {
    .billing-hist-header {
        display: none;
    }

    .billing-hist-data {
        display: none;
    }

    .header-sm {
        background: rgba(170, 194, 206, 0.2);
        border-radius: 4px;
        padding: 10px;
        display: inline-block;
    }

    .subsc-table-sm {
        display: block;
    }

    .billing-data-sm {
        padding: 10px;
        float: right;
    }

    .subsc-plan {
        width: 250px;
        height: 300px;
        margin: 0;
    }
   
}

@media only screen and (max-width: 425px) and (min-width: 375px) {
    .billing-hist-header {
        display: none;
    }

    .billing-hist-data {
        display: none;
    }

    .header-sm {
        background: rgba(170, 194, 206, 0.2);
        border-radius: 4px;
        padding: 10px;
        display: inline-block;
    }

    .subsc-table-sm {
        display: block;
    }

    .billing-data-sm {
        padding: 10px;
        float: right;
    }

    .subsc-plan {
        width: 310px;
        height: 280px;
        margin: 0;
    }
}

@media only screen and (max-width: 600px) and (min-width: 425px) {
    .billing-hist-header {
        display: none;
    }

    .billing-hist-data {
        display: none;
    }

    .header-sm {
        background: rgba(170, 194, 206, 0.2);
        border-radius: 4px;
        padding: 10px;
        display: inline-block;
    }

    .subsc-table-sm {
        display: block;
    }

    .billing-data-sm {
        padding: 10px;
        float: right;
    }

    .subsc-plan {
        width: 300px;
        height: 400px;
        margin: 0;
    }
}

@media only screen and (max-width: 1919px) and (min-width: 1024px) {
    .billing-hist-header {
        font-size: 13px;
    }

    .billing-hist-data {
        font-size: 13px;
        padding-right: 10px;
    }

    .toggle-pill {
        width: 50px;
        padding: 3px;
        margin-left: 15px;
    }
}
