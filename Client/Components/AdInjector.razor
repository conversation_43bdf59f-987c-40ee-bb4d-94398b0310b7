@using TwentyDishes.Client.State.UserGlobalState
@using Microsoft.AspNetCore.Components.Routing

@inject IJSRuntime JsRuntime
@inject NavigationManager NavigationManager

<NavigationLock OnBeforeInternalNavigation="HandleBeforeInternalNavigation" />

@code {
    [CascadingParameter(Name = UserGlobalStateFields.Value)]
    public UserGlobalStateValue UserGlobalStateValue { get; set; }

    [Parameter]
    public string? CurrentPath { get; set; }

    public async Task HandleBeforeInternalNavigation(LocationChangingContext context)
    {
        // this will handle the ads control from the route changes

        if (UserGlobalStateValue?.UserSubscriptionDetail?.Subscription?.PaymentSystem is not null)
        {
            // disable
            if (UserGlobalStateValue.UserSubscriptionDetail.Subscription.PaymentSystem == SubscriptionHelper.PaymentSystem.Paddle
                || UserGlobalStateValue.UserSubscriptionDetail.Subscription.PaymentSystem == SubscriptionHelper.PaymentSystem.Lifetime
                || (context.TargetLocation.Contains("subscription")) || !UserGlobalStateValue.UserIsSetup)
            {
                await JsRuntime.InvokeVoidAsync("disableAdThrive");
            }
            // enable
            else
            {
                await JsRuntime.InvokeVoidAsync("enableAdThrive");

                if (!adThriveIsLoaded)
                {
                    await JsRuntime.InvokeVoidAsync("loadAdThrive", "6027f88a0df0477ac54d6afb");

                    adThriveIsLoaded = true;
                }
            }
        }
    }

    bool adThriveShouldEnable = false;
    bool adThriveShouldDisable = false;
    bool adThriveIsEnabled = false;
    bool adThriveIsLoaded = false;

    public async override Task SetParametersAsync(ParameterView parameters)
    {
        var userGlobalStateValue = parameters.GetValueOrDefault<UserGlobalStateValue>(nameof(UserGlobalStateValue));

        // this will handle the ads control from the subscription details
        // nothing with ads should happen until the subscription details are known
        if (
        userGlobalStateValue?.UserSubscriptionDetail?.Subscription?.PaymentSystem is not null &&
        (userGlobalStateValue.UserSubscriptionDetail?.Subscription?.PaymentSystem !=
        UserGlobalStateValue?.UserSubscriptionDetail?.Subscription?.PaymentSystem ||
        userGlobalStateValue?.UserIsSetup != UserGlobalStateValue?.UserIsSetup)
        && !NavigationManager.Uri.Contains("subscription")
        )
        {
            // disable
            if (userGlobalStateValue?.UserSubscriptionDetail?.Subscription?.PaymentSystem == SubscriptionHelper.PaymentSystem.Paddle
                || userGlobalStateValue?.UserSubscriptionDetail?.Subscription?.PaymentSystem == SubscriptionHelper.PaymentSystem.Lifetime
                || !userGlobalStateValue?.UserIsSetup == true)
            {
                if (adThriveIsEnabled)
                {
                    adThriveShouldDisable = true;
                }
            }
            // enable
            else
            {
                if (!adThriveIsEnabled)
                {
                    adThriveShouldEnable = true;
                }
            }
        }

        await base.SetParametersAsync(parameters);
    }

    protected async override Task OnAfterRenderAsync(bool firstRender)
    {
        if (adThriveShouldEnable)
        {
            // this is not used right now

            adThriveShouldEnable = false;

            adThriveIsEnabled = true;

            await JsRuntime.InvokeVoidAsync("enableAdThrive");

            if (!adThriveIsLoaded)
            {
                await JsRuntime.InvokeVoidAsync("loadAdThrive", "6027f88a0df0477ac54d6afb");

                adThriveIsLoaded = true;
            }
        }
        else if (adThriveShouldDisable)
        {
            // this is not used right now

            adThriveShouldDisable = false;

            adThriveIsEnabled = false;

            await JsRuntime.InvokeVoidAsync("disableAdThrive");
        }

        adThriveShouldEnable = false;
        adThriveShouldDisable = false;
    }
}