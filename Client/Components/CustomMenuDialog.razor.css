.save-menu-dialog-main {
  position: absolute;
  left: calc(40vw);
  background-color: #fff;
  top: 170px;
  margin: 0 auto;
  z-index: 100 !important;
  height: 500px;
  width: 400px;
  border-radius: 10px;
  box-shadow: 0px 2.22907px 36.2224px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
  overflow-x: hidden;
}

.new-menu-init {
  margin: 10px;
  width: 150px;
}

.new-menu-init-btn {
  height: 100%;
  width: 100%;
  color: #fff;
  background-color: var(--vibrantRed);
  border: none;
  cursor: pointer;
}

.existing-menus {
  position: relative;
  top: 20px;
  left: 20px;
}

.menu-names-label-container {
  width: 80%;
}

.menu-names-container {
  position: relative;
  margin: 10px;
  width: 300px;
}

.listView-template {
  text-align: left;
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 60px;
}

.new-menu-info-container {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  top: 20px;
  left: 20px;
  margin: 10px;
}

.new-menu-name-container {
}

.new-menu-name {
  width: 200px;
}

.new-menu-notes-container {
  position: relative;
  left: 72px;
}

.new-menu-notes {
  position: relative;
  top: 20px;
  width: 200px;
}

.new-menu-btn-container {
  position: relative;
  display: flex;
  top: 40px;
  left: 50px;
}

.btn-cancel-container {
  padding: 5px;
  margin: 20px 40px 0 0;
  width: 100px;
  background-color: var(--salesButtonOrange);
}

.btn-cancel {
  height: 100%;
  width: 100%;
  color: #fff;
  background-color: var(--salesButtonOrange);
  border: none;
  cursor: pointer;
}

.btn-continue-container {
  padding: 5px;
  margin-top: 20px;
  width: 100px;
  background-color: var(--vibrantRed);
}

.btn-continue {
  height: 100%;
  width: 100%;
  color: #fff;
  background-color: var(--vibrantRed);
  border: none;
  cursor: pointer;
}

.close-dialog {
  position: relative;
  right: 5px;
  float: right;
  cursor: pointer;
  opacity: 0.5;
  z-index: 1000;
}

@media only screen and (max-width: 375px) and (min-width: 320px) {
  .save-menu-dialog-main {
    left: 20px;
    width: 90%;
  }

  .existing-menus {
    padding: 10px;
    left: 5px;
  }

  .menu-names-container {
    position: relative;
    margin: 10px;
    width: 280px;
  }

  .new-menu-info-container {
    margin: 10px 10px 10px 20px;
  }

  .new-menu-name-container {
    position: relative;
    left: 8px;
  }

  .new-menu-notes-container {
    position: relative;
    display: flex;
    left: 20px;
  }

  .new-menu-notes {
    position: relative;
    top: 25px;
    right: 47px;
    width: 200px;
  }

  .new-menu-btn-container {
    position: relative;
    display: flex;
    top: 30px;
    left: -8px;
  }
}

@media only screen and (max-width: 425px) and (min-width: 375px) {
  .save-menu-dialog-main {
    left: 20px;
    width: 90%;
  }

  .existing-menus {
    padding: 10px;
    left: 10px;
  }

  .new-menu-info-container {
    margin: 10px 10px 10px 20px;
  }

  .new-menu-name-container {
    position: relative;
    left: 8px;
  }

  .new-menu-notes-container {
    position: relative;
    display: flex;
    left: 20px;
  }

  .new-menu-notes {
    position: relative;
    top: 25px;
    right: 47px;
    width: 200px;
  }

  .new-menu-btn-container {
    position: relative;
    display: flex;
    top: 30px;
    left: -8px;
  }
}

@media only screen and (max-width: 600px) and (min-width: 425px) {
  .save-menu-dialog-main {
    left: 20px;
    width: 90%;
  }

  .existing-menus {
    padding: 10px;
    left: 10px;
  }

  .menu-names-container {
    position: relative;
    margin: 10px;
    width: 280px;
  }

  .new-menu-info-container {
    margin: 10px 10px 10px 20px;
  }

  .new-menu-name-container {
    position: relative;
    left: 8px;
  }

  .new-menu-notes-container {
    position: relative;
    display: flex;
    left: 20px;
  }

  .new-menu-notes {
    position: relative;
    top: 25px;
    right: 47px;
    width: 200px;
  }

  .new-menu-btn-container {
    position: relative;
    display: flex;
    top: 30px;
    left: -8px;
  }
}

.custom-menu-dialog-blank {
  width: 100%;
  height: 450px;
  display: flex;
  align-items: center;
  text-align: center;
  font-family: Raleway;
  font-size: 1.5rem;
  padding: 20px;
}
