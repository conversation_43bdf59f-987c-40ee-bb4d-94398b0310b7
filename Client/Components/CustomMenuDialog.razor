@attribute [Authorize]
@inject HttpClient HttpClient

<div class="save-menu-dialog-main" style="@(IsNewMenu ? "height: 250px;" : string.Empty)">
    <div class="close-dialog">
        <svg width="20" height="20" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg"
            @onclick="CancelDialog">
            <path
                d="M6.00011 4.82166L10.1251 0.696655L11.3034 1.87499L7.17844 5.99999L11.3034 10.125L10.1251 11.3033L6.00011 7.17832L1.87511 11.3033L0.696777 10.125L4.82178 5.99999L0.696777 1.87499L1.87511 0.696655L6.00011 4.82166Z"
                fill="black" />
        </svg>
    </div>
    @if (!IsNewMenu)
    {
        @if (isLoading)
        {
            <div class="loader">
                <Loader />
            </div>
        }
        else if (CurrentUser != null && CurrentUser.UserMenus != null && CurrentUser.UserMenus.Any())
        {
            <div class="existing-menus">
                <div class="menu-names-label-container">
                    <label>Select a saved menu:</label>
                </div>
                <div class="menu-names-container">
                    <SfListView DataSource="@userMenuNames">
                        <ListViewFieldSettings TValue="string"></ListViewFieldSettings>
                        <ListViewTemplates TValue="string">
                            <Template>
                                <div class="listView-template" @onclick="@(() => SelectMenu(context))">
                                    <span>@context</span>
                                    <a class="ps-3" @onclick:stopPropagation>
                                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg" @onclick="@(() => DeleteUserMenu(context))">
                                            <path
                                                d="M6.99999 5.586L11.95 0.636002L13.364 2.05L8.41399 7L13.364 11.95L11.95 13.364L6.99999 8.414L2.04999 13.364L0.635986 11.95L5.58599 7L0.635986 2.05L2.04999 0.636002L6.99999 5.586Z"
                                                fill="#DB792F" />
                                        </svg>
                                    </a>
                                </div>
                            </Template>
                        </ListViewTemplates>
                    </SfListView>
                </div>
            </div>
        }
        else
        {
            <div class="custom-menu-dialog-blank">
                Nothing yet - save a custom week to get started!
            </div>
        }
    }
    else
    {
        <div class="new-menu-info-container">
            <div class="new-menu-name-container">
                <label>
                    <span style="color: red;">
                        *
                    </span>New Menu Name:
                </label>
                <input class="new-menu-name" type="text" @ref="txtMenuName" @bind="@newMenuName" />
            </div>
            <div class="new-menu-notes-container">
                <label>Notes:</label>
                <textarea class="new-menu-notes" @bind="@newMenuNotes" />
            </div>
            <div class="new-menu-btn-container">
                <div class="btn-cancel-container">
                    <button class="btn-cancel" @onclick="CancelDialog">Cancel</button>
                </div>
                <div class="btn-continue-container">
                    <button class="btn-continue" @onclick="SetMenuInfo">Continue</button>
                </div>
            </div>
        </div>
        @if (isLoading)
        {
            <div class="loader">
                <Loader />
            </div>
        }
    }
</div>

@code {
    private bool isLoading;
    private string newMenuName;
    private string newMenuNotes;
    private ElementReference txtMenuName;
    private List<string> userMenuNames;

    [Parameter]
    public User CurrentUser { get; set; }

    [Parameter]
    public bool IsNewMenu { get; set; }

    [Parameter]
    public EventCallback OnDialogCanceled { get; set; }

    [Parameter]
    //boolean - true means saved, false means canceled. string is the name of the menu to be used when the dialog is closed.
    public EventCallback<(bool, string)> OnDialogClosed { get; set; }


    [Parameter]
    public EventCallback<string> OnMessageChanged { get; set; }

    [Parameter]
    public EventCallback<MiscUtility.AlertMessageType> OnMessageTypeChanged { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        await Task.Delay(100);
        if (!IsNewMenu)
        {
            await InvokeAsync(() => SetMenuNames());
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await Task.Delay(100);
            if (IsNewMenu)
            {
                await txtMenuName.FocusAsync();
            }
        }
    }

    private async Task CancelDialog()
    {
        await OnDialogCanceled.InvokeAsync();
    }

    private async Task DeleteUserMenu(string menuName)
    {
        if (CurrentUser.UserMenus != null && !string.IsNullOrWhiteSpace(menuName))
        {
            CurrentUser.UserMenus.RemoveAll(x => x.MenuName == menuName);
            await SaveSettings();
            await InvokeAsync(SetMenuNames);
        }
    }

    private async Task SaveSettings()
    {
        isLoading = true;
        HttpResponseMessage response = await HttpClient.PutAsJsonAsync<User>("/api/UpdateUser", CurrentUser);

        if (!response.IsSuccessStatusCode)
        {
            await OnMessageChanged.InvokeAsync("An error occurred saving your menu.");
            await OnMessageTypeChanged.InvokeAsync(MiscUtility.AlertMessageType.Error);
        }
        isLoading = false;
    }

    private async Task SelectMenu(string menuName)
    {
        await OnDialogClosed.InvokeAsync((false, menuName));
    }

    private async Task SetMenuInfo()
    {
        @if (!string.IsNullOrWhiteSpace(newMenuName) && (CurrentUser.UserMenus == null || !CurrentUser.UserMenus.Where(x =>
x.MenuName == newMenuName).Any()))
        {
            UserMenu newMenu = new UserMenu()
                {
                    IsShared = false,
                    LastUpdated = DateTime.Now,
                    MenuName = newMenuName,
                    Notes = newMenuNotes,
                    UserWeek = new UserWeek()
                    {
                        FridayBreakfast = new List<string>(),
                        FridayDinner = new List<string>(),
                        FridayLunch = new List<string>(),
                        FridaySnacks = new List<string>(),
                        MondayBreakfast = new List<string>(),
                        MondayDinner = new List<string>(),
                        MondayLunch = new List<string>(),
                        MondaySnacks = new List<string>(),
                        SaturdayBreakfast = new List<string>(),
                        SaturdayDinner = new List<string>(),
                        SaturdayLunch = new List<string>(),
                        SaturdaySnacks = new List<string>(),
                        SundayBreakfast = new List<string>(),
                        SundayDinner = new List<string>(),
                        SundayLunch = new List<string>(),
                        SundaySnacks = new List<string>(),
                        ThursdayBreakfast = new List<string>(),
                        ThursdayDinner = new List<string>(),
                        ThursdayLunch = new List<string>(),
                        ThursdaySnacks = new List<string>(),
                        TuesdayBreakfast = new List<string>(),
                        TuesdayDinner = new List<string>(),
                        TuesdayLunch = new List<string>(),
                        TuesdaySnacks = new List<string>(),
                        WednesdayBreakfast = new List<string>(),
                        WednesdayDinner = new List<string>(),
                        WednesdayLunch = new List<string>(),
                        WednesdaySnacks = new List<string>()
                    }
                };

            if (CurrentUser.UserMenus == null)
            {
                CurrentUser.UserMenus = new List<UserMenu>();
            }

            CurrentUser.UserMenus.Add(newMenu);
            CurrentUser.UserMenus = CurrentUser.UserMenus.OrderBy(x => x.MenuName).ToList();
            await OnDialogClosed.InvokeAsync((true, newMenu.MenuName));
        }
    }

    private void SetMenuNames()
    {
        if (CurrentUser.UserMenus != null)
        {
            userMenuNames = CurrentUser.UserMenus.Select(x => x.MenuName).ToList();
        }
        else
        {
            userMenuNames = new List<string>();
        }
    }
}