@attribute [AllowAnonymous]
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.WebAssembly.Authentication
@inject NavigationManager Navigation
@inject SignOutSessionStateManager SignOutManager

@if(isBackgroundTransparent == true){
	<AuthorizeView>
		<Authorized>
			<button class="btn-login-logout-without-bg" @onclick="BeginSignOut">
				<span class="logout-text">Log Out</span>
			</button>
		</Authorized>
		<NotAuthorized>
			<button class="btn-login-logout-without-bg" @onclick="BeginSignIn">
				<span>Login</span>
			</button>
		</NotAuthorized>
	</AuthorizeView>
}
else
{
	<AuthorizeView>
		<Authorized>
			<button class="btn-login-logout" @onclick="BeginSignOut">
				<span class="logout-text">Log Out</span>
			</button>
		</Authorized>
		<NotAuthorized>
			<button class="btn-login-logout" @onclick="BeginSignIn">
				<span>Login</span>
			</button>
		</NotAuthorized>
	</AuthorizeView>
}

@code {
	[Parameter]
	public bool? isBackgroundTransparent { get; set; }
	private void BeginSignIn()
	{
		Navigation.NavigateTo("authentication/login");
	}

	private async Task BeginSignOut(MouseEventArgs args)
	{
		await SignOutManager.SetSignOutState();
		Navigation.NavigateTo("authentication/logout");
	}
}