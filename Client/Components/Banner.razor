@using TwentyDishes.Shared.Classes

@if (!string.IsNullOrWhiteSpace(message))
{
 <div class="banner">
    <div class="alert @bannerClass fade show">
        <span>@message</span>
       <div class = "float-end">
           <button class="btn btn-close" @onclick="Dismiss"></button>
       </div>
    </div>
</div>
}

@code {
    private string bannerClass = "";
    private int dismissalTimeError;
    private int dismissalTimeSuccess = 4000; //4 seconds by default
    private int dismissalTimeWarning;
    private string message = "";
    private MiscUtility.AlertMessageType messageType;

    [Parameter]
    public EventCallback<string> MessageChanged { get; set; }

    [Parameter]
    public EventCallback<MiscUtility.AlertMessageType> MessageTypeChanged { get; set; }

    private event System.Action OnMessageChanged;

    [Parameter]
    public int DismissalTimeError
    {
        get => dismissalTimeError;
        set => dismissalTimeError = value * 1000;
    }

    [Parameter]
    public int DismissalTimeSuccess
    {
        get => dismissalTimeSuccess;
        set => dismissalTimeSuccess = value * 1000;
    }

    [Parameter]
    public int DismissalTimeWarning
    {
        get => dismissalTimeWarning;
        set => dismissalTimeWarning = value * 1000;
    }

    [Parameter]
    public string Message
    {
        get => message;
        set
        {
            if (value == message) { return; }
            message = value;
            MessageChanged.InvokeAsync(value);
            OnMessageChanged?.Invoke();
        }
    }

    [Parameter]
    public MiscUtility.AlertMessageType MessageType
    {
        get => messageType;
        set
        {
            switch (value)
            {
                case MiscUtility.AlertMessageType.Error:
                    //bannerClass = "banner-error";
                    bannerClass = "alert-danger";
                    break;
                case MiscUtility.AlertMessageType.Success:
                    //bannerClass = "banner-success";
                    bannerClass = "alert-success";
                    break;
                case MiscUtility.AlertMessageType.Warning:
                    //bannerClass = "banner-warning";
                    bannerClass = "alert-warning";
                    break;
                default:
                    throw new NotImplementedException("Alert message type: " + messageType.ToString() + " is not implemented.");
            }
            if (value == messageType) { return; }
            messageType = value;
            MessageTypeChanged.InvokeAsync(value);
        }
    }

    protected override void OnInitialized()
    {
        OnMessageChanged += async () => await InvokeDelayedDismissal();
    }

    private void Dismiss()
    {
        Message = null;
    }

    private async Task InvokeDelayedDismissal()
    {
        if (!string.IsNullOrWhiteSpace(message))
        {
            switch (messageType)
            {
                case MiscUtility.AlertMessageType.Error:
                    await Task.Delay(dismissalTimeError);
                    if (dismissalTimeError > 0)
                    {
                        Dismiss();
                    }
                    break;
                case MiscUtility.AlertMessageType.Success:
                    await Task.Delay(dismissalTimeSuccess);
                    if (dismissalTimeSuccess > 0)
                    {
                        Dismiss();
                    }
                    break;
                case MiscUtility.AlertMessageType.Warning:
                    await Task.Delay(dismissalTimeWarning);
                    if (dismissalTimeWarning > 0)
                    {
                        Dismiss();
                    }
                    break;
                default:
                    throw new NotImplementedException("Alert message type: " + messageType.ToString() + " is not implemented.");
            }
        }
    }
}