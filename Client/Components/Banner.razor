@using TwentyDishes.Shared.Enums
@inject IJSRuntime JSRuntime

<div class="banner-container">
    @if (!string.IsNullOrEmpty(Message))
    {
        <div class="banner @GetBannerClass()" style="display: @(IsVisible ? "block" : "none")">
            <div class="banner-content">
                <span class="banner-message">@Message</span>
                <button class="banner-close" @onclick="DismissBanner">×</button>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public string? Message { get; set; }
    [Parameter] public BannerType MessageType { get; set; } = BannerType.Info;
    [Parameter] public int DismissalTimeError { get; set; } = 5000;
    [Parameter] public int DismissalTimeSuccess { get; set; } = 3000;
    [Parameter] public int DismissalTimeWarning { get; set; } = 4000;
    [Parameter] public EventCallback<string?> OnMessageChanged { get; set; }

    private bool IsVisible { get; set; } = true;
    private Timer? dismissalTimer;

    protected override void OnParametersSet()
    {
        if (!string.IsNullOrEmpty(Message))
        {
            IsVisible = true;
            SetDismissalTimer();
        }
    }

    private void SetDismissalTimer()
    {
        dismissalTimer?.Dispose();
        
        int delay = MessageType switch
        {
            BannerType.Error => DismissalTimeError,
            BannerType.Success => DismissalTimeSuccess,
            BannerType.Warning => DismissalTimeWarning,
            _ => 3000
        };

        dismissalTimer = new Timer(async _ => await DismissBanner(), null, delay, Timeout.Infinite);
    }

    private async Task DismissBanner()
    {
        IsVisible = false;
        await InvokeAsync(StateHasChanged);
        await OnMessageChanged.InvokeAsync(null);
        dismissalTimer?.Dispose();
    }

    private string GetBannerClass()
    {
        return MessageType switch
        {
            BannerType.Error => "banner-error",
            BannerType.Success => "banner-success",
            BannerType.Warning => "banner-warning",
            _ => "banner-info"
        };
    }

    public void Dispose()
    {
        dismissalTimer?.Dispose();
    }
}

<style>
    .banner-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
    }

    .banner {
        padding: 12px 16px;
        margin: 8px;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .banner-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .banner-message {
        flex: 1;
        margin-right: 16px;
    }

    .banner-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .banner-error {
        background-color: #fee;
        color: #c53030;
        border-left: 4px solid #c53030;
    }

    .banner-success {
        background-color: #f0fff4;
        color: #38a169;
        border-left: 4px solid #38a169;
    }

    .banner-warning {
        background-color: #fffbeb;
        color: #d69e2e;
        border-left: 4px solid #d69e2e;
    }

    .banner-info {
        background-color: #ebf8ff;
        color: #3182ce;
        border-left: 4px solid #3182ce;
    }
</style>
