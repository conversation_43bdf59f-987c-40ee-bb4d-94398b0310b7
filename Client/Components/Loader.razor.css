.loader-3 {
    display: block;
    height: 32px;
    width: 32px;
}

    .loader-3 span {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        height: 120px;
        width: 120px;
    }

        .loader-3 span::before {
            content: "";
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            margin: auto;
            height: 50px;
            width: 50px;
            border: 4px solid rgba(219, 121, 47, 0.1);            
            border-bottom: 3px solid transparent;
            border-radius: 50%;
            -webkit-animation: loader-3-1 1.5s cubic-bezier(0.770, 0.000, 0.175, 1.000) infinite;
            animation: loader-3-1 1.5s cubic-bezier(0.770, 0.000, 0.175, 1.000) infinite;
        }

@-webkit-keyframes loader-3-1 {
    0% {
        -webkit-transform: rotate(0deg);
    }

    40% {
        -webkit-transform: rotate(180deg);
    }

    60% {
        -webkit-transform: rotate(180deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes loader-3-1 {
    0% {
        transform: rotate(0deg);
    }

    40% {
        transform: rotate(180deg);
    }

    60% {
        transform: rotate(180deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loader-3 span::after {
    content: "";
    position: absolute;
    top: 0;
    left: 1px;
    bottom: 0;
    right: 0;
    margin: auto;
    width: 8px;
    height: 8px;
    background: #DB792F; 
    border-radius: 50%;
    -webkit-animation: loader-3-2 1.5s cubic-bezier(0.770, 0.000, 0.175, 1.000) infinite;
    animation: loader-3-2 1.5s cubic-bezier(0.770, 0.000, 0.175, 1.000) infinite;
}

@-webkit-keyframes loader-3-2 {
    0% {
        -webkit-transform: translate3d(0, -32px, 0) scale(0, 2);
        opacity: 0;
    }

    50% {
        -webkit-transform: translate3d(0, 0, 0) scale(1.25, 1.25);
        opacity: 1;
    }

    100% {
        -webkit-transform: translate3d(0, 8px, 0) scale(0, 0);
        opacity: 0;
    }
}

@keyframes loader-3-2 {
    0% {
        transform: translate3d(0, -32px, 0) scale(0, 2);
        opacity: 0;
    }

    50% {
        transform: translate3d(0, 0, 0) scale(1.25, 1.25);
        opacity: 1;
    }

    100% {
        transform: translate3d(0, 8px, 0) scale(0, 0);
        opacity: 0;
    }
}




      

/*
body {
    background-color: #f1c40f;
    margin: 0;
    padding: 0;
}

.cup {
    width: 150px;
    height: 120px;
    border: 5px solid #DB792F;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    border-radius: 0 0 60px 60px;
    background: url(https://i.postimg.cc/76GJvK5x/coffee.png);
    background-repeat: repeat-x;
    animation: fill 3s infinite;
}

@keyframes fill {
    0% {
        background-position: 0 140px;
    }

    20% {
        background-position: -450px 100px;
    }

    40% {
        background-position: -900px 50px;
    }

    80% {
        background-position: -1350px -50px;
    }

    100% {
        background-position: 0 140px;
    }
}

.h {
    height: 50px;
    width: 28px;
    border: 5px solid #DB792F;
    position: relative;
    left: 141px;
    top: 3px;
    border-radius: 0 20px 20px 0;
}*/





@media only screen and (max-width: 375px) and (min-width: 320px) {
}