using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using TwentyDishes.Client.State.PublicGlobalState;
using TwentyDishes.Shared.Entities;
using TwentyDishes.Shared.Enums;

namespace TwentyDishes.Client.Components.ExportMenuPdfButton
{
    public partial class ExportMenuPdfButton
    {
        [Inject]
        protected IHttpClientFactory HttpClientFactory { get; set; } = default!;

        [Inject]
        protected IJSRuntime JSRuntime { get; set; } = default!;

        private bool Exporting { get; set; }

        [Parameter]
        public RenderFragment? ChildContent { get; set; }

        [Parameter]
        public UserWeek? UserWeek { get; set; }

        [Parameter]
        public List<string>? AdditionalRecipeIds { get; set; }

        [Parameter]
        public DateTime? WeekStartDate { get; set; }

        [Parameter]
        public MenuPdfFormat? Format { get; set; }

        [Parameter]
        public bool IncludeMealPlan { get; set; } = true;

        [Parameter]
        public bool IncludeShoppingList { get; set; } = true;

        [Parameter]
        public bool IncludeRecipes { get; set; } = true;

        [Parameter]
        public bool IncludePrepGuide { get; set; } = true;

        Brand? logo => PublicGlobalStateValue.Brand;

        [Parameter]
        public List<ShoppingListItem>? AdditionalIngredients { get; set; }

        [CascadingParameter(Name = PublicGlobalStateFields.Value)]
        public PublicGlobalStateValue PublicGlobalStateValue {get; set;}

        private async Task PrintReport()
        {
            Console.WriteLine("clicked");

            if (!WeekStartDate.HasValue) return;

            Exporting = true;

            try
            {
                var client = HttpClientFactory.CreateClient("MainClient");

                HttpResponseMessage response = await client.PostAsJsonAsync("/api/CreateMenuPdf",
                    new CreateMenuPdfRequest
                    {
                        UserWeek = UserWeek,
                        AdditionalRecipeIds = AdditionalRecipeIds,
                        WeekStartDate = WeekStartDate.Value,
                        IncludeMealPlan = IncludeMealPlan,
                        IncludeShoppingList = IncludeShoppingList,
                        IncludePrepGuide = IncludePrepGuide,
                        IncludeRecipes = IncludeRecipes,
                        AdditionalIngredients = AdditionalIngredients,
                        Format = Shared.Enums.MenuPdfFormat.FullMenu,
                        BrandId = logo?.Id ?? 0
                    }
                );

                var fileContent = await response.Content.ReadAsByteArrayAsync();

                await JSRuntime.InvokeVoidAsync("DownloadFileFromByteArray", "menu.pdf", "application/pdf", fileContent);
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception.Message);
            }
            finally
            {
                Exporting = false;
            }
        }
    }
}