@attribute [Authorize]

@using System.Data;
@using System.IO;
@using TwentyDishes.Client.Components.TdButton;
@using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent

@inherits UserBaseComponent

@inject HttpClient HttpClient
@inject IJSRuntime JSRuntime

<Banner @bind-MessageType="@messageType" @bind-Message="@message" />
 
@if (isPreviewActive)
{
    <div class="btn-loc" id="report-btn">
        <TdButton OnClick="PrintReport" loaderLeftPropertyCSS="-15px" Loading="PdfExportLoading"><span class="button-style">Save as PDF</span></TdButton>
        <button class="button-style" @onclick="Close">Back</button>
    </div>
    <div id="print-report" class="pb-5">
        <div class="meal-plan-print" id="meal-plan-print" style="@(reportChk[0] ? "display:block" : "display:none")">
            <div class="meal-plan-header">
                <div @onclick="Close" class="img-logo">
                    <img src="@MiscUtility.GetCloudflareImageUrl(logo.BaseUrl, logo.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" />
                </div>
                <div class="text-float"><h4>Meal Plan</h4></div>
                <hr>
            </div>

            <div><h6 style="font-family: Raleway;font-size: 16px;">This Week</h6></div>

            @for (int menuDayCount = 0; menuDayCount < 7; menuDayCount++)
            {
                int currentDayIndex = menuDayCount;
                GetCurrentRecipes(currentDayIndex);
                <div style="position:relative; top:-7px;left:2px;font-family:Raleway;font-size: 16px">
                    <span style="font-weight:700;">@selectedUserWeek?.StartDate.Month.@selectedUserWeek?.StartDate.AddDays(currentDayIndex).Day</span> @selectedUserWeek?.StartDate.AddDays(currentDayIndex).ToString("ddd")
                </div>
                foreach (List<Recipe> recipes in recipesByWeek)
                {
                    foreach (Recipe recipe in recipes)
                    {
                        <div class="recipe-holder pt-1">
                            <h6 class="print-diet-name" style=@($"font-size: {currentUser.PrintFontSize}px;")>@recipe.Diets.FirstOrDefault()</h6>
                            <h6 class="print-recipe-name" style=@($"font-size: {currentUser.PrintFontSize}px;")>@recipe.Name</h6>
                        </div>
                    }
                }
                <div class="pt-3"></div>
            }
        </div>
        <div class="print-prep-guide" id="print-prep-guide" style="@(reportChk[2] ? "display:block" : "display:none")">
            <div class="meal-plan-header">
                <div @onclick="Close" class="img-logo">
                    <img src="@MiscUtility.GetCloudflareImageUrl(logo.BaseUrl, logo.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" />
                </div>
                <div class="text-float-prep"><h4>SIMPLE FOOD PREP GUIDE</h4></div>
                <hr>
            </div>
            <div class="print-prep-text">
                <h6>Prepare your mise en place and get the the following things out.</h6>
                <h6>Label your storage bags and/or jars with these recipes.</h6>
            </div>
            <div class="prep-table">
                <div class="billing-hist-header">
                    <span class="header-item-250">Instructions</span>
                    <span class="header-item-200">Recipes & Ingredients</span>
                    <span class="header-item-80">Done</span>
                </div>
                @foreach (KeyValuePair<string, List<string>> step in assembledSteps)
                {
                    <div class="prep-table-data">
                        <div class="prep-instructions">
                            <span class="smt" style=@($"font-size: {currentUser.PrintFontSize}px;")>
                                @step.Key @*step description*@
                            </span>
                        </div>
                        <div class="prep-recipe">
                            <span style=@($"font-size: {currentUser.PrintFontSize}px;")>
                                @string.Join(", ", step.Value) @*list of recipes that share the same step, concatenated - comma separated*@
                            </span>
                        </div>
                        <div class="prep-done">
                            <input type="checkbox" disabled />
                        </div>
                    </div>
                }
            </div>
        </div>
        <div class="print-shopping" id="print-shopping" style="@(reportChk[1] ? "display:block" : "display:none")">
            <div class="meal-plan-header">
                <div class="img-logo">
                    <img src="@MiscUtility.GetCloudflareImageUrl(logo.BaseUrl, logo.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logo.PageTitle)" />
                </div>
                <div class="text-float-prep"><h4>SHOPPING LIST</h4></div>
                <hr>
            </div>
            @if (summedIngredients != null)
            {
                foreach (string category in shoppingCategories)
                {
                    List<SummedIngredient> categoryData = GetCategoryData(category);
                    if (categoryData == null || !categoryData.Any())
                    {
                        <div class="ps-1">
                            <h6 style="font-family: Raleway; margin:0;font-size: 16px !important;font-weight: 500;line-height:15px;">@category</h6>
                            <span style=@($"font-family: Raleway; font-size: {currentUser.PrintFontSize}px")>No ingredients for this category.</span>
                        </div>
                        <br />
                    }
                    else
                    {
                        <div class="ps-1">
                            <h6 style="font-family: Raleway; margin:0;font-size: 16px !important;font-weight: 500;line-height:15px;">@category</h6>
                            @foreach (SummedIngredient cd in categoryData)
                            {
                                <span style=@($"font-family: Raleway; font-size: {currentUser.PrintFontSize}px;line-height:5px;")>@(FormatShoppingListItem(cd))</span>
                                <br />
                            }
                        </div>
                        <br />
                    }
                }
            }
            else
            {
                <div class="load">
                    <Loader />
                </div>
            }
        </div>
        <div class="print-recipe-sec" id="print-recipe-sec" style="@(reportChk[3] ? "display:block" : "display:none")">
            <div class="meal-plan-header">
                <div class="img-logo">
                    <img src="@MiscUtility.GetCloudflareImageUrl(logo.BaseUrl, logo.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic)" alt="@(logo.PageTitle)" />
                </div>
                <div class="text-float"><h4>RECIPES</h4></div>
                <hr>
            </div>
            @foreach (Recipe seeRecipe in recipesToPrint)
            {
                <div class="recipe">
                    <div class="see-header pt-2">
                        <div>
                            <span class="see-recipe-name" style=@($"font-size: 20px;font-weight: 500;")>@seeRecipe.Name</span>
                        </div>
                        <div class="see-info" style=@($"font-size: {currentUser.PrintFontSize}px; padding-left:10px;")>
                            <span style="color: rgba(202, 47, 53, 1);" class="oi oi-clock"></span><span style="padding:0 5px; font-family: raleway;">Cook in @seeRecipe.CookTime mins</span>
                            <span style="color: rgba(202, 47, 53, 1);" class="oi oi-clock"></span><span style="padding:0 5px; font-family: raleway;">Prep in @seeRecipe.PrepTime mins</span>
                            <span style="color: rgba(202, 47, 53, 1);" class="oi oi-pie-chart"></span>
                            <span style="padding:0 5px; font-family: raleway;">Serves @seeRecipe.Servings @seeRecipe.ServingsUnit</span>
                        </div>
                    </div>
                    <div class="see-img" style="padding: 20px 0;">
                        <img class="rounded-1 modal-img" src="@MiscUtility.GetCloudflareImageUrl(logo.BaseUrl, seeRecipe.CloudflareImageId, MiscUtility.ImageUrlSuffixDesktopModal)"
                             alt="@seeRecipe.Name" />
                    </div>
                    <div>
                        <div style="font-family:Raleway;" class="recipe-txt-wrap">
                            <span class="see-recipe-ing">Ingredients</span>
                            @{
                                int ingrCount = 1;
                            }
                            @foreach (Recipe.RecipeIngredient ingr in seeRecipe.Ingredients)
                            {
                                <div class="ps-2 pt-2 ing-style" style=@($"font-size: {currentUser.PrintFontSize}px;")>
                                    <span class="group-pill">@ingrCount</span>
                                    @IngredientHelper.FormatIngredientString(ingr, 25)
                                </div>
                                ingrCount++;
                            }
                            <div class="see-recipe-ins">Instructions</div>
                            @{
                                int instrCount = 1;
                            }
                            @foreach (Instruction instr in seeRecipe.Instructions)
                            {
                                @if (instr != null && !string.IsNullOrWhiteSpace(instr.Text))
                                {
                                    <div class="ps-2 pt-2 ins-style">
                                        <span style="font-family:raleway;" class="group-pill fw-lighter">@instrCount</span>
                                        <span style=@($"font-size: {currentUser.PrintFontSize}px;")>@MiscUtility.ReplaceHtmlTags(instr.Text)</span>
                                    </div>
                                    instrCount++;
                                }
                            }
                        </div>
                        <br />
                        <div>
                            <section class="nutrition-see">
                                <header>
                                    <h1 class="nutrition-title">Recipe Nutrients</h1>
                                </header>
                                <ul class="nutrition-facts">
                                    <li class="nutrition-note"><span>Amount per serving</span> %Daily Values*</li>
                                    <li>
                                        <span><span class="nutrition-facts-label">Calories</span> @seeRecipe.Nutrition.Energy @seeRecipe.Nutrition.EnergyUnit</span>  @("Calories from Fat " + ((int)(seeRecipe.Nutrition.TotalFat * 9)).ToString())
                                    </li>
                                    <li class="nutrition-facts-section">
                                        <span><span class="nutrition-facts-label">Total Fat</span> @(seeRecipe.Nutrition.TotalFat + seeRecipe.Nutrition.TotalFatUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.TotalFat.Value, nameof(IngredientNutrition.TotalFat)) + "%")
                                        <ul>
                                            <li><span>Saturated Fat @(seeRecipe.Nutrition.SaturatedFat + seeRecipe.Nutrition.SaturatedFatUnit)</span> @(CalculateDailyValue(seeRecipe.Nutrition.SaturatedFat.Value, nameof(IngredientNutrition.SaturatedFat)) + "%")</li>
                                            <li>Polyunsaturated Fat @(seeRecipe.Nutrition.PolyUnsaturatedFat + seeRecipe.Nutrition.PolyUnsaturatedFatUnit)</li>
                                            <li>Monounsaturated Fat @(seeRecipe.Nutrition.MonoUnsaturatedFat + seeRecipe.Nutrition.MonoUnsaturatedFatUnit)</li>
                                        </ul>
                                    </li>
                                    <li>
                                        <span><span class="nutrition-facts-label">Sodium</span> @(seeRecipe.Nutrition.Sodium + seeRecipe.Nutrition.SodiumUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.Sodium.Value, nameof(IngredientNutrition.Sodium)) + "%")
                                    </li>
                                    <li>
                                        <span><span class="nutrition-facts-label">Potassium</span> @(seeRecipe.Nutrition.Potassium + seeRecipe.Nutrition.PotassiumUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.Potassium.Value, nameof(IngredientNutrition.Potassium)) + "%")
                                    </li>
                                    <li>
                                        <span><span class="nutrition-facts-label">Total Carbohydrate</span> @(seeRecipe.Nutrition.TotalCarbsByDifference + seeRecipe.Nutrition.TotalCarbsByDifferenceUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.TotalCarbsByDifference.Value, nameof(IngredientNutrition.TotalCarbsByDifference)) + "%")
                                        <ul>
                                            <li><span>Dietary Fiber @(seeRecipe.Nutrition.Fiber + seeRecipe.Nutrition.FiberUnit)</span> @(CalculateDailyValue(seeRecipe.Nutrition.Fiber.Value, nameof(IngredientNutrition.Fiber)) + "%")</li>
                                            <li>Sugars @(seeRecipe.Nutrition.TotalSugar + seeRecipe.Nutrition.TotalSugarUnit)</li>
                                        </ul>
                                    </li>
                                    <li>
                                        <span><span class="nutrition-facts-label">Protein</span> @(seeRecipe.Nutrition.Protein + seeRecipe.Nutrition.ProteinUnit)</span> @(CalculateDailyValue(seeRecipe.Nutrition.Protein.Value, nameof(IngredientNutrition.Protein)) + "%")
                                    </li>
                                </ul>
                                <ul class="nutrition-facts">
                                    <li>
                                        <span>Vitamin A @(seeRecipe.Nutrition.VitaminA + seeRecipe.Nutrition.VitaminAUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.VitaminA.Value, nameof(IngredientNutrition.VitaminA)) + "%")
                                    </li>
                                    <li>
                                        <span>Vitamin C @(seeRecipe.Nutrition.VitaminC + seeRecipe.Nutrition.VitaminCUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.VitaminC.Value, nameof(IngredientNutrition.VitaminC)) + "%")
                                    </li>
                                    <li>
                                        <span>Calcium @(seeRecipe.Nutrition.Calcium + seeRecipe.Nutrition.CalciumUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.Calcium.Value, nameof(IngredientNutrition.Calcium)) + "%")
                                    </li>
                                    <li>
                                        <span>Iron @(seeRecipe.Nutrition.Iron + seeRecipe.Nutrition.IronUnit)</span>
                                        @(CalculateDailyValue(seeRecipe.Nutrition.Iron.Value, nameof(IngredientNutrition.Iron)) + "%")
                                    </li>
                                </ul>
                                <footer class="nutrition-note">
                                    * Percent Daily Values (DV) are based on a 2,000 calorie diet
                                </footer>
                            </section>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}
//not preview
else
{
    <div class="print-main">
        <div class="print-header">
            <h2 style="font-family: Raleway;">Export</h2>
            <div class="print-header-buttons">
                <button class="button-style" @onclick="PrintPreview">PDF Preview</button>
                <button class="button-style" @onclick="BackToMain">Back to @ActivePage</button>
            </div>
            <div class="print-sm-btn">
                <button class="button-style" @onclick="PrintReportSm">Print</button>
                <button class="button-style" @onclick="BackToMain">Back to @ActivePage</button>
            </div>
        </div>
        <div class="print-section">
            @if (selectedWeekRecipes != null)
            {
                <div class="chk-print">
                    <div class="chk-wrap">
                        <input type="checkbox" checked="@reportChk[0]" @onchange="e => {PrintCheckedItem(e.Value,0);}" />
                        <label>Plan</label>
                    </div>
                    <div class="chk-wrap">
                        <input type="checkbox" checked="@reportChk[1]" @onchange="e => {PrintCheckedItem(e.Value,1);}" />
                        <label>Shopping</label>
                    </div>
                    <div class="chk-wrap">
                        <input type="checkbox" checked="@reportChk[2]" @onchange="e => {PrintCheckedItem(e.Value,2);}" />
                        <label>Simple Food Prep Guide</label>
                    </div>
                    <div class="chk-wrap">
                        <input type="checkbox" checked="@reportChk[3]" @onchange="e => {PrintCheckedItem(e.Value,3);}" />
                        <label>Recipes</label>
                    </div>
                </div>
                <div class="chk-select-all">
                    <label class="p-2">Select All</label>
                    <input type="checkbox" checked="@isChecked" @onchange="e => {SelectAll(e.Value);}" />
                </div>
                <div id="print-data">
                    <div class="print-recipe">
                        @foreach (Recipe recipe in selectedWeekRecipes)
                        {
                            <div class="rec-data">
                                <svg class="mt-2" width="27" height="25" viewBox="0 0 20 19"><use href="images/icons.svg#favorite-active" /></svg>
                                <h4 class="ps-2 d-inline-block">@recipe.Name</h4>
                            </div>
                            <div class="chk-select-print">
                                <label class="p-2">Print</label>
                                <input type="checkbox" checked="@allChecked" @onchange="e => {RecipeCheckAction(e.Value, recipe);}">
                            </div>
                            <hr class="print-bar" />
                        }
                    </div>
                </div>
            }
            else
            {
                <Loader />
            }
        </div>
    </div>
}
