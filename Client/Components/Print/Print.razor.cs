using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using TwentyDishes.Client.Components.BaseComponents.UserBaseComponent;
using TwentyDishes.Client.State.MenuGlobalState;
using TwentyDishes.Client.State.PublicGlobalState;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Client.Components.Print
{
    public partial class Print : UserBaseComponent
    {
        private bool allChecked = true;
        private Dictionary<string, List<string>> assembledSteps = new Dictionary<string, List<string>>();
        private Dictionary<string, bool> checkedItems = new Dictionary<string, bool>();
        private User currentUser;
        private List<DailyNutritionValues> dailyNutritionValues;
        Dictionary<string, string> ingrShoppingCategories;
        private bool isChecked;
        private bool isPreviewActive = false;
        Brand? logo => PublicGlobalStateValue.Brand;
        private MathHelper mathHelper = new MathHelper();
        private List<string> mealTypes = new List<string>() { "Breakfast", "Lunch", "Dinner", "Snacks" };
        string message;
        MiscUtility.AlertMessageType messageType;
        private List<List<Recipe>> recipesByWeek = new List<List<Recipe>>();
        private List<Recipe> recipesToPrint = new List<Recipe>();
        // 0 = plan, 1 = shopping list, 2 = prep guide, 3 = recipe details
        private bool[] reportChk = new bool[] { true, true, true, true };
        private UserWeek selectedUserWeek;
        private List<Recipe> selectedWeekRecipes;
        private List<ShoppingListItem> selectedWeekShoppingItems = new List<ShoppingListItem>();
        private List<string> shoppingCategories = new List<string>();
        private List<SummedIngredient> summedIngredients;
        private string userId;
        private bool PdfExportLoading = false;

        [Parameter]
        public string ActivePage { get; set; }
        [Parameter]
        public EventCallback<bool> OnBackToPage { get; set; }

        [CascadingParameter(Name = MenuGlobalStateFields.Value)]
        public MenuGlobalStateValue MenuGlobalStateValue {get; set;}

        [CascadingParameter(Name = PublicGlobalStateFields.Value)]
        public PublicGlobalStateValue PublicGlobalStateValue {get; set;}

        public async Task Initialize()
        {
            initializationStarted = true;

            shoppingCategories = await HttpClient.GetFromJsonAsync<List<string>>("/api/ShoppingCategories") ?? new List<string>();
            // selectedWeekRecipes = await SessionStorage.GetItemAsync<List<Recipe>>("SelectedWeekRecipes");
            selectedWeekRecipes = MenuGlobalStateValue.SelectedWeekRecipes;
            // selectedUserWeek = await SessionStorage.GetItemAsync<UserWeek>("SelectedUserWeek");
            selectedUserWeek = MenuGlobalStateValue.SelectedUserWeek;
            dailyNutritionValues = await HttpClient.GetFromJsonAsync<List<DailyNutritionValues>>("/api/DailyNutritionValues");
            userId = UserGlobalStateValue.UserId;
            currentUser = UserGlobalStateValue.FullUserData;
            await InvokeAsync(() => PrintRecipes());
            await FormatShoppingListData();
            await InvokeAsync(() => AssembleSteps());
            isChecked = true;

            initializationStarted = false;
            initialized = true;
        }

        public override async Task GlobalStateLoaded()
        {
            if (!initialized && !initializationStarted)
            {
                await Initialize();
            }
        }

        private void AssembleSteps()
        {
            Dictionary<string, List<(string, double)>> assembledStepsDictionary = new Dictionary<string, List<(string, double)>>();

            //Assemble dictionary
            foreach (Recipe recipe in selectedWeekRecipes)
            {
                foreach (Step step in recipe.Steps)
                {
                    string stepDesc = step.Description?.Replace("[", string.Empty).Replace("]", string.Empty) ?? "";

                    if (!assembledStepsDictionary.ContainsKey(stepDesc))
                    {
                        var recipeList = new List<(string, double)>() { (recipe.Name, stepDesc.Contains("Please Note:") ? 0 : Double.Parse(step.Priority)) };
                        assembledStepsDictionary.Add(stepDesc, recipeList);
                    }
                    else
                    {
                        if (!assembledStepsDictionary[stepDesc].Any(i => i.Item1 == recipe.Name))
                        {
                            assembledStepsDictionary[stepDesc].Add((recipe.Name, stepDesc.Contains("Please Note:") ? 0 : Double.Parse(step.Priority)));
                        }
                    }
                }
            }

            assembledSteps = assembledStepsDictionary.OrderBy(a => a.Value.First().Item2).Select(a => (a.Key, a.Value.Select(v => v.Item1).Order().ToList())).ToDictionary();
        }

        private async Task BackToMain()
        {
            await OnBackToPage.InvokeAsync();
        }

        private string CalculateDailyValue(double value, string nutrientKey)
        {
            decimal decCalc = Math.Round((decimal)(value / dailyNutritionValues.Where(x => x.Name == nutrientKey).Select(x => x.Value).FirstOrDefault()), 2) * 100;
            return ((int)decCalc).ToString();
        }

        private void Close()
        {
            isPreviewActive = false;
        }

        private async Task FormatShoppingListData()
        {
            await GetIngrShoppingCategories();
            SumTotalIngredients();
        }

        private string FormatShoppingListItem(SummedIngredient si)
        {
            if (si.IsCustom)
            {
                //Clone object to break object reference
                SummedIngredient siClone = new SummedIngredient()
                {
                    Amount = si.Amount,
                    AmountUnit = si.AmountUnit,
                    IngredientName = si.IngredientName.Replace("-" + IngredientHelper.AdditionalShoppingItems, string.Empty, StringComparison.InvariantCultureIgnoreCase),
                    IsCustom = si.IsCustom
                };
                return IngredientHelper.FormatIngredientString(siClone, 25);
            }
            else
            {
                return IngredientHelper.FormatIngredientString(si, 25);
            }
        }

        private List<SummedIngredient> GetCategoryData(string shoppingCategory)
        {
            List<string> ingrInCategory = ingrShoppingCategories.Where(x => x.Value == shoppingCategory).Select(x => x.Key.ToUpper()).ToList();
            return summedIngredients.Where(x => ingrInCategory.Contains(x.IngredientName.ToUpper())).Select(x => x).ToList();
        }

        private void GetCurrentRecipes(int currentDayIndex)
        {
            recipesByWeek = new List<List<Recipe>>();
            try
            {
                foreach (string mealType in mealTypes)
                {
                    string propertyName = $"{selectedUserWeek.StartDate.AddDays(currentDayIndex).ToString("dddd")}{mealType}";

                    if (selectedUserWeek.GetType().GetProperty(propertyName).GetValue(selectedUserWeek, null) != null && selectedWeekRecipes != null)
                    {
                        List<string> recipeIds = ((IEnumerable<string>)selectedUserWeek.GetType().GetProperty(propertyName).GetValue(selectedUserWeek, null)).Cast<string>().ToList();
                        List<Recipe> result = selectedWeekRecipes.Where(x => recipeIds.Contains(x.Id)).ToList() ?? new List<Recipe>();
                        recipesByWeek.Add(result);
                    }
                    else
                    {
                        recipesByWeek = new List<List<Recipe>>();
                    }
                }
            }
            catch (NullReferenceException)
            {
                recipesByWeek = new List<List<Recipe>>();
            }
        }

        private async Task GetIngrShoppingCategories()
        {
            HttpResponseMessage response = await HttpClient.PostAsJsonAsync<List<Recipe>>("/api/IngredientShoppingCategories", selectedWeekRecipes);

            if (response.IsSuccessStatusCode)
            {
                ingrShoppingCategories = await response.Content.ReadFromJsonAsync<Dictionary<string, string>>() ?? new Dictionary<string, string>();

                //Add custom additional items
                if (currentUser.ShoppingListItems != null && currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate) != null)
                {
                    foreach (ShoppingListItem sli in currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate))
                    {
                        if (!ingrShoppingCategories.ContainsKey(sli.IngredientName))
                        {
                            ingrShoppingCategories.Add(sli.IngredientName, IngredientHelper.AdditionalShoppingItems);
                        }
                    }
                }
            }
            else
            {
                message = await response.Content.ReadAsStringAsync();
                messageType = MiscUtility.AlertMessageType.Error;
            }
        }

        private void PrintCheckedItem(object checkedValue, int index)
        {
            if (!(bool)checkedValue)
            {
                reportChk[index] = false;
            }
            else
            {
                reportChk[index] = true;
            }
        }

        private void PrintPreview()
        {
            isPreviewActive = true;
        }

        private void PrintRecipes()
        {
            foreach (Recipe item in selectedWeekRecipes)
            {
                recipesToPrint.Add(item);
            }
        }

        private async Task PrintReport()
        {
            PdfExportLoading = true;

            try
            {
                HttpResponseMessage response = await HttpClient.PostAsJsonAsync("/api/CreateMenuPdf",
                    new CreateMenuPdfRequest
                    {
                        UserWeek = selectedUserWeek.ToExcluded(selectedUserWeek.ToRecipeIds().Where(id => !recipesToPrint.Any(r => r.Id == id)).ToList()),
                        AdditionalRecipeIds = null,
                        WeekStartDate = selectedUserWeek.StartDate,
                        IncludeMealPlan = reportChk[0],
                        IncludeShoppingList = reportChk[1],
                        IncludePrepGuide = reportChk[2],
                        IncludeRecipes = reportChk[3],
                        Format = Shared.Enums.MenuPdfFormat.FullMenu,
                        AdditionalIngredients = currentUser?.ShoppingListItems?.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate).ToList(),
                        BrandId = logo.Id
                    }
                );

                var fileContent = await response.Content.ReadAsByteArrayAsync();

                await JSRuntime.InvokeVoidAsync("DownloadFileFromByteArray", "menu.pdf", "application/pdf", fileContent);

                Close();
            }
            catch (Exception)
            {

            }
            finally
            {
                PdfExportLoading = false;
            }
        }

        private async Task PrintReportSm()
        {
            await InvokeAsync(() => PrintPreview());
            await Task.Delay(1000);
            await JSRuntime.InvokeVoidAsync("exportToPDFMobile", "print-report");
            Close();
        }

        private void RecipeCheckAction(object value, Recipe recipe)
        {
            if (!(bool)value)
            {
                Recipe recipeToBeRemoved = recipesToPrint.Where(x => x.Id == recipe.Id).FirstOrDefault();
                recipesToPrint.Remove(recipeToBeRemoved);
            }
            else
            {
                Recipe recipeToBeAdded = recipesToPrint.Where(x => x.Id == recipe.Id).FirstOrDefault();
                if (recipeToBeAdded == null)
                {
                    recipesToPrint.Add(recipe);
                }
            }
        }

        private void SelectAll(object checkedValue)
        {
            if (!(bool)checkedValue)
            {
                allChecked = false;
                recipesToPrint.Clear();

            }
            else
            {
                allChecked = true;
                PrintRecipes();
            }
        }

        private void SumTotalIngredients()
        {
            summedIngredients = IngredientHelper.SumTotalIngredients(selectedWeekRecipes);

            //Add custom additional items
            if (currentUser.ShoppingListItems != null && currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate) != null)
            {
                foreach (ShoppingListItem sli in currentUser.ShoppingListItems.Where(x => x.IsCustom && x.ScheduleDate == selectedUserWeek.StartDate))
                {
                    SummedIngredient si = new SummedIngredient()
                    {
                        Amount = MathHelper.FractionToDecimal(sli.Amount),
                        AmountUnit = sli.AmountUnit,
                        IngredientName = sli.IngredientName,
                        IsCustom = sli.IsCustom
                    };
                    summedIngredients.Add(si);
                }
            }
        }
    }
}