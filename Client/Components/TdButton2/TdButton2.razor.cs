using Microsoft.AspNetCore.Components;

namespace TwentyDishes.Client.Components.TdButton2
{
    public partial class TdButton2: ComponentBase
    {
        [Parameter]
        public EventCallback OnClick { get; set; }

        [Parameter]
        public RenderFragment? ChildContent { get; set; }

        [Parameter]
        public bool Loading {get; set;}

        [Parameter]
        public bool Disabled {get; set;}

        [Parameter]
        public TdButton2DisplayType DisplayType {get; set;} = TdButton2DisplayType.Primary;

        public string displayTypeClass => this.DisplayType == TdButton2DisplayType.Primary ? "display-type-primary" : this.DisplayType == TdButton2DisplayType.Minimal ? "display-type-minimal" : "";
    }

    public enum TdButton2DisplayType
    {
        Primary,
        Minimal
    }
}