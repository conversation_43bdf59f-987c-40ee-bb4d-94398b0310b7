/* DO NOT MODIFY THIS FILE. THIS IS A REUSABLE COMPONENT. */

.button {
  cursor: pointer;
  margin-right: 30px;
  font-size: 22px;
  background-color: #ca2f35;
  border: 1px solid #ca2f35;
  border-radius: 35px;
  color: white;
  font-family: Raleway;
  padding: 10px 30px;
  display: inline-flex;
  align-items: center;
  gap: 15px;
}

.display-type-minimal {
  background-color: transparent;
  border: transparent;
  color: #ca2f35;
}

.display-type-minimal:hover {
  background-color: #f5eaea;
}

@media only screen and (max-width: 1000px) {
  .button {
    font-size: 14px;
  }
}

.button:disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: default;
}

.button-loader {
    width: 18px;
    height: 18px;
    border: 3px solid var(--salesButtonOrange);
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
    position: relative;
}

@media only screen and (max-width: 1000px) {
  .button-loader {
    width: 12px;
    height: 12px;
    border: 2px solid #fff;
  }
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
