.button {
  /*cursor: pointer;
  margin-right: 30px;
  font-size: 22px;
  background: #ca2f35;
  border: 1px solid #ca2f35;
  border-radius: 35px;
  color: white;
  font-family: Raleway;
  padding: 0px 30px;
  display: inline-flex;
  align-items: center;
  height: 65px;*/
  border:none;
  border-radius: 7px;
  background:none;
  color: inherit;
}

@media only screen and (max-width: 1440px) and (min-width: 1024px) {
  .button {
    font-size: 14px;
    height: 53px;
  }
}

.button:disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: default;
}

.button-loader {
    width: 18px;
    height: 18px;
    border: 3px solid var(--salesButtonOrange);
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;
    position: relative;    
    top: 5px;
}

@media only screen and (max-width: 1440px) and (min-width: 1024px) {
  .button-loader {
    width: 12px;
    height: 12px;
    border: 2px solid #fff;
  }
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
