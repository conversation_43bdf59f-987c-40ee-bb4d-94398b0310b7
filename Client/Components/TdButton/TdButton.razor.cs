using Microsoft.AspNetCore.Components;

namespace TwentyDishes.Client.Components.TdButton
{
    public partial class TdButton: ComponentBase
    {
        [Parameter]
        public string? loaderLeftPropertyCSS { get; set; } = "0";
        [Parameter]
        public EventCallback OnClick { get; set; }

        [Parameter]
        public RenderFragment? ChildContent { get; set; }

        [Parameter]
        public bool Loading {get; set;}

        [Parameter]
        public bool Disabled {get; set;}
    }
}