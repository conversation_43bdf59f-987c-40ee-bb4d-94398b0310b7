namespace TwentyDishes.Client.Helpers
{
    public static class BreakingErrors
    {
        public static BreakingError PaddleCheckoutFailedToOpen = new BreakingError()
        {
            Title = "The Subscription Checkout Failed To Open",
            ErrorCode = 5000
        };

        public static BreakingError SubscriptionSelectionFailedToInitialize = new BreakingError()
        {
            Title = "The Subscription Selection Component Failed to Initialize",
            ErrorCode = 5001
        };

        public static BreakingError SubscriptionSelectionFailedToCreateSubscription = new BreakingError()
        {
            Title = "The Subscription Selection Component Failed to Create the Subscription",
            ErrorCode = 5002
        };

        public static BreakingError SubscriptionSelectionFailedToChangeSubscription = new BreakingError()
        {
            Title = "The Subscription Selection Component Failed to Change the Subscription",
            ErrorCode = 5003
        };

        public static BreakingError UserStateFailedToGetUserIdAndEmail = new BreakingError()
        {
            Title = "User State ID and Email",
            ErrorCode = 5004,
            Description = "The user's ID and email address could not be fetched when the app's user state tried to initialize."
        };

        public static BreakingError UserStateFailedToCheckUser = new BreakingError()
        {
            Title = "User State User Exists",
            ErrorCode = 5005,
            Description = "The user existence check could not be fetched when the app's user state tried to initialize."
        };

        public static BreakingError UserStateFailedToCreateUser = new BreakingError()
        {
            Title = "User State Could Not Create User",
            ErrorCode = 5006,
            Description = "The user could not be created by the app's back-end API when the app's user state tried to initialize."
        };

        public static BreakingError UserStateFailedToInitialize = new BreakingError()
        {
            Title = "User State Could Not Initialize",
            ErrorCode = 5007,
            Description = "The app's user state could not complete initialization."
        };

        public static BreakingError RootComponentFailedToGetBrand = new BreakingError()
        {
            Title = "Root Component Failed to Get Brand Details",
            ErrorCode = 5008
        };

        public static BreakingError RootComponentFailedToSetupPaddle = new BreakingError()
        {
            Title = "Root Component Failed to Set Up Paddle",
            ErrorCode = 5009
        };
    }

    public class BreakingError
    {
        public string Title { get; set; } = default!;

        public string Description { get; set; } = default!;

        public int ErrorCode { get; set; } = default!;
    }
}