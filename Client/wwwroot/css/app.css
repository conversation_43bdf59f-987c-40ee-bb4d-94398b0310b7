@import url('open-iconic/font/css/open-iconic-bootstrap.min.css');
@import url('https://fonts.googleapis.com/css2?family=Oswald&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lato&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Raleway&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@400;500&display=swap');

:root {
    --activeGray: #2D3748;
    --black: #000;
    --lightAccentYellow: #E5DFC3;
    --salesButtonOrange: #DB792F;
    --seranataBlue: #AAC2CE;
    --vibrantRed: #CA2F35;    
}

html, body {
    font-family: '<PERSON>', sans-serif;
}

body, h1, h2, h3, h4, h5, h6 {
    font-family: '<PERSON>', sans-serif;    
}

    h1:focus {
        outline: none;
    }

a, .btn-link {
    color: var(--vibrantRed);
}

p {
    font-family: 'Lato', sans-serif;
    font-size: 18px;
}

input[type=checkbox] {
    transform: scale(1.5);
    margin-right: 5px;
    accent-color: var(--salesButtonOrange);
}

.btn-primary {
    color: #fff;
    background-color: var(--vibrantRed);
    border-color: var(--vibrantRed);
    border-style: none;
    font-family: 'Lato', sans-serif;
    font-size: 18px;
    font-weight: bold;
}

    .btn-primary:hover {
        background-color: var(--activeGray);
        font-family: 'Montserrat', sans-serif;
    }

    .btn-primary:focus {
        background-color: var(--activeGray);
        font-family: 'Montserrat', sans-serif;
    }

.btn-secondary {
    color: #fff;
    background-color: var(--salesButtonOrange);
    border-color: var(--salesButtonOrange);
    border-style: none;
    font-family: 'Lato', sans-serif;
    font-size: 18px;
    font-weight: bold;
}

    .btn-secondary:hover {
        background-color: var(--activeGray);
        font-family: 'Montserrat', sans-serif;
    }

    .btn-secondary:focus {
        background-color: var(--activeGray);
        font-family: 'Montserrat', sans-serif;
    }

.content {
    padding-top: 1.1rem;
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid red;
}

.validation-message {
    color: red;
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

.checkbox-background-img {
    background-image: url("https://www.20dishes.com/wp-content/themes/20dishes/css/minimal/green.png");
}

.text-background {
    background-color: var(--vibrantRed); /*::selection*/
    color: #fff;
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
    /*box-shadow: inset 0 0 5px grey;
        border-radius: 10px;*/
    background: #D7D7D7;
    /*box-shadow: inset 4px 4px 13px rgba(0, 0, 0, 0.14);*/
    border-radius: 10px;
    transform: rotate(90deg);
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: var(--salesButtonOrange);
    border-radius: 10px;
    transform: rotate(90deg);
}

    /* Handle on hover */
    ::-webkit-scrollbar-thumb:hover {
        background: var(--vibrantRed);
    }

