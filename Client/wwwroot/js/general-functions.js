function calcSetupProgress(identifier) {
  const progress = document.getElementById("progress");
  const circles = document.querySelectorAll(".circle");
  const actives = document.querySelectorAll(".active");
  let currentActive = actives.length;

  if (identifier === "next") {
    currentActive++;
    if (currentActive > circles.length) currentActive = circles.length;
    update();
  } else {
    currentActive--;
    if (currentActive < 1) currentActive = 1;
    update();
  }
  function update() {
    circles.forEach((circle, index) => {
      if (index < currentActive) circle.classList.add("active");
      else {
        circle.classList.remove("active");
      }
    });
    const newActive = document.querySelectorAll(".active");
    progress.style.width =
      ((newActive.length - 1) / (circles.length - 1)) * 100 + "%";
  }
}

function exportToPDF(param) {
  //var elements = Array.from(document.getElementById(param).children);;
  var element = document.getElementById(param);
  replaceCss();
  useCORS: true;
  var options = {
    margin: [0.25, 0, 0.25, 0],
    filename: "20Dishes_Print_Report.pdf",
    image: { type: "jpeg", quality: 0.5 },
    html2canvas: { scale: 2, scrollY: 0, useCORS: true, width: 790 },
    jsPDF: { unit: "in", format: "a4", orientation: "portrait" },
    pagebreak: { mode: ["avoid-all", "css", "legacy"] },
  };
  html2pdf().set(options).from(element).save();
  //let worker = html2pdf().set(options).from(elements[0]);

  //if (elements.length > 1) {
  //    worker = worker.toPdf();
  //    elements.slice(1).forEach((el, index) => {
  //        worker = worker.get('pdf')
  //            .then(pdf => {
  //                pdf.addPage()
  //            })
  //            .from(el)
  //            .toContainer()
  //            .toCanvas()
  //            .toPdf();
  //    });
  //}
  //worker = worker.save();
}

function exportToPDFMobile(param) {
  var element = document.getElementById(param);
  useCORS: true;
  var options = {
    margin: 0,
    filename: "20Dishes_Print_Report.pdf",
    image: { type: "jpeg", quality: 0.5 },
    html2canvas: {
      scale: 2,
      useCORS: true,
      scrollX: 0,
      scrollY: 0,
      width: 790,
    },
    jsPDF: { unit: "in", format: "a4", orientation: "p" },
    pagebreak: { mode: ["avoid-all", "css", "legacy"] },
  };
  html2pdf().set(options).from(element).save();
}

function loadAdThrive(siteId) {
  if (adThriveIsLoaded()) return;

  try {
    const script = document.createElement("script");
    script.async = true;
    script.referrerpolicy = "no-referrer-when-downgrade";
    script.src =
      "https://ads.adthrive.com/sites/" +
      siteId +
      "/ads.min.js?referrer=" +
      encodeURIComponent(window.location.href);
    script.id = "adthrive-script";

    const bodyElement = document.getElementsByTagName("body")[0];

    bodyElement.appendChild(script)
  } catch (error) {
  }
}

function unloadAdThrive() {
  // const scriptElement = document.getElementById("adthrive-script");
  // scriptElement?.remove();

  // const footerElements = document.getElementsByClassName("adthrive-footer");
  // if (footerElements) {
  //   for (let i = 0; i < footerElements.length; i++) {
  //     footerElements.item(i)?.remove();
  //   }
  // }

  // const footer2Elements = document.getElementsByClassName(
  //   "adthrive-footer-message"
  // );
  // if (footer2Elements) {
  //   for (let i = 0; i < footer2Elements.length; i++) {
  //     footer2Elements.item(i)?.remove();
  //   }
  // }
}

function enableAdThrive() {
  const element = document.getElementsByTagName("body")

  if (!element?.length) return

  element.item(0).classList.remove("adthrive-disable-all")

  const footerElement = document.getElementsByClassName("adthrive-footer-message")

  if (!footerElement?.length) return

  footerElement.item(0).style["display"] = "block"
}

function disableAdThrive() {
  const element = document.getElementsByTagName("body")

  if (!element?.length) return

  element.item(0).classList.add("adthrive-disable-all")

  const footerElement = document.getElementsByClassName("adthrive-footer-message")

  if (!footerElement?.length) return

  footerElement.item(0).style["display"] = "none"
}

function adThriveIsLoaded() {
  const element = document.getElementById("adthrive-script");

  return !!element;
}

function loadGoogleAnalytics(sourceUrl, measurementId) {
  //Check to see if element exists before creating
  var gTagEl = document.getElementById("gtag-" + measurementId);
  if (!gTagEl) {
    //Get source from Google Tag Manager
    var newTag = document.createElement("script");
    newTag.setAttribute("async", "");
    newTag.setAttribute("id", "gtag-" + measurementId);
    newTag.src = sourceUrl;
    newTag.type = "text/javascript";
    document.body.appendChild(newTag);
  }

  //Invoke gtag
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    dataLayer.push(arguments);
  }
  gtag("js", new Date());
  gtag("config", measurementId);
}

function replaceCss() {
  let meal_plan = document.getElementById("meal-plan-print");
  let print_shopping = document.getElementById("print-shopping");
  let print_prep = document.getElementById("print-prep-guide");
  let print_recipe = document.getElementById("print-recipe-sec");
  let btn = document.getElementById("report-btn");

  meal_plan.style.paddingLeft = "20px";
  meal_plan.style.paddingRight = "0";
  meal_plan.style.paddingTop = "0";
  meal_plan.style.paddingBottom = "0";
  meal_plan.style.top = "0";

  print_shopping.style.paddingLeft = "20px";
  print_shopping.style.paddingTop = "0px";
  print_shopping.style.paddingRight = "0";
  print_shopping.style.paddingBottom = "0";
  print_shopping.style.top = "0";

  print_prep.style.paddingLeft = "20px";
  print_prep.style.paddingTop = "0";
  print_prep.style.paddingRight = "0";
  print_prep.style.paddingBottom = "0";
  print_prep.style.top = "0";

  print_recipe.style.paddingLeft = "20px";
  print_recipe.style.paddingTop = "0";
  print_recipe.style.paddingRight = "0";
  print_recipe.style.paddingBottom = "0";
  print_recipe.style.top = "0";

  if (btn != null) {
    btn.style.display = "none";
  }
}

function toggleDropdown(dropdown, content) {
  var dropdown = document.getElementById(dropdown);
  var contents = document.getElementsByClassName(content);
  if (dropdown.classList.contains("dropdown-menu-active")) {
    dropdown.classList.remove("dropdown-menu-active");
    for (var i = 0; i < contents.length; i++) {
      contents[i].style.display = "none";
    }
  } else {
    dropdown.classList.add("dropdown-menu-active");
    for (var i = 0; i < contents.length; i++) {
      contents[i].style.display = "block";
    }
  }
}

/**
 * Browsers have odd rules for downloading a file using file content from an API request. This function takes the file content as a byte array and mimics the old file link behavior.
 * @param {any} filename
 * @param {any} contentType
 * @param {any} content
 */
function DownloadFileFromByteArray(filename, contentType, content) {
  // https://www.meziantou.net/generating-and-downloading-a-file-in-a-blazor-webassembly-application.htm

  // Create the URL
  const file = new File([content], filename, { type: contentType });
  const exportUrl = URL.createObjectURL(file);

  // Create the <a> element and click on it
  const a = document.createElement("a");
  document.body.appendChild(a);
  a.href = exportUrl;
  a.download = filename;
  a.target = "_self";
  a.click();

  // We don't need to keep the object URL, let's release the memory
  // On older versions of Safari, it seems you need to comment this line...
  URL.revokeObjectURL(exportUrl);
}

function ScrollToTop() {
  setTimeout(() => window.scrollTo(0, 0), 200);
}

function SetDotNetObjectRef(path, objRef) {
  window[path] = objRef;
}

function InitializePaddle(environment, token) {
  if (environment != "production") Paddle.Environment.set(environment);

  Paddle.Setup({
    token: token,
    eventCallback: function (event) {
      if (window.SubscriptionSelectionObjRef) {
        if (event?.name) {
          window.SubscriptionSelectionObjRef.invokeMethodAsync(
            "HandleCheckoutEvent",
            event.name
          );
        }
      }
    },
  });
}

function ShowBreakingError(
  erroCode,
  errorTitle,
  errorDescription,
  errorDetailedMessage,
  additionalInfoMessage
) {
  const appDivElement = document.getElementById("app");

  const errorElement = document.createElement("div");
  errorElement.style.height = "100vh";
  errorElement.style.width = "100vw";
  errorElement.style.backgroundColor = "rgba(256, 256, 256, 0.8)";
  errorElement.style.position = "absolute";
  errorElement.style.top = "0";
  errorElement.style.zIndex = "1005";
  errorElement.style.padding = "20px";
  errorElement.style.display = "flex";
  errorElement.style.justifyContent = "center";
  errorElement.style.alignItems = "flex-start";

  errorElement.innerHTML = `
  <div style="max-width: 400px; color:#842029; background-color:#f8d7da; border-radius: 20px; padding: 20px; display: flex; flex-direction: column; gap: 20px;">
    <div style="font-size: 30px;">The app has encountered an error and cannot recover. Please refresh the page.</div>
    <div>Error code ${erroCode}: ${errorTitle}</div>
    ${errorDescription ? `<div>${errorDescription}</div>` : ""}
    ${errorDetailedMessage ? `<div>${errorDetailedMessage}</div>` : ""}
    ${additionalInfoMessage ? `<div>${additionalInfoMessage}</div>` : ""}
  </div>
  `;

  appDivElement.parentNode.insertBefore(errorElement, appDivElement);
}

function setSessionItem(key, value) {
  sessionStorage.setItem(key, value)
}

function getSessionItem(key) {
  return sessionStorage.getItem(key)
}

function removeSessionItem(key) {
  sessionStorage.removeItem(key)
}
