function calcSetupProgress(n){function r(){i.forEach((n,i)=>{i<t?n.classList.add("active"):n.classList.remove("active")});const n=document.querySelectorAll(".active");u.style.width=(n.length-1)/(i.length-1)*100+"%"}const u=document.getElementById("progress"),i=document.querySelectorAll(".circle"),f=document.querySelectorAll(".active");let t=f.length;n==="next"?(t++,t>i.length&&(t=i.length),r()):(t--,t<1&&(t=1),r())}function exportToPDF(n){var i=document.getElementById(n),t;replaceCss();t={margin:[.25,0,.25,0],filename:"20Dishes_Print_Report.pdf",image:{type:"jpeg",quality:.5},html2canvas:{scale:2,scrollY:0,useCORS:!0,width:790},jsPDF:{unit:"in",format:"a4",orientation:"portrait"},pagebreak:{mode:["avoid-all","css","legacy"]}};html2pdf().set(t).from(i).save()}function exportToPDFMobile(n){var i=document.getElementById(n),t;t={margin:0,filename:"20Dishes_Print_Report.pdf",image:{type:"jpeg",quality:.5},html2canvas:{scale:2,useCORS:!0,scrollX:0,scrollY:0,width:790},jsPDF:{unit:"in",format:"a4",orientation:"p"},pagebreak:{mode:["avoid-all","css","legacy"]}};html2pdf().set(t).from(i).save()}function loadAdThrive(n){if(!adThriveIsLoaded())try{const t=document.createElement("script");t.async=!0;t.referrerpolicy="no-referrer-when-downgrade";t.src="https://ads.adthrive.com/sites/"+n+"/ads.min.js?referrer="+encodeURIComponent(window.location.href);t.id="adthrive-script";const i=document.getElementsByTagName("body")[0];i.appendChild(t)}catch(t){}}function unloadAdThrive(){}function enableAdThrive(){const n=document.getElementsByTagName("body");if(n?.length){n.item(0).classList.remove("adthrive-disable-all");const t=document.getElementsByClassName("adthrive-footer-message");t?.length&&(t.item(0).style.display="block")}}function disableAdThrive(){const n=document.getElementsByTagName("body");if(n?.length){n.item(0).classList.add("adthrive-disable-all");const t=document.getElementsByClassName("adthrive-footer-message");t?.length&&(t.item(0).style.display="none")}}function adThriveIsLoaded(){const n=document.getElementById("adthrive-script");return!!n}function loadGoogleAnalytics(n,t){function r(){dataLayer.push(arguments)}var u=document.getElementById("gtag-"+t),i;u||(i=document.createElement("script"),i.setAttribute("async",""),i.setAttribute("id","gtag-"+t),i.src=n,i.type="text/javascript",document.body.appendChild(i));window.dataLayer=window.dataLayer||[];r("js",new Date);r("config",t)}function replaceCss(){let n=document.getElementById("meal-plan-print"),t=document.getElementById("print-shopping"),i=document.getElementById("print-prep-guide"),r=document.getElementById("print-recipe-sec"),u=document.getElementById("report-btn");n.style.paddingLeft="20px";n.style.paddingRight="0";n.style.paddingTop="0";n.style.paddingBottom="0";n.style.top="0";t.style.paddingLeft="20px";t.style.paddingTop="0px";t.style.paddingRight="0";t.style.paddingBottom="0";t.style.top="0";i.style.paddingLeft="20px";i.style.paddingTop="0";i.style.paddingRight="0";i.style.paddingBottom="0";i.style.top="0";r.style.paddingLeft="20px";r.style.paddingTop="0";r.style.paddingRight="0";r.style.paddingBottom="0";r.style.top="0";u!=null&&(u.style.display="none")}function toggleDropdown(n,t){var n=document.getElementById(n),r=document.getElementsByClassName(t),i;if(n.classList.contains("dropdown-menu-active"))for(n.classList.remove("dropdown-menu-active"),i=0;i<r.length;i++)r[i].style.display="none";else for(n.classList.add("dropdown-menu-active"),i=0;i<r.length;i++)r[i].style.display="block"}function DownloadFileFromByteArray(n,t,i){const f=new File([i],n,{type:t}),u=URL.createObjectURL(f),r=document.createElement("a");document.body.appendChild(r);r.href=u;r.download=n;r.target="_self";r.click();URL.revokeObjectURL(u)}function ScrollToTop(){setTimeout(()=>window.scrollTo(0,0),200)}function SetDotNetObjectRef(n,t){window[n]=t}function InitializePaddle(n,t){n!="production"&&Paddle.Environment.set(n);Paddle.Setup({token:t,eventCallback:function(n){window.SubscriptionSelectionObjRef&&n?.name&&window.SubscriptionSelectionObjRef.invokeMethodAsync("HandleCheckoutEvent",n.name)}})}function ShowBreakingError(n,t,i,r,u){const e=document.getElementById("app"),f=document.createElement("div");f.style.height="100vh";f.style.width="100vw";f.style.backgroundColor="rgba(256, 256, 256, 0.8)";f.style.position="absolute";f.style.top="0";f.style.zIndex="1005";f.style.padding="20px";f.style.display="flex";f.style.justifyContent="center";f.style.alignItems="flex-start";f.innerHTML=`
  <div style="max-width: 400px; color:#842029; background-color:#f8d7da; border-radius: 20px; padding: 20px; display: flex; flex-direction: column; gap: 20px;">
    <div style="font-size: 30px;">The app has encountered an error and cannot recover. Please refresh the page.</div>
    <div>Error code ${n}: ${t}</div>
    ${i?`<div>${i}</div>`:""}
    ${r?`<div>${r}</div>`:""}
    ${u?`<div>${u}</div>`:""}
  </div>
  `;e.parentNode.insertBefore(f,e)}function setSessionItem(n,t){sessionStorage.setItem(n,t)}function getSessionItem(n){return sessionStorage.getItem(n)}function removeSessionItem(n){sessionStorage.removeItem(n)}