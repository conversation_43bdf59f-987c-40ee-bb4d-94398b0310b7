<svg id="e0HZKlkRtx91" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1920 1920" shape-rendering="geometricPrecision" text-rendering="geometricPrecision"><g id="e0HZKlkRtx92"><path d="M1037.78,1248.35c8.15-15.25,15.64-29.58,23.54-43.67.89-1.58,4.07-2.42,6.27-2.63c23.07-2.18,46.16-4.17,69.24-6.2.07-.62.13-1.23.2-1.85-21.12-3.5-42.25-6.99-64.08-10.61c7.85-15.46,15.36-30.57,23.28-45.45.97-1.83,4.1-3.23,6.4-3.52c16.84-2.11,33.73-3.86,50.59-5.81c2.88-.33,5.72-1.05,8.44-2.63-18.5-1.84-37-3.68-56.59-5.63c9.18-19.67,18.16-38.47,26.76-57.45c8.64-19.05,16.9-38.28,24.57-57.85-53.7,70.89-100.66,146.22-149.78,221.14c0-2.74.12-5.48-.02-8.21-1.05-20.2-2.06-40.41-3.26-60.61-1.58-26.56,11.3-42.2,37.62-45.43c2.97-.37,5.95-.74,8.91-1.21c16.46-2.63,25.79-17.19,20.93-33.19-1.73-5.71-4.54-11.08-6.68-16.67-4.9-12.8-4.44-25.2,4.58-36.01c9.08-10.87,21.42-15.16,35-11.98c15.7,3.67,25.87-2.19,33.91-15.03c5.42-8.66,11.19-17.2,17.71-25.03c8.89-10.66,20.84-14.42,34.39-11.38c12.62,2.84,21.2,11.01,23.09,23.68c1.05,6.98-.5,14.93-2.9,21.72-6.91,19.47-4.61,28.99,12.17,41.39c12.71,9.4,22.53,20.48,23.54,37.23c1.32,21.84-10.27,39.05-31.44,46.38-4.95,1.71-10.09,2.83-15.06,4.5-19.01,6.38-26.95,21.12-21.66,40.35c3.09,11.22,5.07,22.3,1.16,33.84-7.5,22.17-26.27,36.87-51.64,37.61-29.15.86-58.39.21-89.19.21Z" fill="#db792f"/><path d="M1036.86,1248.59c8.15-15.25,15.64-29.58,23.54-43.67.89-1.58,4.07-2.42,6.27-2.63c23.07-2.18,46.16-4.17,69.24-6.2.07-.62.13-1.23.2-1.85-21.13-3.5-42.25-6.99-64.08-10.61c7.85-15.46,15.36-30.57,23.28-45.45.97-1.83,4.1-3.23,6.4-3.52c16.84-2.11,33.73-3.86,50.59-5.81c2.88-.33,5.72-1.05,8.44-2.63-18.5-1.84-37-3.68-56.59-5.63c9.18-19.67,18.16-38.47,26.76-57.45c8.64-19.05,16.9-38.28,24.57-57.85-53.7,70.89-100.66,146.22-149.78,221.14" fill="#e5dfc3"/></g><g id="e0HZKlkRtx95" transform="matrix(.872311 0.488952-.488952 0.872311 658.364649-291.71291)"><path d="M797.2,981.82c107.1,33.73,191.89,132.78,181.66,263.9-8.28,0-16.7.47-25-.31-2.22-.21-4.65-3.95-6.02-6.57-14.38-27.68-27.45-56.11-43.1-83.05-16.39-28.21-35.2-55-52.95-82.42-.79.45-1.58.89-2.37,1.34c26.12,57.04,52.24,114.09,78.36,171.13-.39.55-.79,1.11-1.18,1.66-5.03-2.68-10.21-5.1-15.06-8.07-48.24-29.51-80-72.26-99.16-124.79-15.59-42.75-19.15-87.01-15.18-132.82Z" fill="#db792f"/><path d="M952.64,1245.41c-2.2-.21-4.61-3.95-5.97-6.57-14.26-27.68-27.23-56.11-42.75-83.05-16.25-28.21-34.91-55-52.52-82.42-.79.45-1.57.89-2.36,1.34c25.91,57.04,51.81,114.09,77.72,171.13-.39.55-.78,1.11-1.17,1.66" fill="#e5dfc3"/></g><g id="e0HZKlkRtx98"><path d="M650.58,800.77c5.7-1.64,11.4-3.28,17.18-4.94-3.57-6.63-6.88-12.79-10.2-18.95.66-.77,1.32-1.54,1.98-2.31c6.61,3.68,13.22,7.35,20.56,11.43.84-6.52,1.59-12.41,2.35-18.3.89-.22,1.78-.43,2.66-.65c3.34,8.45,6.69,16.89,10.23,25.84c2.47-.38,5.13-.88,7.82-1.18c26.73-2.98,48.66,7.9,61.23,33.17c5.8,11.65,8.52,25.15,10.86,38.13c2.22,12.3,2.27,25.02,2.91,37.57.12,2.44-.74,6.11-2.42,7.14-2.01,1.23-5.53.79-8.15.12-9.88-2.55-19.76-5.2-29.46-8.37-14.55-4.75-29.95-8.12-43.19-15.37-25.65-14.04-33.5-38.22-23.6-65.59c1.71-4.73,1.06-6.95-3.79-8.53-5.75-1.87-11.28-4.41-16.9-6.66-.03-.83-.05-1.69-.07-2.55Zm92.25,40.98c6.68-7.65,3.13-24.04-6.86-31.87-15.19-11.9-39.54-9.23-50.37,5.52-12.92,17.59-8.36,40.65,11.05,53.82-1.73-3.25-3.35-6.03-4.71-8.93-1.38-2.94-2.49-6-4.04-9.78c10.56,9.68,20.12,18.44,31.65,29c1.27-5.71,3.84-10.26,2.51-12.48-5.59-9.32-12.24-18-18.7-26.78-3.17-4.3-6.97-8.14-10.06-12.49-.98-1.39-.6-3.74-1.04-7.27c19.35,14.12,30.92,34.1,50.38,44.91.65-.63,1.3-1.26,1.94-1.88-14.53-16.21-29.06-32.42-43.58-48.63.72-.82,1.44-1.64,2.16-2.45c15.4,14.94,34.51,25.16,48.74,43.88c2.7-10.73-7.87-9.14-9.07-14.57Z" fill="#db792f"/><path d="M742.84,841.69c6.68-7.65,3.13-24.04-6.86-31.87-15.19-11.9-39.54-9.23-50.37,5.52-12.92,17.59-8.36,40.65,11.05,53.82-1.73-3.25-3.35-6.03-4.71-8.93-1.38-2.94-2.49-6-4.04-9.78c10.56,9.68,20.12,18.44,31.65,29c1.27-5.71,3.84-10.26,2.51-12.48-5.59-9.32-12.24-18-18.7-26.78-3.17-4.3-6.97-8.14-10.06-12.49-.98-1.39-.6-3.74-1.04-7.27c19.35,14.12,30.92,34.1,50.38,44.91.65-.63,1.3-1.26,1.94-1.88-14.53-16.21-29.06-32.42-43.58-48.63.72-.82,1.44-1.64,2.16-2.45c15.4,14.94,34.51,25.16,48.74,43.88c2.7-10.73-7.87-9.13-9.07-14.57Z" fill="#e5dfc3"/></g><g id="e0HZKlkRtx911" transform="matrix(.657246-.753676 0.753676 0.657246-368.716969 604.429844)"><path d="M400.77,628.08c-6.44-7.5-13.38-13.78-18.03-21.44-6.06-9.98-6.33-21.16.13-31.47c5.51-8.79,14-12.63,24.27-12.06c11,.61,19.1,6.77,24.02,15.9c6.53,12.12,11.68,24.98,17.37,37.4.67-1.56,1.1-4.32,2.7-5.9c2.89-2.85,6.65-7.13,9.75-6.9c4.07.31,9.74,3.57,11.3,7.08c2.22,4.99,1.78,11.37,1.66,17.14-.29,14.02-1.17,28.03-1.56,42.05-.13,4.77,2.26,7.03,7.55,7.2c10.62.35,17.25,7.44,22.35,15.72c13.73,22.28,27.64,44.48,40.51,67.26c16.29,28.81,31.64,58.15,47.37,87.28.68,1.25,1.11,2.63,2.01,4.8-21.04-8.49-38.33-20.9-53.38-35.9-23.42-23.34-45.86-47.68-68.5-71.79-4.01-4.27-7.11-9.41-10.44-14.28-8.64-12.63-14.65-25.69-5.21-40.75-15.79-13.56-32.67-20.62-53.5-16.71-5.94,1.11-12.95.25-18.65-1.89-10.89-4.1-15.74-13.93-13.94-24.62c1.65-9.79,10.72-17.3,22.07-18.08c3.52-.24,7.04-.04,10.15-.04Zm25.88-24.63c-1.63-6.5-3.03-12.06-4.98-19.82-3.34.87-7.77,1.92-12.14,3.19-8.49,2.47-10.19,6.25-4.83,13.55c7.4,10.09,15.6,19.6,23.55,29.28c1.44,1.75,3.31,3.14,4.98,4.69-5.25-11.05-16.53-18.85-14.34-33.92c3.43,1.34,5.46,2.13,7.76,3.03Zm-12.9-30.13c-13.08-7.54-26.89,4.5-26.89,16.21c0,11.86,10.15,26.66,19.35,29.97-2.53-7.17-6.91-15.63-8.3-24.55-1.72-10.98,11.69-13.23,15.84-21.63Zm-36.49,80.12c12.76-8.01,26.3-7.72,41.74-1.83-4.88-4.64-7.57-7.2-11.73-11.17c4.65-.27,6.45-.38,10.28-.6-13.21-5.74-24.5-7.55-35.51-.72-4.5,2.79-7.06,7.31-4.78,14.32Zm108.38,57.13c4.94-6.48,4.34-13.42.33-19.83-1.7-2.73-5.1-5.44-8.12-5.99-2.61-.47-7.04,1.4-8.33,3.6-1.23,2.1-.3,6.5,1.2,8.96c3.7,6.07,8.22,11.66,12.56,17.33c1,1.31,2.49,2.43,3.99,3.07.45.19,2.6-2.1,2.47-2.31-.98-1.55-2.33-2.85-4.1-4.83Zm24.12,40.36c4.64,5.78,8.28,11.05,12.75,15.5c1.66,1.65,5.25,1.34,7.96,1.93-.23-2.22.23-4.85-.81-6.59-3.47-5.85-7.46-11.38-11.6-17.55-3.1,2.51-5.41,4.38-8.3,6.71ZM453.53,638.05c2.8-6.32,6.07-13.27,8.92-20.39.74-1.86.16-4.26.18-6.41-1.99,1.03-4.88,1.54-5.81,3.17-4.47,7.79-6.01,16.13-3.29,23.63Z" fill="#db792f"/><path d="M426.65,603.98c-1.63-6.5-3.03-12.06-4.98-19.82-3.34.87-7.77,1.92-12.14,3.19-8.49,2.47-10.19,6.25-4.83,13.55c7.4,10.09,15.6,19.6,23.55,29.28c1.44,1.75,3.31,3.14,4.98,4.69-5.25-11.05-16.53-18.85-14.34-33.92c3.43,1.33,5.46,2.13,7.76,3.03Zm-12.9-30.13c-13.08-7.54-26.89,4.5-26.89,16.21c0,11.86,10.15,26.66,19.35,29.97-2.53-7.17-6.91-15.63-8.3-24.55-1.72-10.99,11.69-13.24,15.84-21.63Zm-36.49,80.11c12.76-8.01,26.3-7.72,41.74-1.83-4.88-4.64-7.57-7.2-11.73-11.17c4.65-.27,6.45-.38,10.28-.6-13.21-5.74-24.5-7.55-35.51-.72-4.5,2.79-7.06,7.31-4.78,14.32Zm108.38,57.13c4.94-6.48,4.34-13.42.33-19.83-1.7-2.73-5.1-5.44-8.12-5.99-2.61-.47-7.04,1.4-8.33,3.6-1.23,2.1-.3,6.5,1.2,8.96c3.7,6.07,8.22,11.66,12.56,17.33c1,1.31,2.49,2.43,3.99,3.07.45.19,2.6-2.1,2.47-2.31-.98-1.54-2.33-2.85-4.1-4.83Zm24.12,40.37c4.64,5.78,8.28,11.05,12.75,15.5c1.66,1.65,5.25,1.34,7.96,1.93-.23-2.22.23-4.85-.81-6.59-3.47-5.85-7.46-11.38-11.6-17.55-3.1,2.5-5.41,4.37-8.3,6.71ZM453.53,638.57c2.8-6.32,6.07-13.27,8.92-20.39.74-1.86.16-4.26.18-6.41-1.99,1.03-4.88,1.54-5.81,3.17-4.47,7.8-6.01,16.13-3.29,23.63Z" fill="#e5dfc3"/></g><g id="e0HZKlkRtx914"><path d="M724.67,630.37c-17.07-12.06-34.04-24.06-51.01-36.06-.42.45-.85.89-1.27,1.34c16.7,19.22,33.4,38.45,50.8,58.48-18.19-2.24-35.74-4.4-53.3-6.57c27.16,11.84,57.1,16.17,82.85,33.59-13.03,10.04-26.87,16.69-43.02,17.53-35.17,1.84-62.43-13.11-82.88-40.81-6.45-8.74-12.17-18.55-15.81-28.73-13.36-37.35-25.71-75.07-38.44-112.65.32-.37.61-1.02.96-1.06c41.22-4.35,82.29-5.17,122.31,7.84c32.9,10.7,59.63,29.34,73.22,62.57c11.03,26.98,9.08,53.53-5.25,80.36-8.29-8.12-16.41-15.41-23.62-23.51-2.37-2.66-2.61-7.38-3.41-11.24-3.7-17.83-7.25-35.69-10.9-53.53-.24-1.18-.88-2.27-1.23-3.14c0,18.29,0,36.74,0,55.59Z" fill="#db792f"/><path d="M763.73,666.55c-8.29-8.12-16.41-15.41-23.62-23.51-2.37-2.66-2.61-7.38-3.41-11.24-3.7-17.83-7.25-35.69-10.9-53.53-.24-1.18-.88-2.27-1.23-3.14c0,18.29,0,36.74,0,55.59-17.07-12.06-34.04-24.06-51.01-36.06-.42.45-.85.89-1.27,1.34c16.7,19.22,33.4,38.45,50.8,58.48-18.19-2.24-35.74-4.4-53.3-6.57c27.16,11.84,57.1,16.17,82.85,33.59" fill="#e5dfc3"/></g><g id="e0HZKlkRtx917"><path d="M948.14,376.98c67.31,28.2,68.34,97.2,41.21,132.58-31.42,40.98-92.32,48.1-131.5,14.42-37.2-31.98-47.17-84.78,4.73-133.56-6.42-5.3-12.86-10.6-20.81-17.15c15.34-4.6,28.39-4.46,41.81,1.49c0-6.56,0-12.32,0-18.08.57-.56,1.14-1.13,1.71-1.69c5.93,5.54,11.87,11.09,17.55,16.39c6.77-5.81,13.33-13.17,21.43-17.93c8.19-4.81,17.9-7.03,27.61-10.63c4.44,13.06,2.81,23.89-3.74,34.16Zm-66.15,29.96c-6.88,3.22-14.21,7.68-22.16,10.13-14.84,4.57-19.38,16.11-22.48,29.34-3.72,15.9.92,38,9.38,45.06-6.93-22.05-5.45-42.69,5.11-60.99.33,7.51-.23,16.2,1.35,24.48c1.6,8.38,9.33,11.78,17.32,9.29c8.96-2.79,19-19.4,17.21-28.58-1.83-9.43-3.75-18.85-5.73-28.73ZM870.72,518.98c28.29,22.98,77.17,19.04,103.6-7.75c28.61-29,25.15-68.29,11.31-87.06c10.44,33.08,6.02,63.05-20.53,86-28.63,24.74-61.06,24.52-94.38,8.81ZM910,378.7c11.37-7.77,23.56-16.09,35.75-24.42-16.69,2.51-29.03,10.77-35.75,24.42Zm-21.35,9.77c-15.83,4.06-27.42,11.82-36.16,24.11c11.51-7.68,23.03-15.36,36.16-24.11Zm-29.59-11.82c-.08.63-.17,1.26-.25,1.89c5.45,1.58,10.9,3.16,16.35,4.75-.15.45-.3.9-.45,1.35-1.11,0-2.23,0-3.35,0-.09.52-.18,1.04-.27,1.55c3.12.71,6.23,1.41,9.87,2.23-2.5-7.7-10.65-11.79-21.9-11.77Zm41.65,21.56c-9.17,2.85-10.74,6.32-8.72,17.85c2.72-5.56,5.57-11.4,8.72-17.85Z" fill="#db792f"/><path d="M882.45,406.88c-6.88,3.22-14.21,7.68-22.16,10.13-14.84,4.57-19.38,16.11-22.48,29.34-3.72,15.9.92,38,9.38,45.06-6.93-22.05-5.45-42.69,5.11-60.99.33,7.51-.23,16.2,1.35,24.48c1.6,8.38,9.33,11.78,17.32,9.29c8.96-2.79,19-19.4,17.21-28.58-1.83-9.43-3.75-18.86-5.73-28.73ZM871.18,518.91c28.29,22.98,77.17,19.04,103.6-7.75c28.61-29,25.15-68.29,11.31-87.06c10.44,33.08,6.02,63.05-20.53,86-28.63,24.75-61.06,24.52-94.38,8.81Zm39.28-140.27c11.37-7.77,23.56-16.09,35.75-24.42-16.69,2.5-29.03,10.76-35.75,24.42Zm-21.35,9.76c-15.83,4.06-27.42,11.82-36.16,24.11c11.51-7.68,23.03-15.36,36.16-24.11Zm-29.59-11.81c-.08.63-.17,1.26-.25,1.89c5.45,1.58,10.9,3.16,16.35,4.75-.15.45-.3.9-.45,1.35-1.11,0-2.23,0-3.34,0-.09.52-.18,1.04-.27,1.55c3.12.71,6.23,1.41,9.87,2.23-2.51-7.71-10.66-11.79-21.91-11.77Zm41.65,21.55c-9.17,2.85-10.74,6.32-8.72,17.85c2.72-5.56,5.57-11.4,8.72-17.85Z" fill="#e5dfc3"/></g><g id="e0HZKlkRtx920"><path d="M1120.09,636.15c-35.36-.07-63.4-28.81-63.23-64.8.17-35.85,28.62-64.62,63.77-64.5c35.39.13,65.15,29.64,65.05,64.5-.11,35.5-29.84,64.88-65.59,64.8Zm-37.1-41.78c.45,1.44.53,3.16,1.4,4.27c9,11.51,20.09,18.81,35.5,16.98c7.08-.84,14.15-1.69,21.22-2.54-.14-.82-.28-1.64-.41-2.46-1.84.47-3.66,1.02-5.52,1.4-13.65,2.81-27.13,3.02-39.15-5.14-4.93-3.35-8.86-8.17-13.24-12.32-1.13-3.91-2.16-7.86-3.41-11.73-5.83-17.99.85-38.6,16.06-49.31c15.42-10.86,36.41-10.18,51.52,1.85c3.68,2.93,6.95,6.38,11.91,10.99-6-12.73-15.83-17.99-26.41-22.4.25-.58.5-1.16.75-1.74c6.15,1.03,12.3,2.06,19.29,3.23-21.77-14.77-48.5-13.72-67.11,2.26-2.95,2.53-5.28,5.78-8.69,9.59c3.69-.7,5.76-1.09,8.91-1.68-2.73,6.15-4.34,11.57-7.3,16.1-7.6,11.68-5.59,32.55,4.68,42.65Zm18.83,4.15c.26.47.52.93.78,1.4c2.19-1.95,4.63-3.7,6.53-5.9c12.71-14.69,12.63-14.69,23.61,1.01.85,1.21,2.82,1.64,5.32,3-2.86-6.6-5.43-11.31-6.96-16.34-2.39-7.87.38-11.17,8.57-11.44c1.26-.04,2.49-.68,3.81-1.07-2.53-5.73-13.62-.98-11.41-8.84c1.56-5.55,7.98-9.74,12.77-15.15-.52-.42-2.5-2.03-3.38-2.74-5.8,5.22-10.89,9.52-15.64,14.17-3.8,3.73-6.48,3.92-9.79-.79-3.06-4.36-6.79-8.39-10.85-11.84-1.65-1.4-4.93-.89-7.47-1.25-.08.69-.16,1.38-.24,2.07c7.05,3.09,11.31,8.93,12.75,15.99.55,2.68-3.01,6.2-4.69,9.34-2.57-1.39-5.17-2.72-7.7-4.18-1.1-.64-2.06-1.54-3.1-2.33.22,3.22.4,5.88.62,9.02.58.15,2.9.73,6.66,1.69-5.06,2.13-8.61,3.63-12.16,5.13.26.73.52,1.46.78,2.19c4.54-1.07,9.05-2.38,13.65-3.12c2.05-.33,5.59-.14,6.14.97.95,1.92.9,5.17-.16,7.07-2.36,4.25-5.58,8-8.44,11.94Z" fill="#db792f"/><path d="M1082.99,594.37c.45,1.44.53,3.16,1.4,4.27c9,11.51,20.09,18.81,35.5,16.98c7.08-.84,14.15-1.69,21.22-2.54-.14-.82-.28-1.64-.41-2.46-1.84.47-3.66,1.02-5.52,1.4-13.65,2.81-27.13,3.02-39.15-5.14-4.93-3.35-8.86-8.17-13.24-12.32-1.13-3.91-2.16-7.86-3.41-11.73-5.83-17.99.85-38.6,16.06-49.31c15.42-10.86,36.41-10.18,51.52,1.85c3.68,2.93,6.95,6.38,11.91,10.99-6-12.73-15.83-17.99-26.41-22.4.25-.58.5-1.16.75-1.74c6.15,1.03,12.3,2.06,19.29,3.23-21.77-14.77-48.5-13.72-67.11,2.26-2.95,2.53-5.28,5.78-8.69,9.59c3.69-.7,5.76-1.09,8.91-1.68-2.73,6.15-4.34,11.57-7.3,16.1-7.6,11.68-5.59,32.55,4.68,42.65Zm18.83,4.15c.26.47.52.93.78,1.4c2.19-1.95,4.63-3.7,6.53-5.9c12.71-14.69,12.63-14.69,23.61,1.01.85,1.21,2.82,1.64,5.32,3-2.86-6.6-5.43-11.31-6.96-16.34-2.39-7.87.38-11.17,8.57-11.44c1.26-.04,2.49-.68,3.81-1.07-2.53-5.73-13.62-.98-11.41-8.84c1.56-5.55,7.98-9.74,12.77-15.15-.52-.42-2.5-2.03-3.38-2.74-5.8,5.22-10.89,9.52-15.64,14.17-3.8,3.73-6.48,3.92-9.79-.79-3.06-4.36-6.79-8.39-10.85-11.84-1.65-1.4-4.93-.89-7.47-1.25-.08.69-.16,1.38-.24,2.07c7.05,3.09,11.31,8.93,12.75,15.99.55,2.68-3.01,6.2-4.69,9.34-2.57-1.39-5.17-2.72-7.7-4.18-1.1-.64-2.06-1.54-3.1-2.33.22,3.22.4,5.88.62,9.02.58.15,2.9.73,6.66,1.69-5.06,2.13-8.61,3.63-12.16,5.13.26.73.52,1.46.78,2.19c4.54-1.07,9.05-2.38,13.65-3.12c2.05-.33,5.59-.14,6.14.97.95,1.92.9,5.17-.16,7.07-2.36,4.25-5.58,8-8.44,11.94Z" fill="#e5dfc3"/></g><g id="e0HZKlkRtx923"><path d="M1192.82,718.45c.31-2.72.08-5.66,1.03-8.14c6.72-17.56,17.1-32.77,30.97-45.39c6.4-5.82,10.33-12.27,13.41-20.6c15.83-42.79,46.04-71.39,89.12-86.41c8.96-3.13,17.93-6.24,26.88-9.43c23.31-8.31,41.17-23.7,55.79-43.21c4.15-5.55,7.82-11.48,12.23-16.81c2.42-2.93,5.91-4.98,8.91-7.43c2.64,3.53,6.62,6.7,7.69,10.65c3.91,14.5,2.12,29.06-1.79,43.36-8.48,30.98-23.14,58.55-45.12,82.2-19.99,21.51-44.61,32.2-73.76,32.98-13.6.36-27.2.99-40.8,1.1-37.94.29-59.85,22.77-75.45,54.09-2.21,4.44-4.02,9.08-6.01,13.63-1.05-.2-2.07-.4-3.1-.59Zm60.65-81.65c.92.81,1.84,1.63,2.76,2.44c1.51-2.37,2.75-4.99,4.59-7.08c10.12-11.46,20.53-22.62,34.4-29.76c1.34-.69,2.9-1.5,3.66-2.7c7.01-11.17,19.8-11.45,30.25-15.88c11.4-4.84,23.86-7.3,34.98-12.65c9.51-4.57,17.69-11.86,26.58-17.77c14.84-9.86,26.56-22.56,34.76-38.35c1.94-3.73,2.59-8.13,4.04-12.88-2.07.28-2.43.22-2.54.35-1.62,1.97-3.29,3.89-4.78,5.96-18.56,25.8-42.53,44.16-72.99,53.99-10.97,3.54-21.82,7.65-32.37,12.29-28.89,12.74-48.95,34.58-63.34,62.04Z" fill="#db792f"/><path d="M1253.47,637.59c.92.81,1.84,1.63,2.76,2.44c1.51-2.37,2.75-4.99,4.59-7.08c10.12-11.46,20.53-22.62,34.4-29.76c1.34-.69,2.9-1.5,3.66-2.7c7.01-11.17,19.8-11.45,30.25-15.88c11.4-4.84,23.86-7.3,34.98-12.65c9.51-4.57,17.69-11.86,26.58-17.77c14.84-9.86,26.56-22.56,34.76-38.35c1.94-3.73,2.59-8.13,4.04-12.88-2.07.28-2.43.22-2.54.35-1.62,1.97-3.29,3.89-4.78,5.96-18.56,25.8-42.53,44.16-72.99,53.99-10.97,3.54-21.82,7.65-32.37,12.29-28.89,12.74-48.95,34.58-63.34,62.04Z" fill="#e5dfc3"/></g><g id="e0HZKlkRtx926"><path d="M1046.54,704.1c21.36-1.39,41.25-.19,60.59,7.8-29.55,4.71-58.21,11.87-84.63,27.15c35.47,46.24,39.13,93.24,1.38,139.37-26.73,32.66-76.52,48.31-127.62,23.8-7.48,10.05-15.28,20.08-22.53,30.5-3.26,4.69-5.97,6.77-11.23,2.69-3.09-2.39-6.97-3.76-12.08-6.41c43.53-49.38,89.39-95.48,133.25-143.62-20.86,12.67-40.41,27.24-58.64,43.44-18.42,16.37-36.08,33.58-54.09,50.43-32.63-29.77-39.19-87.16-15.21-126.87c23.16-38.34,80.38-64.1,136.2-36.39c1.88-20.16-3.11-38.51-11.87-56.26c19.32,9.54,31.09,24.43,33.7,47.19c17.37-19.45,38.23-29.93,64.67-29.9-10.99,9.35-21.44,18.21-31.89,27.08ZM868.31,837.65c.69-8.81,1-16.77,1.99-24.64c6.26-49.73,45.83-88.33,98.12-73.42c1.71.49,5.25-1.16,6.01-2.74.97-2.02.93-5.79-.39-7.34-1.91-2.24-5.28-4.23-8.18-4.47-11.36-.92-22.97-2.68-34.14-1.3-54.05,6.69-86.52,64.76-63.41,113.91Z" fill="#db792f"/><path d="M868.31,837.65c.69-8.81,1-16.77,1.99-24.64c6.26-49.73,45.83-88.33,98.12-73.42c1.71.49,5.25-1.16,6.01-2.74.97-2.02.93-5.79-.39-7.34-1.91-2.24-5.28-4.23-8.18-4.47-11.36-.92-22.97-2.68-34.14-1.3-54.05,6.69-86.52,64.76-63.41,113.91Z" fill="#e5dfc3"/><path d="M880.99,896.5c34.41-37.51,62.73-66.56,102.45-110.16-20.86,12.67-40.41,27.24-58.64,43.44-18.42,16.37-36.08,33.58-54.09,50.43m10.28,16.29" fill="#e5dfc3"/></g><g id="e0HZKlkRtx930"><path d="M1482.59,871.5c-1.76-7.26-3.34-13.81-5.36-22.15c12.93,7.02,20.84,16.79,28.72,27.23c4.68-6.43,4.39-12.69,2.63-19.39-.51-1.93.87-4.36,1.38-6.57c1.86,1.06,4.35,1.67,5.46,3.25c6.35,9,7.05,19.01,4.32,29.4c10.43.97,20.78,1.94,31.13,2.9.13,1.01.26,2.03.39,3.04-6.66,3.38-13.33,6.76-19.94,10.12c13.16,35.07,9.08,58.32-13.21,76.22-18.55,14.9-47.54,16.84-66.58,4.45-25.06-16.31-35.51-41.39-28.34-68.05c6.5-24.2,27.38-38.74,59.4-40.45Zm4.76,16.98c-10.27-7.74-19.98-4.56-29.37-.09-14.2,6.76-23.67,21.52-21.64,34.46c11.61-20.03,27.3-32.86,51.01-34.37Z" fill="#db792f"/><path d="M1487.35,888.48c-10.27-7.74-19.98-4.56-29.37-.09-14.2,6.76-23.67,21.52-21.64,34.46c11.61-20.03,27.3-32.86,51.01-34.37Z" fill="#e5dfc3"/></g><g id="e0HZKlkRtx933"><path d="M1224,916.9c-8.52-16.6-17.21-32.89-25.3-49.47-6.93-14.18-4.39-27.06,6.33-35.03c2.57-1.91,5.64-3.16,8.5-4.67c7.91-4.17,9.66-8.86,6.31-17.25-.83-2.08-1.92-4.06-2.88-6.08-11.11-23.39,2.39-46.45,28.41-48.25c4.18-.29,8.48.2,12.65.82c9.28,1.38,17.32-.68,21.73-9.39c9.05-17.87,24.21-26.26,43.01-30.19c8.45-1.76,16.62-4.93,26-7.8c5.65,19.08,11.88,37.71,16.55,56.72c2.89,11.78-.71,23.43-6.77,33.8-7.27,12.43-6.74,23.15,3.67,33.85c8.41,8.64,10.79,19.68,6.73,31.26-4.23,12.08-13.78,17.82-26,19.7-3.21.49-6.43.9-9.61,1.53-7.74,1.54-11.72,6.06-10.19,13.84c4.02,20.35-8.17,35.38-30.93,36.5-15.66.77-31.39.15-48.74.15c3.54-7.67,7.47-14.05,9.49-20.99c4.32-14.86,13.11-23.13,29-22.98c1.45.01,2.91-.57,4.56-1.97-6.01-1.13-12.03-2.25-18.92-3.54c14.24-32.7,28.24-64.83,42.24-96.97-.68-.39-1.37-.77-2.05-1.16-27.92,42.51-55.84,85.02-83.79,127.57Z" fill="#db792f"/><path d="M1243.24,937.59c3.54-7.67,7.47-14.05,9.49-20.99c4.32-14.86,13.11-23.13,29-22.98c1.45.01,2.91-.57,4.56-1.97-6.01-1.13-12.03-2.25-18.92-3.54c14.24-32.7,28.24-64.83,42.24-96.97-.68-.39-1.36-.77-2.05-1.16-27.92,42.51-55.84,85.02-83.78,127.57" fill="#e5dfc3"/></g><g id="e0HZKlkRtx936" transform="translate(-3.313792-2.285557)"><path d="M612.92,940.86c37.93,1.37,74.61,3.71,107.39,23.06c29.6,17.46,48.31,42.71,52.12,77.73c1.8,16.61-3.27,36.69-12.5,49.46-14.59-10.33-15.28-26.24-16.69-41.88-1.01-11.17-2.15-22.34-4.51-33.58-1.47,15.76-2.94,31.53-4.42,47.3-13.6-9.04-21.51-21.03-24.22-37.01-1.98-11.67-5.93-23.01-9.66-34.32c0,12.99,0,25.99,0,39.86-17.64-15.71-34.97-31.14-52.3-46.57-.3.24-.59.48-.89.72c13.73,18.09,27.46,36.19,41.92,55.24-13.57.8-26.09,1.54-38.62,2.28-.04.28-.09.57-.13.85c11.05,2.04,22.02,4.88,33.16,5.88c10.06.9,17.36,5.14,23.29,12.83c3.15,4.08,6.62,7.9,10.52,12.53-17.69,2.25-34.35,4.38-51.01,6.5.02.48.03.96.05,1.44c16.75,1.54,33.49,3.4,50.27,4.49c7.35.48,13.54,2.19,18.8,7.59c3.45,3.54,7.64,6.37,12.09,10-17.47,11.05-36.12,13.81-55.48,10.18-38.94-7.31-61.08-33.73-74.49-68.7-10.25-26.75-9.93-54.78-7.3-82.84.69-7.42,1.67-14.81,2.61-23.04Z" fill="#db792f"/><g><path d="M759.38,1090.57c-14.59-10.33-15.28-26.24-16.69-41.88-1.01-11.17-2.15-22.34-4.51-33.58-1.47,15.76-2.94,31.53-4.42,47.3-13.6-9.04-21.51-21.03-24.22-37.01-1.98-11.67-5.93-23.01-9.66-34.32c0,12.99,0,25.99,0,39.86-17.64-15.71-34.97-31.14-52.3-46.57-.3.24-.59.48-.89.72c13.73,18.09,27.46,36.19,41.92,55.24-13.57.8-26.09,1.54-38.62,2.28-.04.28-.09.57-.13.85c11.05,2.04,22.02,4.88,33.16,5.88c10.06.9,17.36,5.14,23.29,12.83c3.15,4.08,6.62,7.9,10.52,12.53-17.69,2.25-34.35,4.38-51.01,6.5.02.48.03.96.05,1.44c16.75,1.54,33.49,3.4,50.27,4.49c7.35.48,13.54,2.19,18.8,7.59c3.45,3.54,7.64,6.37,12.09,10" fill="#e5dfc3"/></g></g><path d="" transform="translate(278.540014 495.936463)" fill="#e5dfc3" stroke="#e5dfc3" stroke-width="0" stroke-linecap="round" stroke-linejoin="round"/><path d="" transform="translate(278.540014 495.936463)" fill="#e5dfc3" stroke="#e5dfc3" stroke-width="0" stroke-linecap="round" stroke-linejoin="round"/><g id="e0HZKlkRtx942" transform="translate(-3.665066 5.136901)"><g><path d="M455.55,968c8.98,12.09,17.97,24.17,28.12,37.84-6.76-.97-11.74-1.68-16.72-2.4-5.19-.74-10.5-2.58-15.54-2.01-11.14,1.25-15.24-4.33-18.46-13.96-5.06-15.13-11.82-29.68-17.56-44.59-.94-2.43-1.44-6.98-.19-7.9c2.62-1.93,6.95-3.55,9.84-2.72c12.93,3.71,25.61,8.32,38.37,12.62c2.78.94,5.53,1.97,8.39,3c3.4-6.34,5.74-13.61,14.22-12.89c8.47.72,11.3,7.94,13.25,14.53c6.43,21.68,12.25,43.54,18.31,65.33-.76.39-1.51.77-2.27,1.16-19.5-16.35-39.01-32.69-58.51-49.04-.43.34-.84.69-1.25,1.03Z" fill="#db792f"/><path d="M517.36,1040.86c-15.98,2.66-29.95,5.17-43.99,7.28-11.18,1.68-21.93-.42-31.96-5.46-3.5-1.76-6.83-4.24-9.54-7.06-4.34-4.53-6.6-10.11-4.35-16.46c2.33-6.57,8.01-9.27,14.19-9c11.39.48,22.74,2.13,34.11,3.18c18.13,1.67,28.1,15.38,41.54,27.52Z" fill="#db792f"/><path d="M543.47,1042.15c-4.19-6.37-9.83-12.24-12.28-19.22-6.24-17.79-11.46-35.97-16.23-54.21-1.69-6.46-1.06-13.7,6.48-17.44c5.85-2.9,13.24-.52,19.14,6.3c7.34,8.48,11.05,18.69,10.48,29.66-.95,18.12-3.05,36.18-4.67,54.26-.97.21-1.95.43-2.92.65Z" fill="#db792f"/><path d="M547.9,1072.82c-17.68,3.52-35.26,7.85-53.1,10.16-6.58.85-14.35-1.98-20.47-5.22-3.48-1.84-6.46-7.93-6.4-12.06.04-2.95,5.14-7.82,8.57-8.41c14.89-2.57,29.98-3.99,45.03-5.51c1.94-.2,4.36,1.05,6.04,2.31c6.75,5.06,13.31,10.39,19.93,15.63.14,1.03.27,2.06.4,3.1Z" fill="#db792f"/><path d="M561.54,1059.22c-6.98-17.75-7.89-34.25-5.64-50.99.81-6,3.62-11.21,10.58-11.62c5.46-.32,10.85,5.42,9.59,12.8-2.32,13.6-6.45,26.9-10.04,40.27-.74,2.75-2.44,5.26-4.49,9.54Z" fill="#db792f"/></g><g><path d="M426.64,1019.65c-.07-.04,1.07-3.34,3.75-5.69c2.79-2.45,6.52-3.48,10.44-3.31c11.39.48,22.74,2.13,34.11,3.18c18.13,1.68,28.1,15.4,41.54,27.53-5.13.78-12.93,2.74-22.72,3.85-10.8,1.22-15.76,1.44-20.38,2.92-6.32,2.03-14.16.77-26.5-3.05c7.02,6.87,14.04,13.74,21.07,20.61.37-1.23,1.14-3.23,2.84-5.02c5.57-5.88,12.79-.71,26.77-4.62c3.7-1.04,10.81-3.53,17.14-4.08c1.28-.11,3.39-.22,5.96.31c0,0,2.95.61,6.04,2.31c4.05,2.24,11.91,8.67,21.2,18.22c4.25-4.36,8.5-8.73,12.75-13.09-.46-2.76-1.66-10-3.24-19.62-.64-3.88-2.94-18.41-1.51-31.88.29-2.71.89-6.9,4.14-9.33c1.96-1.46,4.17-1.74,5.55-1.79-1.48-.15-5.62-.76-9.3-4.07-4.88-4.41-5.27-10.28-5.33-11.45-.9,11.01-1.85,22.11-2.87,33.29-.83,9.1-1.69,18.14-2.58,27.12-.98.22-1.95.44-2.93.66-3.86-7.94-6.83-14.67-8.91-19.57-3.8-8.95-3.07-8.09-9.49-24.13-1.77-4.42-4.25-10.53-7.01-18.92c0,0-1.72-5.22-2.88-15.89-.28-2.57-.48-5.25,1.03-7.89c1.46-2.57,3.83-3.87,5.23-4.48-.96.7-5.5,3.86-11.4,2.88-5.74-.96-10.51-5.45-12.24-11.49c3.56,13.16,7.15,26.33,10.75,39.48c2.99,10.91,5.99,21.81,9.01,32.71-.33.39-.67.77-1,1.16-19.63-16.51-39.27-33.03-58.9-49.54-1.14.25-2.27.49-3.41.74c8.84,12.53,17.67,25.05,26.51,37.58-1.8-.21-4.58-.57-7.98-1.13-6.06-1.01-7.1-1.53-11.41-2.03-5.02-.58-5.1-.05-9.08-.69-3.78-.6-6.99-1.12-9.91-2.87-6.37-3.84-7.58-11.39-8.56-11.09-1.07.33,2.73,8.66.65,18.21-1.7,7.86-6.86,14.01-6.94,13.96Z" fill="#e5dfc3" stroke="#e5dfc3" stroke-width="0" stroke-linecap="round" stroke-linejoin="round"/></g></g><g id="e0HZKlkRtx951" transform="translate(0 0.000001)"><g><path d="M1271.13,1244.3c46.67-27.41,97.76-41.71,149.32-54.88-53.38-3.62-104.68,4.37-153.37,27.67c54.15-46.71,110.52-89.35,183.97-101.4-.21-.97-.42-1.93-.63-2.9-15.04,1.51-30.27,2.02-45.08,4.79-18.81,3.52-36.12,11.56-51.64,22.9-5.9,4.31-11.85,6.21-19.32,6.2-57.31-.07-102.81,31.78-125.68,88.45-3.15,7.81-6.73,10.6-14.5,9.26-2.16-.37-4.43-.06-7.41-.06c6.03-22.44,15.99-42.16,30.05-59.57c28.12-34.84,64.82-53.17,109.65-54.35c8.56-.23,15.36-2.41,22.69-7.11c33.13-21.24,69.86-28.66,108.82-25.2c10.33.92,20.55,3.17,31.87,4.97-11.39,22.26-22.17,43.08-32.71,64.02-12.23,24.3-24.19,48.73-36.46,73.01-.91,1.8-3.36,4.02-5.1,4.03-48.16.22-96.32.17-144.47.17Z" fill="#db792f"/><path d="M1271.13,1244.3c46.67-27.41,97.76-41.71,149.32-54.88-53.38-3.62-104.68,4.37-153.37,27.67c54.15-46.71,110.52-89.35,183.97-101.4-.21-.97-.42-1.93-.63-2.9-15.04,1.51-30.27,2.02-45.08,4.79-18.81,3.52-36.12,11.56-51.64,22.9-5.9,4.31-11.85,6.21-19.32,6.2-57.31-.07-102.81,31.78-125.68,88.45-3.15,7.81-6.73,10.6-14.5,9.26-2.16-.37-4.43-.06-7.41-.06" fill="#e5dfc3"/></g></g><path id="e0HZKlkRtx955" d="M864.28,1244.12c-18.19,0-36.38,0-54.57,0-28.49.01-56.97-.13-85.46.18-5.23.06-7.63-1.44-8.82-6.48-2.18-9.21-5.87-18.17-7.16-27.47-2.48-17.97,9.72-27.91,26.77-21.9c6.64,2.34,12.54,6.9,18.56,10.78c2.25,1.45,3.94,3.81,5.79,5.84c15.06,16.6,31.26,11.59,35.36-8.52c1.18-5.78,4.17-11.16,10.89-11.9c6.71-.73,10.69,3.89,13.21,9.26c9.98,21.26,24.79,37.88,45.43,50.21Z" fill="#db792f"/><g id="e0HZKlkRtx956" transform="translate(0 0.000001)"><path d="M682.55,1214.19c.63,1.28,1.18,2.6,2.26,5.01-20.53-10.62-36.48-24.94-51.08-40.94-4.04-4.43-7.99-8.94-12.14-13.27-22.14-23.12-41.28-20.84-57.43,7.48-26.94-12.65-54.65-15.41-82.97-3.33c31.67-.64,62.28,4.11,91.55,18.2.95-2.95,1.69-5,2.26-7.1c2.17-7.98,6.61-14.12,14.78-16.43c9.13-2.57,16.42,1.01,22.85,7.58c12.55,12.84,24.61,26.32,38.29,37.85c14.45,12.18,30.55,22.39,45.96,33.49-.5.1-1.69.56-2.88.56" fill="#e5dfc3"/><path d="M573,1188.25c.95-2.95,1.69-5,2.26-7.1c2.17-7.98,6.61-14.12,14.78-16.43c9.13-2.57,16.42,1.01,22.85,7.58c12.55,12.84,24.61,26.32,38.29,37.85c14.45,12.18,30.55,22.39,45.96,33.49-.5.1-1.69.56-2.88.56-48.71.01-97.42.04-146.12-.2-3.36-.02-7.59-1.24-9.93-3.47-29.92-28.45-64.03-49.07-106.18-57.13c8.82-11.02,19.1-18.69,30.64-24.43c28.44-14.15,58-17.37,88.71-7.98c2.42.74,7.03.24,8.06-1.35c10.91-16.79,38.7-20.86,56.69-9.44c6.88,4.37,13.35,9.73,19.04,15.59c17.72,18.25,36.15,36.01,47.64,59.29.63,1.28,1.18,2.6,2.26,5.01-20.53-10.62-36.48-24.94-51.08-40.94-4.04-4.43-7.99-8.94-12.14-13.27-22.14-23.12-41.28-20.84-57.43,7.48-26.94-12.65-54.65-15.41-82.97-3.33c31.67-.62,62.28,4.14,91.55,18.22Z" fill="#db792f"/></g><path d="M1430.7,1238.22h-812.6c-1.03.03-2.03.03-2.99,0h-80.84c-18.29,2.95-18.34,9.25-16.74,32.89c10.59,156.66,79.59,280.42,210.32,368.25c12.77,8.58,30.34,13.99,45.73,14.11c137.74,1.07,275.5.73,413.25.4c9.98-.02,21.68-.43,29.66-5.37c143.84-89.09,217.04-219.71,225.97-387.64.64-12.36-3.03-19.89-11.76-22.64Z" fill="#db792f"/>
<script><![CDATA[
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof __SVGATOR_DEFINE__&&__SVGATOR_DEFINE__.amd?__SVGATOR_DEFINE__(n):((t="undefined"!=typeof globalThis?globalThis:t||self).__SVGATOR_PLAYER__=t.__SVGATOR_PLAYER__||{},t.__SVGATOR_PLAYER__["5c7f360c"]=n())}(this,(function(){"use strict";function t(t,n){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(t,n).enumerable}))),e.push.apply(e,r)}return e}function n(n){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t(Object(r),!0).forEach((function(t){u(n,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(r,t))}))}return n}function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r(t,n){if(!(t instanceof n))throw new TypeError("Cannot call a class as a function")}function i(t,n){for(var e=0;e<n.length;e++){var r=n[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function o(t,n,e){return n&&i(t.prototype,n),e&&i(t,e),t}function u(t,n,e){return n in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function l(t,n){return(l=Object.setPrototypeOf||function(t,n){return t.__proto__=n,t})(t,n)}function s(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function f(t,n,e){return(f=s()?Reflect.construct:function(t,n,e){var r=[null];r.push.apply(r,n);var i=new(Function.bind.apply(t,r));return e&&l(i,e.prototype),i}).apply(null,arguments)}function c(t,n){if(n&&("object"==typeof n||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function h(t,n,e){return(h="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,n,e){var r=function(t,n){for(;!Object.prototype.hasOwnProperty.call(t,n)&&null!==(t=a(t)););return t}(t,n);if(r){var i=Object.getOwnPropertyDescriptor(r,n);return i.get?i.get.call(e):i.value}})(t,n,e||t)}function v(t){return function(t){if(Array.isArray(t))return y(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,n){if(!t)return;if("string"==typeof t)return y(t,n);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return y(t,n)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,r=new Array(n);e<n;e++)r[e]=t[e];return r}function g(t,n,e){if(Number.isInteger(t))return t;var r=Math.pow(10,n);return Math[e]((+t+Number.EPSILON)*r)/r}Number.isInteger||(Number.isInteger=function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t}),Number.EPSILON||(Number.EPSILON=2220446049250313e-31);var d=p(Math.pow(10,-6));function p(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6;return g(t,n,"round")}function m(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:d;return Math.abs(t-n)<e}p(Math.pow(10,-2)),p(Math.pow(10,-4));var b=Math.PI/180;function w(t){return t}function A(t,n,e){var r=1-e;return 3*e*r*(t*r+n*e)+e*e*e}function _(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1;return t<0||t>1||e<0||e>1?null:m(t,n)&&m(e,r)?w:function(i){if(i<=0)return t>0?i*n/t:0===n&&e>0?i*r/e:0;if(i>=1)return e<1?1+(i-1)*(r-1)/(e-1):1===e&&t<1?1+(i-1)*(n-1)/(t-1):1;for(var o,u=0,a=1;u<a;){var l=A(t,e,o=(u+a)/2);if(m(i,l))break;l<i?u=o:a=o}return A(n,r,o)}}function x(){return 1}function k(t){return 1===t?1:0}function S(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(1===t){if(0===n)return k;if(1===n)return x}var e=1/t;return function(t){return t>=1?1:(t+=n*e)-t%e}}var O=Math.sin,j=Math.cos,M=Math.acos,E=Math.asin,P=Math.tan,I=Math.atan2,R=Math.PI/180,F=180/Math.PI,N=Math.sqrt,T=function(){function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,u=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;r(this,t),this.m=[n,e,i,o,u,a],this.i=null,this.w=null,this.s=null}return o(t,[{key:"determinant",get:function(){var t=this.m;return t[0]*t[3]-t[1]*t[2]}},{key:"isIdentity",get:function(){if(null===this.i){var t=this.m;this.i=1===t[0]&&0===t[1]&&0===t[2]&&1===t[3]&&0===t[4]&&0===t[5]}return this.i}},{key:"point",value:function(t,n){var e=this.m;return{x:e[0]*t+e[2]*n+e[4],y:e[1]*t+e[3]*n+e[5]}}},{key:"translateSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!t&&!n)return this;var e=this.m;return e[4]+=e[0]*t+e[2]*n,e[5]+=e[1]*t+e[3]*n,this.w=this.s=this.i=null,this}},{key:"rotateSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(t%=360){var n=O(t*=R),e=j(t),r=this.m,i=r[0],o=r[1];r[0]=i*e+r[2]*n,r[1]=o*e+r[3]*n,r[2]=r[2]*e-i*n,r[3]=r[3]*e-o*n,this.w=this.s=this.i=null}return this}},{key:"scaleSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(1!==t||1!==n){var e=this.m;e[0]*=t,e[1]*=t,e[2]*=n,e[3]*=n,this.w=this.s=this.i=null}return this}},{key:"skewSelf",value:function(t,n){if(n%=360,(t%=360)||n){var e=this.m,r=e[0],i=e[1],o=e[2],u=e[3];t&&(t=P(t*R),e[2]+=r*t,e[3]+=i*t),n&&(n=P(n*R),e[0]+=o*n,e[1]+=u*n),this.w=this.s=this.i=null}return this}},{key:"resetSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0,u=this.m;return u[0]=t,u[1]=n,u[2]=e,u[3]=r,u[4]=i,u[5]=o,this.w=this.s=this.i=null,this}},{key:"recomposeSelf",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;return this.isIdentity||this.resetSelf(),t&&(t.x||t.y)&&this.translateSelf(t.x,t.y),n&&this.rotateSelf(n),e&&(e.x&&this.skewSelf(e.x,0),e.y&&this.skewSelf(0,e.y)),!r||1===r.x&&1===r.y||this.scaleSelf(r.x,r.y),i&&(i.x||i.y)&&this.translateSelf(i.x,i.y),this}},{key:"decompose",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=this.m,r=e[0]*e[0]+e[1]*e[1],i=[[e[0],e[1]],[e[2],e[3]]],o=N(r);if(0===o)return{origin:{x:p(e[4]),y:p(e[5])},translate:{x:p(t),y:p(n)},scale:{x:0,y:0},skew:{x:0,y:0},rotate:0};i[0][0]/=o,i[0][1]/=o;var u=e[0]*e[3]-e[1]*e[2]<0;u&&(o=-o);var a=i[0][0]*i[1][0]+i[0][1]*i[1][1];i[1][0]-=i[0][0]*a,i[1][1]-=i[0][1]*a;var l=N(i[1][0]*i[1][0]+i[1][1]*i[1][1]);if(0===l)return{origin:{x:p(e[4]),y:p(e[5])},translate:{x:p(t),y:p(n)},scale:{x:p(o),y:0},skew:{x:0,y:0},rotate:0};i[1][0]/=l,i[1][1]/=l,a/=l;var s=0;return i[1][1]<0?(s=M(i[1][1])*F,i[0][1]<0&&(s=360-s)):s=E(i[0][1])*F,u&&(s=-s),a=I(a,N(i[0][0]*i[0][0]+i[0][1]*i[0][1]))*F,u&&(a=-a),{origin:{x:p(e[4]),y:p(e[5])},translate:{x:p(t),y:p(n)},scale:{x:p(o),y:p(l)},skew:{x:p(a),y:0},rotate:p(s)}}},{key:"clone",value:function(){var t=this.m;return new this.constructor(t[0],t[1],t[2],t[3],t[4],t[5])}},{key:"toString",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:" ";if(null===this.s){var n=this.m.map((function(t){return p(t)}));1===n[0]&&0===n[1]&&0===n[2]&&1===n[3]?this.s="translate("+n[4]+t+n[5]+")":this.s="matrix("+n.join(t)+")"}return this.s}}],[{key:"create",value:function(t){return t?Array.isArray(t)?f(this,v(t)):t instanceof this?t.clone():(new this).recomposeSelf(t.origin,t.rotate,t.skew,t.scale,t.translate):new this}}]),t}();function q(t,n,e){return t>=.5?e:n}function B(t,n,e){return 0===t||n===e?n:t*(e-n)+n}function D(t,n,e){var r=B(t,n,e);return r<=0?0:r}function L(t,n,e){var r=B(t,n,e);return r<=0?0:r>=1?1:r}function C(t,n,e){return 0===t?n:1===t?e:{x:B(t,n.x,e.x),y:B(t,n.y,e.y)}}function V(t,n,e){var r=function(t,n,e){return Math.round(B(t,n,e))}(t,n,e);return r<=0?0:r>=255?255:r}function G(t,n,e){return 0===t?n:1===t?e:{r:V(t,n.r,e.r),g:V(t,n.g,e.g),b:V(t,n.b,e.b),a:B(t,null==n.a?1:n.a,null==e.a?1:e.a)}}function z(t,n){for(var e=[],r=0;r<t;r++)e.push(n);return e}function Y(t,n){if(--n<=0)return t;var e=(t=Object.assign([],t)).length;do{for(var r=0;r<e;r++)t.push(t[r])}while(--n>0);return t}var $,U=function(){function t(n){r(this,t),this.list=n,this.length=n.length}return o(t,[{key:"setAttribute",value:function(t,n){for(var e=this.list,r=0;r<this.length;r++)e[r].setAttribute(t,n)}},{key:"removeAttribute",value:function(t){for(var n=this.list,e=0;e<this.length;e++)n[e].removeAttribute(t)}},{key:"style",value:function(t,n){for(var e=this.list,r=0;r<this.length;r++)e[r].style[t]=n}}]),t}(),Q=/-./g,H=function(t,n){return n.toUpperCase()};function J(t){return"function"==typeof t?t:q}function Z(t){return t?"function"==typeof t?t:Array.isArray(t)?function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:w;if(!Array.isArray(t))return n;switch(t.length){case 1:return S(t[0])||n;case 2:return S(t[0],t[1])||n;case 4:return _(t[0],t[1],t[2],t[3])||n}return n}(t,null):function(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:w;switch(t){case"linear":return w;case"steps":return S(n.steps||1,n.jump||0)||e;case"bezier":case"cubic-bezier":return _(n.x1||0,n.y1||0,n.x2||0,n.y2||0)||e}return e}(t.type,t.value,null):null}function K(t,n,e){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=n.length-1;if(t<=n[0].t)return r?[0,0,n[0].v]:n[0].v;if(t>=n[i].t)return r?[i,1,n[i].v]:n[i].v;var o,u=n[0],a=null;for(o=1;o<=i;o++){if(!(t>n[o].t)){a=n[o];break}u=n[o]}return null==a?r?[i,1,n[i].v]:n[i].v:u.t===a.t?r?[o,1,a.v]:a.v:(t=(t-u.t)/(a.t-u.t),u.e&&(t=u.e(t)),r?[o,t,e(t,u.v,a.v)]:e(t,u.v,a.v))}function W(t,n){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return t&&t.length?"function"!=typeof n?null:("function"!=typeof e&&(e=null),function(r){var i=K(r,t,n);return null!=i&&e&&(i=e(i)),i}):null}function X(t,n){return t.t-n.t}function tt(t,n,r,i,o){var u,a="@"===r[0],l="#"===r[0],s=$[r],f=q;switch(a?(u=r.substr(1),r=u.replace(Q,H)):l&&(r=r.substr(1)),e(s)){case"function":if(f=s(i,o,K,Z,r,a,n,t),l)return f;break;case"string":f=W(i,J(s));break;case"object":if((f=W(i,J(s.i),s.f))&&"function"==typeof s.u)return s.u(n,f,r,a,t)}return f?function(t,n,e){if(arguments.length>3&&void 0!==arguments[3]&&arguments[3])return t instanceof U?function(r){return t.style(n,e(r))}:function(r){return t.style[n]=e(r)};if(Array.isArray(n)){var r=n.length;return function(i){var o=e(i);if(null==o)for(var u=0;u<r;u++)t[u].removeAttribute(n);else for(var a=0;a<r;a++)t[a].setAttribute(n,o)}}return function(r){var i=e(r);null==i?t.removeAttribute(n):t.setAttribute(n,i)}}(n,r,f,a):null}function nt(t,n,r,i){if(!i||"object"!==e(i))return null;var o=null,u=null;return Array.isArray(i)?u=function(t){if(!t||!t.length)return null;for(var n=0;n<t.length;n++)t[n].e&&(t[n].e=Z(t[n].e));return t.sort(X)}(i):(u=i.keys,o=i.data||null),u?tt(t,n,r,u,o):null}function et(t,n,e){if(!e)return null;var r=[];for(var i in e)if(e.hasOwnProperty(i)){var o=nt(t,n,i,e[i]);o&&r.push(o)}return r.length?r:null}function rt(t,n){if(!n.settings.duration||n.settings.duration<0)return null;var e,r,i,o,u,a=function(t,n){if(!n)return null;var e=[];if(Array.isArray(n))for(var r=n.length,i=0;i<r;i++){var o=n[i];if(2===o.length){var u=null;if("string"==typeof o[0])u=t.getElementById(o[0]);else if(Array.isArray(o[0])){u=[];for(var a=0;a<o[0].length;a++)if("string"==typeof o[0][a]){var l=t.getElementById(o[0][a]);l&&u.push(l)}u=u.length?1===u.length?u[0]:new U(u):null}if(u){var s=et(t,u,o[1]);s&&(e=e.concat(s))}}}else for(var f in n)if(n.hasOwnProperty(f)){var c=t.getElementById(f);if(c){var h=et(t,c,n[f]);h&&(e=e.concat(h))}}return e.length?e:null}(t,n.elements);return a?(e=a,r=n.settings,i=r.duration,o=e.length,u=null,function(t,n){var a=r.iterations||1/0,l=(r.alternate&&a%2==0)^r.direction>0?i:0,s=t%i,f=1+(t-s)/i;n*=r.direction,r.alternate&&f%2==0&&(n=-n);var c=!1;if(f>a)s=l,c=!0,-1===r.fill&&(s=r.direction>0?0:i);else if(n<0&&(s=i-s),s===u)return!1;u=s;for(var h=0;h<o;h++)e[h](s);return c}):null}function it(t,n){for(var e=n.querySelectorAll("svg"),r=0;r<e.length;r++)if(e[r].id===t.root&&!e[r].svgatorAnimation)return e[r].svgatorAnimation=!0,e[r];return null}function ot(t){var n=function(t){return t.shadowRoot};return document?Array.from(t.querySelectorAll(":not("+["a","area","audio","br","canvas","circle","datalist","embed","g","head","hr","iframe","img","input","link","object","path","polygon","rect","script","source","style","svg","title","track","video"].join()+")")).filter(n).map(n):[]}function ut(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=it(t,n);if(r)return r;if(e>=20)return null;for(var i=ot(n),o=0;o<i.length;o++){var u=ut(t,i[o],e+1);if(u)return u}return null}function at(t,n){if($=n,!t||!t.root||!Array.isArray(t.animations))return null;var e=ut(t);if(!e)return null;var r=t.animations.map((function(t){return rt(e,t)})).filter((function(t){return!!t}));return r.length?{svg:e,animations:r,animationSettings:t.animationSettings,options:t.options||void 0}:null}function lt(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"undefined"!=typeof BigInt&&BigInt,i="0x"+(t.replace(/[^0-9a-fA-F]+/g,"")||27);return n&&r&&e.isSafeInteger&&!e.isSafeInteger(+i)?e(r(i))%n+n:+i}function st(t,n,e){return!t||!e||n>t.length?t:t.substring(0,n)+st(t.substring(n+1),e,e)}function ft(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:27;return!t||t%n?t%n:[0,1].includes(n)?n:ft(t/n,n)}function ct(t,n,e){if(t&&t.length){var r=lt(e),i=ft(r)+5,o=st(t,ft(r,5),i);return o=o.replace(/\x7c$/g,"==").replace(/\x2f$/g,"="),o=function(t,n,e){var r=+("0x"+t.substring(0,4));t=t.substring(4);for(var i=lt(n,r)%r+e%27,o=[],u=0;u<t.length;u+=2)if("|"!==t[u]){var a=+("0x"+t[u]+t[u+1])-i;o.push(a)}else{var l=+("0x"+t.substring(u+1,u+1+4))-i;u+=3,o.push(l)}return String.fromCharCode.apply(String,o)}(o=(o=atob(o)).replace(/[\x41-\x5A]/g,""),n,r),o=JSON.parse(o)}}var ht=[{key:"alternate",def:!1},{key:"fill",def:1},{key:"iterations",def:0},{key:"direction",def:1},{key:"speed",def:1},{key:"fps",def:100}],vt=function(){function t(n,e){var i=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;r(this,t),this._id=0,this._running=!1,this._rollingBack=!1,this._animations=n,this._settings=e,(!o||o<"2022-05-02")&&delete this._settings.speed,ht.forEach((function(t){i._settings[t.key]=i._settings[t.key]||t.def})),this.duration=e.duration,this.offset=e.offset||0,this.rollbackStartOffset=0}return o(t,[{key:"alternate",get:function(){return this._settings.alternate}},{key:"fill",get:function(){return this._settings.fill}},{key:"iterations",get:function(){return this._settings.iterations}},{key:"direction",get:function(){return this._settings.direction}},{key:"speed",get:function(){return this._settings.speed}},{key:"fps",get:function(){return this._settings.fps}},{key:"maxFiniteDuration",get:function(){return this.iterations>0?this.iterations*this.duration:this.duration}},{key:"_apply",value:function(t){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},e=this._animations,r=e.length,i=0,o=0;o<r;o++)n[o]?i++:(n[o]=e[o](t,1),n[o]&&i++);return i}},{key:"_rollback",value:function(t){var n=this,e=1/0,r=null;this.rollbackStartOffset=t,this._rollingBack=!0,this._running=!0;this._id=window.requestAnimationFrame((function i(o){if(n._rollingBack){null==r&&(r=o);var u=Math.round(t-(o-r)*n.speed);if(u>n.duration&&e!==1/0){var a=!!n.alternate&&u/n.duration%2>1,l=u%n.duration;u=(l+=a?n.duration:0)||n.duration}var s=(n.fps?1e3/n.fps:0)*n.speed,f=Math.max(0,u);f<=e-s&&(n.offset=f,e=f,n._apply(f));var c=n.iterations>0&&-1===n.fill&&u>=n.maxFiniteDuration;(u<=0||n.offset<u||c)&&n.stop(),n._id=window.requestAnimationFrame(i)}}))}},{key:"_start",value:function(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=-1/0,r=null,i={};this._running=!0;var o=function o(u){null==r&&(r=u);var a=Math.round((u-r)*t.speed+n),l=(t.fps?1e3/t.fps:0)*t.speed;if(a>=e+l&&!t._rollingBack&&(t.offset=a,e=a,t._apply(a,i)===t._animations.length))return void t.pause(!0);t._id=window.requestAnimationFrame(o)};this._id=window.requestAnimationFrame(o)}},{key:"_pause",value:function(){this._id&&window.cancelAnimationFrame(this._id),this._running=!1}},{key:"play",value:function(){if(!this._running)return this._rollingBack?this._rollback(this.offset):this._start(this.offset)}},{key:"stop",value:function(){this._pause(),this.offset=0,this.rollbackStartOffset=0,this._rollingBack=!1,this._apply(0)}},{key:"reachedToEnd",value:function(){return this.iterations>0&&this.offset>=this.iterations*this.duration}},{key:"restart",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.stop(t),this.play(t)}},{key:"pause",value:function(){this._pause()}},{key:"reverse",value:function(){this.direction=-this.direction}}],[{key:"build",value:function(t,n){delete t.animationSettings,t.options=ct(t.options,t.root,"5c7f360c"),t.animations.map((function(n){n.settings=ct(n.s,t.root,"5c7f360c"),delete n.s,t.animationSettings||(t.animationSettings=n.settings)}));var e=t.version;if(!(t=at(t,n)))return null;var r=t.options||{},i=new this(t.animations,t.animationSettings,e);return{el:t.svg,options:r,player:i}}},{key:"push",value:function(t){return this.build(t)}},{key:"init",value:function(){var t=this,n=window.__SVGATOR_PLAYER__&&window.__SVGATOR_PLAYER__["5c7f360c"];Array.isArray(n)&&n.splice(0).forEach((function(n){return t.build(n)}))}}]),t}();function yt(t){return p(t)+""}function gt(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return t&&t.length?t.map(yt).join(n):""}function dt(t){if(!t)return"transparent";if(null==t.a||t.a>=1){var n=function(t){return 1===(t=parseInt(t).toString(16)).length?"0"+t:t},e=function(t){return t.charAt(0)===t.charAt(1)},r=n(t.r),i=n(t.g),o=n(t.b);return e(r)&&e(i)&&e(o)&&(r=r.charAt(0),i=i.charAt(0),o=o.charAt(0)),"#"+r+i+o}return"rgba("+t.r+","+t.g+","+t.b+","+t.a+")"}function pt(t){return t?"url(#"+t+")":"none"}!function(){for(var t=0,n=["ms","moz","webkit","o"],e=0;e<n.length&&!window.requestAnimationFrame;++e)window.requestAnimationFrame=window[n[e]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[n[e]+"CancelAnimationFrame"]||window[n[e]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(n){var e=Date.now(),r=Math.max(0,16-(e-t)),i=window.setTimeout((function(){n(e+r)}),r);return t=e+r,i},window.cancelAnimationFrame=window.clearTimeout)}();var mt={f:null,i:function(t,n,e){return 0===t?n:1===t?e:{x:D(t,n.x,e.x),y:D(t,n.y,e.y)}},u:function(t,n){return function(e){var r=n(e);t.setAttribute("rx",yt(r.x)),t.setAttribute("ry",yt(r.y))}}},bt={f:null,i:function(t,n,e){return 0===t?n:1===t?e:{width:D(t,n.width,e.width),height:D(t,n.height,e.height)}},u:function(t,n){return function(e){var r=n(e);t.setAttribute("width",yt(r.width)),t.setAttribute("height",yt(r.height))}}};Object.freeze({M:2,L:2,Z:0,H:1,V:1,C:6,Q:4,T:2,S:4,A:7});var wt={},At=null;function _t(t){var n=function(){if(At)return At;if("object"!==("undefined"==typeof document?"undefined":e(document))||!document.createElementNS)return{};var t=document.createElementNS("http://www.w3.org/2000/svg","svg");return t&&t.style?(t.style.position="absolute",t.style.opacity="0.01",t.style.zIndex="-9999",t.style.left="-9999px",t.style.width="1px",t.style.height="1px",At={svg:t}):{}}().svg;if(!n)return function(t){return null};var r=document.createElementNS(n.namespaceURI,"path");r.setAttributeNS(null,"d",t),r.setAttributeNS(null,"fill","none"),r.setAttributeNS(null,"stroke","none"),n.appendChild(r);var i=r.getTotalLength();return function(t){var n=r.getPointAtLength(i*t);return{x:n.x,y:n.y}}}function xt(t){return wt[t]?wt[t]:wt[t]=_t(t)}function kt(t,n,e,r){if(!t||!r)return!1;var i=["M",t.x,t.y];if(n&&e&&(i.push("C"),i.push(n.x),i.push(n.y),i.push(e.x),i.push(e.y)),n?!e:e){var o=n||e;i.push("Q"),i.push(o.x),i.push(o.y)}return n||e||i.push("L"),i.push(r.x),i.push(r.y),i.join(" ")}function St(t,n,e,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=kt(t,n,e,r),u=xt(o);try{return u(i)}catch(t){return null}}function Ot(t,n,e){return t+(n-t)*e}function jt(t,n,e){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i={x:Ot(t.x,n.x,e),y:Ot(t.y,n.y,e)};return r&&(i.a=Mt(t,n)),i}function Mt(t,n){return Math.atan2(n.y-t.y,n.x-t.x)}function Et(t,n,e,r){var i=1-r;return i*i*t+2*i*r*n+r*r*e}function Pt(t,n,e,r){return 2*(1-r)*(n-t)+2*r*(e-n)}function It(t,n,e,r){var i=arguments.length>4&&void 0!==arguments[4]&&arguments[4],o=St(t,n,null,e,r);return o||(o={x:Et(t.x,n.x,e.x,r),y:Et(t.y,n.y,e.y,r)}),i&&(o.a=Rt(t,n,e,r)),o}function Rt(t,n,e,r){return Math.atan2(Pt(t.y,n.y,e.y,r),Pt(t.x,n.x,e.x,r))}function Ft(t,n,e,r,i){var o=i*i;return i*o*(r-t+3*(n-e))+3*o*(t+e-2*n)+3*i*(n-t)+t}function Nt(t,n,e,r,i){var o=1-i;return 3*(o*o*(n-t)+2*o*i*(e-n)+i*i*(r-e))}function Tt(t,n,e,r,i){var o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],u=St(t,n,e,r,i);return u||(u={x:Ft(t.x,n.x,e.x,r.x,i),y:Ft(t.y,n.y,e.y,r.y,i)}),o&&(u.a=qt(t,n,e,r,i)),u}function qt(t,n,e,r,i){return Math.atan2(Nt(t.y,n.y,e.y,r.y,i),Nt(t.x,n.x,e.x,r.x,i))}function Bt(t,n,e){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Lt(n)){if(Ct(e))return It(n,e.start,e,t,r)}else if(Lt(e)){if(Vt(n))return It(n,n.end,e,t,r)}else{if(Vt(n))return Ct(e)?Tt(n,n.end,e.start,e,t,r):It(n,n.end,e,t,r);if(Ct(e))return It(n,e.start,e,t,r)}return jt(n,e,t,r)}function Dt(t,n,e){var r=Bt(t,n,e,!0);return r.a=function(t){return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?t+Math.PI:t}(r.a)/b,r}function Lt(t){return!t.type||"corner"===t.type}function Ct(t){return null!=t.start&&!Lt(t)}function Vt(t){return null!=t.end&&!Lt(t)}var Gt=new T;var zt={f:yt,i:B},Yt={f:yt,i:L};function $t(t,n,e){return t.map((function(t){return function(t,n,e){var r=t.v;if(!r||"g"!==r.t||r.s||!r.v||!r.r)return t;var i=e.getElementById(r.r),o=i&&i.querySelectorAll("stop")||[];return r.s=r.v.map((function(t,n){var e=o[n]&&o[n].getAttribute("offset");return{c:t,o:e=p(parseInt(e)/100)}})),delete r.v,t}(t,0,e)}))}var Ut={gt:"gradientTransform",c:{x:"cx",y:"cy"},rd:"r",f:{x:"x1",y:"y1"},to:{x:"x2",y:"y2"}};function Qt(t,n,r,i,o,u,a,l){return $t(t,0,l),n=function(t,n,e){for(var r,i,o,u=t.length-1,a={},l=0;l<=u;l++)(r=t[l]).e&&(r.e=n(r.e)),r.v&&"g"===(i=r.v).t&&i.r&&(o=e.getElementById(i.r))&&(a[i.r]={e:o,s:o.querySelectorAll("stop")});return a}(t,i,l),function(i){var o=r(i,t,Ht);if(!o)return"none";if("c"===o.t)return dt(o.v);if("g"===o.t){if(!n[o.r])return pt(o.r);var u=n[o.r];return function(t,n){for(var e=t.s,r=e.length;r<n.length;r++){var i=e[e.length-1].cloneNode();i.id=Kt(i.id),t.e.appendChild(i),e=t.s=t.e.querySelectorAll("stop")}for(var o=0,u=e.length,a=n.length-1;o<u;o++)e[o].setAttribute("stop-color",dt(n[Math.min(o,a)].c)),e[o].setAttribute("offset",n[Math.min(o,a)].o)}(u,o.s),Object.keys(Ut).forEach((function(t){if(void 0!==o[t])if("object"!==e(Ut[t])){var n,r="gt"===t?(n=o[t],Array.isArray(n)?"matrix("+n.join(" ")+")":""):o[t],i=Ut[t];u.e.setAttribute(i,r)}else Object.keys(Ut[t]).forEach((function(n){if(void 0!==o[t][n]){var e=o[t][n],r=Ut[t][n];u.e.setAttribute(r,e)}}))})),pt(o.r)}return"none"}}function Ht(t,e,r){if(0===t)return e;if(1===t)return r;if(e&&r){var i=e.t;if(i===r.t)switch(e.t){case"c":return{t:i,v:G(t,e.v,r.v)};case"g":if(e.r===r.r){var o={t:i,s:Jt(t,e.s,r.s),r:e.r};return e.gt&&r.gt&&(o.gt=function(t,n,e){var r=n.length;if(r!==e.length)return q(t,n,e);for(var i=new Array(r),o=0;o<r;o++)i[o]=B(t,n[o],e[o]);return i}(t,e.gt,r.gt)),e.c?(o.c=C(t,e.c,r.c),o.rd=D(t,e.rd,r.rd)):e.f&&(o.f=C(t,e.f,r.f),o.to=C(t,e.to,r.to)),o}}if("c"===e.t&&"g"===r.t||"c"===r.t&&"g"===e.t){var u="c"===e.t?e:r,a="g"===e.t?n({},e):n({},r),l=a.s.map((function(t){return{c:u.v,o:t.o}}));return a.s="c"===e.t?Jt(t,l,a.s):Jt(t,a.s,l),a}}return q(t,e,r)}function Jt(t,n,e){if(n.length===e.length)return n.map((function(n,r){return Zt(t,n,e[r])}));for(var r=Math.max(n.length,e.length),i=[],o=0;o<r;o++){var u=Zt(t,n[Math.min(o,n.length-1)],e[Math.min(o,e.length-1)]);i.push(u)}return i}function Zt(t,n,e){return{o:L(t,n.o,e.o||0),c:G(t,n.c,e.c||{})}}function Kt(t){return t.replace(/-fill-([0-9]+)$/,(function(t,n){return"-fill-"+(+n+1)}))}var Wt={fill:Qt,"fill-opacity":Yt,stroke:Qt,"stroke-opacity":Yt,"stroke-width":zt,"stroke-dashoffset":{f:yt,i:B},"stroke-dasharray":{f:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:" ";return t&&t.length>0&&(t=t.map((function(t){return p(t,4)}))),gt(t,n)},i:function(t,n,e){var r,i,o,u=n.length,a=e.length;if(u!==a)if(0===u)n=z(u=a,0);else if(0===a)a=u,e=z(u,0);else{var l=(o=(r=u)*(i=a)/function(t,n){for(var e;n;)e=n,n=t%n,t=e;return t||1}(r,i))<0?-o:o;n=Y(n,Math.floor(l/u)),e=Y(e,Math.floor(l/a)),u=a=l}for(var s=[],f=0;f<u;f++)s.push(p(D(t,n[f],e[f])));return s}},opacity:Yt,transform:function(t,n,r,i){if(!(t=function(t,n){if(!t||"object"!==e(t))return null;var r=!1;for(var i in t)t.hasOwnProperty(i)&&(t[i]&&t[i].length?(t[i].forEach((function(t){t.e&&(t.e=n(t.e))})),r=!0):delete t[i]);return r?t:null}(t,i)))return null;var o=function(e,i,o){var u=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return t[e]?r(i,t[e],o):n&&n[e]?n[e]:u};return n&&n.a&&t.o?function(n){var e=r(n,t.o,Dt);return Gt.recomposeSelf(e,o("r",n,B,0)+e.a,o("k",n,C),o("s",n,C),o("t",n,C)).toString()}:function(t){return Gt.recomposeSelf(o("o",t,Bt,null),o("r",t,B,0),o("k",t,C),o("s",t,C),o("t",t,C)).toString()}},r:zt,"#size":bt,"#radius":mt,_:function(t,n){if(Array.isArray(t))for(var e=0;e<t.length;e++)this[t[e]]=n;else this[t]=n}},Xt=function(t){!function(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(n&&n.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),n&&l(t,n)}(u,t);var n,e,i=(n=u,e=s(),function(){var t,r=a(n);if(e){var i=a(this).constructor;t=Reflect.construct(r,arguments,i)}else t=r.apply(this,arguments);return c(this,t)});function u(){return r(this,u),i.apply(this,arguments)}return o(u,null,[{key:"build",value:function(t){var n=h(a(u),"build",this).call(this,t,Wt);if(!n)return null;n.el,n.options,function(t,n,e){t.play()}(n.player)}}]),u}(vt);return Xt.init(),Xt}));
(function(s,i,o,w,d,a,b){w[o]=w[o]||{};w[o][s]=w[o][s]||[];w[o][s].push(i);})('5c7f360c',{"root":"e0HZKlkRtx91","version":"2022-05-04","animations":[{"elements":{"e0HZKlkRtx92":{"transform":{"data":{"t":{"x":-1124.461029,"y":-1104.50589}},"keys":{"o":[{"t":0,"v":{"x":1124.461029,"y":1104.50589,"type":"corner"}},{"t":300,"v":{"x":1116.293155,"y":1226.074893,"type":"corner"}},{"t":2700,"v":{"x":1116.293155,"y":1226.074893,"type":"corner"}},{"t":3000,"v":{"x":1121.269776,"y":1115.75,"type":"corner"}}]}}},"e0HZKlkRtx95":{"transform":{"data":{"t":{"x":-887.700958,"y":-1114.660004}},"keys":{"o":[{"t":0,"v":{"x":887.700958,"y":1114.660004,"type":"corner"}},{"t":500,"v":{"x":891.443204,"y":1206.837587,"type":"corner"}},{"t":2500,"v":{"x":891.443204,"y":1206.837587,"type":"corner"}},{"t":3000,"v":{"x":887.749664,"y":1115.750004,"type":"corner"}}],"r":[{"t":0,"v":29.271702},{"t":500,"v":-16.761725},{"t":2500,"v":-16.761725},{"t":3000,"v":29.636191}]}}},"e0HZKlkRtx98":{"transform":{"data":{"t":{"x":-714.375458,"y":-837.773712}},"keys":{"o":[{"t":0,"v":{"x":714.375458,"y":837.773712,"type":"corner"}},{"t":280,"v":{"x":718.969317,"y":912.30794,"type":"corner"}},{"t":1500,"v":{"x":738.985417,"y":1238.065173,"type":"corner"}},{"t":3000,"v":{"x":716.759939,"y":837.649999,"type":"corner"}}],"r":[{"t":0,"v":0},{"t":1500,"v":69.105793},{"t":3000,"v":-1.050433}]}}},"e0HZKlkRtx911":{"transform":{"data":{"t":{"x":-480.176514,"y":-707.597504}},"keys":{"o":[{"t":0,"v":{"x":480.176514,"y":707.597504,"type":"cusp","end":{"x":594.816923,"y":821.125291}}},{"t":1500,"v":{"x":758.716531,"y":1203.533966,"type":"cusp","start":{"x":740.530574,"y":1066.367366},"end":{"x":717.554366,"y":1040.55847}}},{"t":3000,"v":{"x":481.19,"y":710.00238,"type":"cusp","start":{"x":532.486775,"y":723.252551}}}],"r":[{"t":0,"v":-48.909853},{"t":1500,"v":3.727572},{"t":3000,"v":-48.621873}]}}},"e0HZKlkRtx914":{"transform":{"data":{"t":{"x":-674.373138,"y":-605.697083}},"keys":{"o":[{"t":0,"v":{"x":674.373138,"y":605.697083,"type":"cusp","end":{"x":669.051153,"y":605.697084}}},{"t":1400,"v":{"x":803.063197,"y":1283.821558,"type":"cusp","start":{"x":814.511034,"y":1291.676088}}},{"t":1600,"v":{"x":803.063197,"y":1283.821558,"type":"cusp","start":{"x":799.448456,"y":1283.82156}}},{"t":3000,"v":{"x":674.368167,"y":604.266641,"type":"cusp","start":{"x":674.373138,"y":605.697083}}}],"r":[{"t":0,"v":0},{"t":1400,"v":45.11962},{"t":1600,"v":45.11962},{"t":3000,"v":0.14217}]}}},"e0HZKlkRtx917":{"transform":{"data":{"t":{"x":-916.381104,"y":-444.083771}},"keys":{"o":[{"t":0,"v":{"x":916.381104,"y":444.083771,"type":"corner"}},{"t":1500,"v":{"x":917.934846,"y":1241.193555,"type":"corner"}},{"t":3000,"v":{"x":913.60608,"y":446.288905,"type":"corner"}}],"r":[{"t":0,"v":0},{"t":1500,"v":40.987506},{"t":3000,"v":2.386159}]}}},"e0HZKlkRtx920":{"transform":{"data":{"t":{"x":-1121.269775,"y":-571.499939}},"keys":{"o":[{"t":0,"v":{"x":1121.269775,"y":571.499939,"type":"corner"}},{"t":1300,"v":{"x":1044.156982,"y":1291.850358,"type":"corner"}},{"t":1700,"v":{"x":1044.156982,"y":1291.850358,"type":"corner"}},{"t":3000,"v":{"x":1121.270905,"y":567.610391,"type":"corner"}}],"r":[{"t":0,"v":0},{"t":1300,"v":89.847407},{"t":3000,"v":0.226961}]}}},"e0HZKlkRtx923":{"transform":{"data":{"t":{"x":-1316.92804,"y":-600.035049}},"keys":{"o":[{"t":0,"v":{"x":1316.92804,"y":600.035049,"type":"corner"}},{"t":1300,"v":{"x":1163.288456,"y":1314.602541,"type":"corner"}},{"t":1700,"v":{"x":1163.288456,"y":1314.602541,"type":"corner"}},{"t":3000,"v":{"x":1315.713506,"y":604.22554,"type":"corner"}}],"r":[{"t":0,"v":0},{"t":1300,"v":-45.81083},{"t":1700,"v":-47.406341},{"t":3000,"v":0.138601}]}}},"e0HZKlkRtx926":{"transform":{"data":{"t":{"x":-974.227631,"y":-798.659332}},"keys":{"o":[{"t":0,"v":{"x":974.227631,"y":798.659332,"type":"corner"}},{"t":800,"v":{"x":977.322714,"y":1290.393344,"type":"corner"}},{"t":2200,"v":{"x":977.322714,"y":1290.393344,"type":"corner"}},{"t":3000,"v":{"x":966.646821,"y":802.230474,"type":"corner"}}],"r":[{"t":0,"v":0},{"t":800,"v":-62.555464},{"t":2200,"v":-62.555464},{"t":3000,"v":-1.384352}]}}},"e0HZKlkRtx930":{"transform":{"data":{"t":{"x":-1486.117798,"y":-918.749329}},"keys":{"o":[{"t":0,"v":{"x":1486.117798,"y":918.749329,"type":"cusp","end":{"x":1324.656414,"y":949.951154}}},{"t":1200,"v":{"x":1276.337788,"y":1320.578386,"type":"cusp","start":{"x":1260.994008,"y":1169.347019}}},{"t":1700,"v":{"x":1276.337788,"y":1320.578386,"type":"cusp","start":{"x":1260.994008,"y":1169.347019},"end":{"x":1310.25045,"y":1053.547621}}},{"t":3000,"v":{"x":1489.869939,"y":923.944229,"type":"cusp","start":{"x":1344.194013,"y":958.570055}}}]}}},"e0HZKlkRtx933":{"transform":{"data":{"t":{"x":-1282.851807,"y":-823.590027}},"keys":{"o":[{"t":0,"v":{"x":1282.851807,"y":823.590027,"type":"cusp","end":{"x":1275.850416,"y":823.590027}}},{"t":1400,"v":{"x":1108.567261,"y":1234.125215,"type":"cusp","start":{"x":1108.567264,"y":1226.072733}}},{"t":1600,"v":{"x":1108.567261,"y":1234.125215,"type":"cusp","start":{"x":1108.567264,"y":1226.072733}}},{"t":3000,"v":{"x":1279.62634,"y":814.0449,"type":"cusp","start":{"x":1279.626342,"y":805.992414}}}],"r":[{"t":0,"v":0},{"t":1400,"v":49.721938},{"t":3000,"v":-4.41695}]}}},"e0HZKlkRtx936":{"transform":{"data":{"t":{"x":-690.770844,"y":-1028.902771}},"keys":{"o":[{"t":0,"v":{"x":687.457052,"y":1026.617214,"type":"corner"}},{"t":600,"v":{"x":799.448271,"y":1302.48102,"type":"corner"}},{"t":2400,"v":{"x":799.448271,"y":1302.48102,"type":"corner"}},{"t":3000,"v":{"x":687.560665,"y":1025.613404,"type":"corner"}}],"r":[{"t":0,"v":0},{"t":600,"v":41.709327},{"t":2400,"v":41.709327},{"t":3000,"v":1.958721}]}}},"e0HZKlkRtx942":{"transform":{"data":{"t":{"x":-507.914628,"y":-1010.141782}},"keys":{"o":[{"t":0,"v":{"x":504.249562,"y":1015.278683,"type":"cusp","end":{"x":597.032492,"y":1055.537801}}},{"t":1500,"v":{"x":729.320106,"y":1322.812558,"type":"cusp","start":{"x":727.384106,"y":1305.482979},"end":{"x":628.237413,"y":1148.88595}}},{"t":3000,"v":{"x":504.249562,"y":1015.694428,"type":"cusp","start":{"x":630.569115,"y":1111.734306}}}],"r":[{"t":0,"v":0},{"t":1500,"v":43.652877},{"t":3000,"v":-2.471796}]}}},"e0HZKlkRtx951":{"transform":{"data":{"t":{"x":-1338.330017,"y":-1170.999328}},"keys":{"o":[{"t":0,"v":{"x":1338.330017,"y":1170.999329,"type":"corner"}},{"t":300,"v":{"x":1338.330017,"y":1214.438232,"type":"corner"}},{"t":2700,"v":{"x":1338.330017,"y":1214.438232,"type":"corner"}},{"t":3000,"v":{"x":1340.500222,"y":1173.592747,"type":"corner"}}]}}},"e0HZKlkRtx955":{"transform":{"data":{"t":{"x":-786.114807,"y":-1214.437805}},"keys":{"o":[{"t":0,"v":{"x":786.114807,"y":1214.437805,"type":"corner"}},{"t":200,"v":{"x":783.24378,"y":1228.824602,"type":"corner"}},{"t":2800,"v":{"x":783.24378,"y":1228.824602,"type":"corner"}},{"t":3000,"v":{"x":788.453391,"y":1212.094316,"type":"corner"}}]}}},"e0HZKlkRtx956":{"transform":{"data":{"t":{"x":-564.585007,"y":-1188.973083}},"keys":{"o":[{"t":0,"v":{"x":564.585007,"y":1188.973084,"type":"corner"}},{"t":300,"v":{"x":569.847563,"y":1225.200123,"type":"corner"}},{"t":2700,"v":{"x":570.527964,"y":1225.636111,"type":"corner"}},{"t":3000,"v":{"x":564.585007,"y":1188.973084,"type":"corner"}}]}}}},"s":"MMDA1ZDk2M2Q3ZlI5MDChkN2NGOGY4NDhhTzgB5M2Q1NTRlNGI0YjRiANDczZDdmODQ4ZEs4MXDdlOGY4NEw4YU44OTKNkNTVONGM0N1UzZDgV0OGY4MDhkN2M4Zjg0WOGE4OThlM2Q1NTRiNLDczZDgxODQ4Nzg3M2FQ1NTRjNDczZDdjODcO4ZjgwTThkODlFN2M4KZjgwM2Q1NTgxN2M4NHzhlODA0NzNkOGU4YjPgwODBDTjdmM2Q1NU4X0YzQ3M2Q4MThiOGUzIZDU1NGM0YjRiOTg/"}],"options":"MKDAxMDg4MmY4MDgxNmAU3ZjgxMmY0NzJmNzkG3YzZlNzEyZjhh"},'__SVGATOR_PLAYER__',window,document)
]]></script>
</svg>