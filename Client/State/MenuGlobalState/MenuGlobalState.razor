@inherits ComponentBase

<CascadingValue Value="@value" Name="@MenuGlobalStateFields.Value">
    <CascadingValue Value="@(new EventCallback<UserWeek?>(this, SetSelectedUserWeek))"
        Name="@MenuGlobalStateFields.SetSelectedUserWeek">
        <CascadingValue Value="@(new EventCallback<List<Recipe>?>(this, SetSelectedWeekRecipes))"
            Name="@MenuGlobalStateFields.SetSelectedWeekRecipes">
            <CascadingValue Value="@(new EventCallback<List<ShoppingListItem>?>(this, SetShoppingListItems))"
                Name="@MenuGlobalStateFields.SetShoppingListItems">
                @ChildContent
            </CascadingValue>
        </CascadingValue>
    </CascadingValue>
</CascadingValue>