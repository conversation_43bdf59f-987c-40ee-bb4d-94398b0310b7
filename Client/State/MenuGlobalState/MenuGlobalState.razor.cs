using Microsoft.AspNetCore.Components;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Client.State.MenuGlobalState;

public class MenuGlobalStateValue
{
    // public bool Loading { get; set; }
    // public bool Loaded { get; set; }
    public UserWeek? SelectedUserWeek { get; set; }
    public List<Recipe>? SelectedWeekRecipes { get; set; }
    public List<ShoppingListItem>? ShoppingListItems {get; set;}
}

public partial class MenuGlobalState : ComponentBase
{
    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    public MenuGlobalStateValue value = new MenuGlobalStateValue();

    public async Task SetSelectedUserWeek(UserWeek? newUserWeek)
    {
        value = new MenuGlobalStateValue()
        {
            SelectedUserWeek = newUserWeek,
            SelectedWeekRecipes = value.SelectedWeekRecipes,
            ShoppingListItems = value.ShoppingListItems
        };
    }

    public async Task SetSelectedWeekRecipes(List<Recipe>? newSelectedWeekRecipes)
    {
        value = new MenuGlobalStateValue()
        {
            SelectedUserWeek = value.SelectedUserWeek,
            SelectedWeekRecipes = newSelectedWeekRecipes,
            ShoppingListItems = value.ShoppingListItems
        };
    }

    public async Task SetShoppingListItems(List<ShoppingListItem>? newShoppingListItems)
    {
        value = new MenuGlobalStateValue()
        {
            SelectedUserWeek = value.SelectedUserWeek,
            SelectedWeekRecipes = value.SelectedWeekRecipes,
            ShoppingListItems = newShoppingListItems
        };
    }
}

public static class MenuGlobalStateFields
{
    private const string Prefix = "GlobalState_Menu";
    public const string Value = $"{Prefix}_Value";
    public const string SetSelectedUserWeek = $"{Prefix}_SetSelectedUserWeek";
    public const string SetSelectedWeekRecipes = $"{Prefix}_SetSelectedWeekRecipes";
    public const string SetShoppingListItems = $"{Prefix}_SetShoppingListItems";
}