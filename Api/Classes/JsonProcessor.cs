using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.IO;

namespace TwentyDishes.Api.Classes
{
    public static class JsonProcessor
    {
        private static readonly string folderPathRoot = Path.GetDirectoryName(Path.GetDirectoryName(Directory.GetCurrentDirectory())) + "/";

        public static JObject GetJsonObject(string fileName)
        {
            JObject result;
            using (StreamReader sr = new StreamReader(folderPathRoot + fileName))
            {
                result = JObject.Parse(sr.ReadToEnd());
            }
            return result;
        }

        public static T GetJsonExport<T>(string fileName)
        {
            T result;
            using (StreamReader sr = new StreamReader(folderPathRoot + fileName))
            {
                result = JsonConvert.DeserializeObject<T>(sr.ReadToEnd());
            }
            return result;
        }
    }
}