using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Api.Services
{
    public class Auth0Service : IAuth0Service
    {
        public IHttpClientFactory httpClientFactory { get; set; }

        public Auth0Service(IHttpClientFactory _httpClientFactory)
        {
            httpClientFactory = _httpClientFactory;
        }

        public async Task<string> GetAuthManagementAccessToken()
        {
            using (HttpClient client = new HttpClient())
            {
                HttpRequestMessage request = new HttpRequestMessage(
                    HttpMethod.Post,
                    new Uri($"{Environment.GetEnvironmentVariable("OidcApiAuthSettings__IssuerUrl")}/oauth/token"));
                // request.Content = new FormUrlEncodedContent(new Dictionary<string, string>
                // {
                //     { "client_id", Environment.GetEnvironmentVariable("OidcApiAuthSettings__ClientId") },
                //     { "client_secret", Environment.GetEnvironmentVariable("OidcApiAuthSettings__ClientSecret") },
                //     { "audience", $"{Environment.GetEnvironmentVariable("OidcApiAuthSettings__Audience")}" },
                //     { "grant_type", "client_credentials" }
                // });
                request.Content = new StringContent(
                    $"grant_type=client_credentials&client_id={Environment.GetEnvironmentVariable("OidcApiAuthSettings__ClientId")}&client_secret={Environment.GetEnvironmentVariable("OidcApiAuthSettings__ClientSecret")}&audience=https%3A%2F%2Fdev-a9uxb-c9.us.auth0.com%2Fapi%2Fv2%2F",
                    Encoding.UTF8,
                    "application/x-www-form-urlencoded"
                    );
                // request.Headers.Add("content-type", "application/x-www-form-urlencoded");

                HttpResponseMessage response = await client.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    return JObject.Parse(await response.Content.ReadAsStringAsync())["access_token"].ToString();
                }
                else
                {
                    throw new InvalidOperationException(response.StatusCode.ToString() + ": " + JObject.Parse(await response.Content.ReadAsStringAsync())["error_description"].ToString());
                }
            }
        }

        public async Task<List<UserRoleResponse>> GetUserRoles(string userId, string apiAccessToken)
        {
            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("bearer", apiAccessToken);

                return JsonConvert.DeserializeObject<List<UserRoleResponse>>(await client.GetStringAsync(Environment.GetEnvironmentVariable("OidcApiAuthSettings:IssuerUrl") + "api/v2/users/" + userId + "/roles"));
            }
        }

        public async Task ResendEmailVerificationLink(string userId, string apiAccessToken = null)
        {
            apiAccessToken ??= await GetAuthManagementAccessToken();

            var client = httpClientFactory.CreateClient();

            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiAccessToken);

            var response = await client.PostAsJsonAsync<ResendEmailVerificationLinkRequest>($"{Environment.GetEnvironmentVariable("OidcApiAuthSettings__IssuerUrl")}/api/v2/jobs/verification-email", new ResendEmailVerificationLinkRequest() { user_id = userId, identity = new() { user_id = userId.Replace("auth0|", ""), provider = "auth0" } });

            var responseMessage = await response.Content.ReadAsStringAsync();
        }

        public async Task<bool> UserEmailIsVerified(string userId, string apiAccessToken = null)
        {
            apiAccessToken ??= await GetAuthManagementAccessToken();

            var client = httpClientFactory.CreateClient();

            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiAccessToken);

            var user = await client.GetFromJsonAsync<Auth0User>($"{Environment.GetEnvironmentVariable("OidcApiAuthSettings:IssuerUrl")}/api/v2/users/{userId}");

            return user.email_verified;
        }

        private class ResendEmailVerificationLinkRequest
        {
            public string user_id { get; set; }

            public Auth0Identity identity { get; set; }
        }

        private class Auth0Identity
        {
            public string user_id { get; set; }

            public string provider { get; set; }
        }

        private class Auth0User
        {
            public bool email_verified { get; set; }
        }
    }
}