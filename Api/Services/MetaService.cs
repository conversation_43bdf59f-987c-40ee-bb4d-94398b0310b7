using Azure.Core;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Serialization.HybridRow;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TwentyDishes.Api.Classes;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Api.Services
{
    public class MetaService : Repository<Meta>, IMetaService
    {
        public MetaService(IDbContext dbContext) : base(dbContext)
        {
        }

        public async Task<Brand> GetBrand(int id)
        {
            JObject metaResult = await GetDbContext().Database.GetCosmosClient().GetDatabase(Environment.GetEnvironmentVariable("CosmosDatabaseName")).GetContainer("Meta").ReadItemAsync<JObject>("Brand", new PartitionKey("Brand"));
            JToken metaRecord = metaResult["values"].Where(x => x.SelectToken("id").Value<int>() == id).FirstOrDefault();

            if (metaRecord is null) return null;

            return new Brand()
            {
                BaseUrl = metaRecord.SelectToken("baseUrl").Value<string>(),
                ComplementaryColor1 = metaRecord.SelectToken("complementaryColor1").Value<string>(),
                ComplementaryColor2 = metaRecord.SelectToken("complementaryColor2").Value<string>(),
                FavIconCloudflareId = metaRecord.SelectToken("favIconCloudflareId").Value<string>(),
                GoogleAnalyticsMeasurementId = metaRecord.SelectToken("googleAnalyticsMeasurementId").Value<string>(),
                GoogleAnalyticsProperty = metaRecord.SelectToken("googleAnalyticsProperty").Value<string>(),
                IconCloudflareId = metaRecord.SelectToken("iconCloudflareId").Value<string>(),
                Id = metaRecord.SelectToken("id").Value<int>(),
                LogoCloudflareId = metaRecord.SelectToken("logoCloudflareId").Value<string>(),
                Name = metaRecord.SelectToken("name").Value<string>(),
                PageTitle = metaRecord.SelectToken("pageTitle").Value<string>(),
                PrimaryColor = metaRecord.SelectToken("primaryColor").Value<string>(),
                PrimaryFont = metaRecord.SelectToken("primaryFont").Value<string>(),
                SecondaryColor = metaRecord.SelectToken("secondaryColor").Value<string>(),
                SecondaryFont = metaRecord.SelectToken("secondaryFont").Value<string>(),
                TextColor = metaRecord.SelectToken("textColor").Value<string>()
            };
        }

        public async Task<Brand> GetBrand(string baseUrl)
        {
            JObject metaResult = await GetDbContext().Database.GetCosmosClient().GetDatabase(Environment.GetEnvironmentVariable("CosmosDatabaseName")).GetContainer("Meta").ReadItemAsync<JObject>("Brand", new PartitionKey("Brand"));
            JToken metaRecord = metaResult["values"].Where(x => baseUrl.Contains(x.SelectToken("baseUrl").Value<string>(), StringComparison.InvariantCultureIgnoreCase)).FirstOrDefault();

            if (metaRecord is null) return null;

            return new Brand()
            {
                BaseUrl = metaRecord.SelectToken("baseUrl").Value<string>(),
                ComplementaryColor1 = metaRecord.SelectToken("complementaryColor1").Value<string>(),
                ComplementaryColor2 = metaRecord.SelectToken("complementaryColor2").Value<string>(),
                FavIconCloudflareId = metaRecord.SelectToken("favIconCloudflareId").Value<string>(),
                GoogleAnalyticsMeasurementId = metaRecord.SelectToken("googleAnalyticsMeasurementId").Value<string>(),
                GoogleAnalyticsProperty = metaRecord.SelectToken("googleAnalyticsProperty").Value<string>(),
                IconCloudflareId = metaRecord.SelectToken("iconCloudflareId").Value<string>(),
                Id = metaRecord.SelectToken("id").Value<int>(),
                LogoCloudflareId = metaRecord.SelectToken("logoCloudflareId").Value<string>(),
                Name = metaRecord.SelectToken("name").Value<string>(),
                PageTitle = metaRecord.SelectToken("pageTitle").Value<string>(),
                PrimaryColor = metaRecord.SelectToken("primaryColor").Value<string>(),
                PrimaryFont = metaRecord.SelectToken("primaryFont").Value<string>(),
                SecondaryColor = metaRecord.SelectToken("secondaryColor").Value<string>(),
                SecondaryFont = metaRecord.SelectToken("secondaryFont").Value<string>(),
                TextColor = metaRecord.SelectToken("textColor").Value<string>()
            };
        }

        public async Task<List<Brand>> GetBrands()
        {
            JObject metaResult = await GetDbContext().Database.GetCosmosClient().GetDatabase(Environment.GetEnvironmentVariable("CosmosDatabaseName")).GetContainer("Meta").ReadItemAsync<JObject>("Brand", new PartitionKey("Brand"));
            List<JToken> metaRecords = metaResult["values"].ToList();
            List<Brand> result = new List<Brand>();

            foreach (JToken metaRecord in metaRecords)
            {
                Brand brand = new Brand()
                {
                    BaseUrl = metaRecord.SelectToken("baseUrl").Value<string>(),
                    ComplementaryColor1 = metaRecord.SelectToken("complementaryColor1").Value<string>(),
                    ComplementaryColor2 = metaRecord.SelectToken("complementaryColor2").Value<string>(),
                    FavIconCloudflareId = metaRecord.SelectToken("favIconCloudflareId").Value<string>(),
                    GoogleAnalyticsMeasurementId = metaRecord.SelectToken("googleAnalyticsMeasurementId").Value<string>(),
                    GoogleAnalyticsProperty = metaRecord.SelectToken("googleAnalyticsProperty").Value<string>(),
                    IconCloudflareId = metaRecord.SelectToken("iconCloudflareId").Value<string>(),
                    Id = metaRecord.SelectToken("id").Value<int>(),
                    LogoCloudflareId = metaRecord.SelectToken("logoCloudflareId").Value<string>(),
                    Name = metaRecord.SelectToken("name").Value<string>(),
                    PageTitle = metaRecord.SelectToken("pageTitle").Value<string>(),
                    PrimaryColor = metaRecord.SelectToken("primaryColor").Value<string>(),
                    PrimaryFont = metaRecord.SelectToken("primaryFont").Value<string>(),
                    SecondaryColor = metaRecord.SelectToken("secondaryColor").Value<string>(),
                    SecondaryFont = metaRecord.SelectToken("secondaryFont").Value<string>(),
                    TextColor = metaRecord.SelectToken("textColor").Value<string>()
                };
                result.Add(brand);
            }
            return result;
        }

        private string GetDailyNutritionName(string nameInJSON)
        {
            switch (nameInJSON)
            {
                case "calcium":
                    return "Calcium";
                case "cholesterol":
                    return "Cholesterol";
                case "fiberTotalDietary":
                    return "Fiber";
                case "iron":
                    return "Iron";
                case "potassium":
                    return "Potassium";
                case "protein":
                    return "Protein";
                case "fatSaturatedTotal":
                    return "SaturatedFat";
                case "sodium":
                    return "Sodium";
                case "carbohydrateByDifferenceTotal":
                    return "TotalCarbsByDifference";
                case "fatLipidTotal":
                    return "TotalFat";
                case "vitaminA":
                    return "VitaminA";
                case "vitaminC":
                    return "VitaminC";
                default:
                    return null;
            }
        }

        public async Task<List<DailyNutritionValues>> GetDailyNutritionValues()
        {
            List<DailyNutritionValues> result = new List<DailyNutritionValues>();
            JObject metaResult = await GetDbContext().Database.GetCosmosClient().GetDatabase(Environment.GetEnvironmentVariable("CosmosDatabaseName")).GetContainer("Meta").ReadItemAsync<JObject>("NutrientDailyValues", new PartitionKey("NutrientDailyValues"));

            foreach (JToken jToken in metaResult["values"])
            {
                DailyNutritionValues dnv = new DailyNutritionValues()
                {
                    Id = jToken.SelectToken("id").Value<int>(),
                    Name = GetDailyNutritionName(jToken.SelectToken("name").Value<string>()),
                    Unit = jToken.SelectToken("unit").Value<string>(),
                    Value = jToken.SelectToken("value").Value<double>(),
                };

                if (dnv.Name != null)
                {
                    result.Add(dnv);
                }
            }
            return result;
        }

        public async Task<List<Diet>> GetDiets()
        {
            List<Diet> diets = new List<Diet>();
            JObject metaResult = await GetDbContext().Database.GetCosmosClient().GetDatabase(Environment.GetEnvironmentVariable("CosmosDatabaseName")).GetContainer("Meta").ReadItemAsync<JObject>("Diets", new PartitionKey("Diets"));

            foreach (JToken jToken in metaResult["values"])
            {
                Diet diet = new Diet()
                {
                    Name = jToken.SelectToken("name").Value<string>(),
                    Notes = jToken.SelectToken("notes").Value<string>()
                };
                diets.Add(diet);
            }
            return diets.OrderBy(x => x.Name).ToList();
        }

        public async Task<List<string>> GetFoodCategories()
        {
            List<string> categories = new List<string>();
            JObject metaResult = await GetDbContext().Database.GetCosmosClient().GetDatabase(Environment.GetEnvironmentVariable("CosmosDatabaseName")).GetContainer("Meta").ReadItemAsync<JObject>("FoodCategories", new PartitionKey("FoodCategories"));

            foreach (JToken jToken in metaResult["values"])
            {
                categories.Add(jToken.SelectToken("name").Value<string>());
            }
            return categories;
        }

        public async Task<List<string>> GetLoadingQuotes()
        {
            JObject metaResult = await GetDbContext().Database.GetCosmosClient().GetDatabase(Environment.GetEnvironmentVariable("CosmosDatabaseName")).GetContainer("Meta").ReadItemAsync<JObject>("Quotes", new PartitionKey("Quotes"));
            List<string> result = new List<string>();

            foreach (JToken jToken in metaResult["values"])
            {
                result.Add(jToken.SelectToken("quote").Value<string>());
            }

            return result;
        }

        public async Task<List<string>> GetShoppingCategories()
        {
            List<string> result = new List<string>();
            JObject metaResult = await GetDbContext().Database.GetCosmosClient().GetDatabase(Environment.GetEnvironmentVariable("CosmosDatabaseName")).GetContainer("Meta").ReadItemAsync<JObject>("ShoppingCategories", new PartitionKey("ShoppingCategories"));

            foreach (JToken jToken in metaResult["values"])
            {
                result.Add(jToken.SelectToken("name").Value<string>());
            }

            result = result.OrderBy(x => x).ToList();
            //Add custom additional items
            result.Add(IngredientHelper.AdditionalShoppingItems);

            return result;
        }

        //A simple call to get a Brand. Used in app's startup Cosmos call to force it to fetch the routing address table.
        public async Task OpenAsync()
        {
            await GetBrand(1);
        }

        public async Task UpdateBrands(List<Brand> modified)
        {
            MetaBrand metaBrand = new MetaBrand()
            {
                id = "Brand",
                pk = "Brand",
                values = modified
            };
            CosmosClientOptions clientOptions = new CosmosClientOptions()
            {
                SerializerOptions = new CosmosSerializationOptions()
                {
                    PropertyNamingPolicy = CosmosPropertyNamingPolicy.CamelCase
                }
            };
            CosmosClient cosmosClient = new CosmosClient(Environment.GetEnvironmentVariable("CosmosEndPointUrl"), Environment.GetEnvironmentVariable("CosmosAuthorizationKey"), clientOptions);
            await cosmosClient.GetDatabase(Environment.GetEnvironmentVariable("CosmosDatabaseName")).GetContainer("Meta").ReplaceItemAsync(metaBrand, "Brand", new PartitionKey("Brand"));
        }

        public async Task UpdateMeta(Meta modified)
        {
            await Update(modified);
        }
    }
}