using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection.Metadata.Ecma335;
using System.Threading.Tasks;
using Newtonsoft.Json;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Classes;

namespace TwentyDishes.Api.Services
{
    public class PaddleService : IPaddleService
    {
        private static readonly string PaddleMonthlyId = Environment.GetEnvironmentVariable("PaddleMonthlyId");
        private static readonly string PaddleQuarterlyId = Environment.GetEnvironmentVariable("PaddleQuarterlyId");
        private static readonly string PaddleYearlyId = Environment.GetEnvironmentVariable("PaddleYearlyId");

        private readonly IHttpClientFactory _httpClientFactory = null!;

        public PaddleService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public Task<bool> SubscriptionExists()
        {
            return Task.FromResult(true);
        }
        public async Task CancelSubscription(string subscriptionId)
        {
            var client = _httpClientFactory.CreateClient();

            var request = new HttpRequestMessage();
            request.Method = HttpMethod.Post;
            request.Content = new StringContent(JsonConvert.SerializeObject(new { effective_from = "next_billing_period" }), null, "application/json");
            request.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/subscriptions/{subscriptionId}/cancel");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var httpResult = await client.SendAsync(request);
        }

        public async Task CancelPreviousSubscriptions(string userEmail, string currentSubId)
        {
            var client = _httpClientFactory.CreateClient();

            var customerRequest = new HttpRequestMessage();
            customerRequest.Method = HttpMethod.Get;
            customerRequest.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/customers?search={userEmail}");
            customerRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var customerHttpResult = await client.SendAsync(customerRequest);
            PaddleCustomerSearchResult customerResult = JsonConvert.DeserializeObject<PaddleCustomerSearchResult>(await customerHttpResult.Content.ReadAsStringAsync());

            var customerId = customerResult is not null && customerResult.Data is not null && customerResult.Data.Length > 0 ? customerResult.Data[0].Id : null;

            if (customerId is null) return;

            var subRequest = new HttpRequestMessage();
            subRequest.Method = HttpMethod.Get;
            subRequest.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/subscriptions?customer_id={customerId}");
            subRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var subHttpResult = await client.SendAsync(subRequest);
            PaddleSubscriptionSearchResult subResult = JsonConvert.DeserializeObject<PaddleSubscriptionSearchResult>(await subHttpResult.Content.ReadAsStringAsync());

            if (subResult?.Data?.Length == 0) return;

            var previousSubs = currentSubId is null ? subResult.Data : subResult.Data.Where(s => s.Id != currentSubId);

            foreach (var sub in previousSubs)
            {
                await CancelSubscription(sub.Id);
            }
        }

        public async Task<string> GetSubscriptionId(string userEmail)
        {
            var client = _httpClientFactory.CreateClient();

            var customerRequest = new HttpRequestMessage();
            customerRequest.Method = HttpMethod.Get;
            customerRequest.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/customers?search={userEmail}");
            customerRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var customerHttpResult = await client.SendAsync(customerRequest);
            PaddleCustomerSearchResult customerResult = JsonConvert.DeserializeObject<PaddleCustomerSearchResult>(await customerHttpResult.Content.ReadAsStringAsync());

            var customerId = customerResult is not null && customerResult.Data is not null && customerResult.Data.Length > 0 ? customerResult.Data[0].Id : null;

            if (customerId is null) return null;

            var subRequest = new HttpRequestMessage();
            subRequest.Method = HttpMethod.Get;
            subRequest.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/subscriptions?customer_id={customerId}");
            subRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var subHttpResult = await client.SendAsync(subRequest);
            PaddleSubscriptionSearchResult subResult = JsonConvert.DeserializeObject<PaddleSubscriptionSearchResult>(await subHttpResult.Content.ReadAsStringAsync());

            if (subResult is null || subResult.Data is null || subResult.Data.Length == 0) return null;

            var subsSorted = subResult.Data.OrderByDescending(s => s.Created_At).ToArray();

            return subsSorted[0].Id;
        }

        public async Task<PaddleSubscription> GetSubscription(string subscriptionId)
        {
            var client = _httpClientFactory.CreateClient();

            var subRequest = new HttpRequestMessage();
            subRequest.Method = HttpMethod.Get;
            subRequest.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/subscriptions/{subscriptionId}");
            subRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var subHttpResult = await client.SendAsync(subRequest);
            PaddleSubscriptionResult subResult = JsonConvert.DeserializeObject<PaddleSubscriptionResult>(await subHttpResult.Content.ReadAsStringAsync());

            return subResult.Data;
        }

        public async Task<PaddleSubscription> GetSubscriptionByUserEmail(string userEmail)
        {
            var client = _httpClientFactory.CreateClient();

            var customerRequest = new HttpRequestMessage();
            customerRequest.Method = HttpMethod.Get;
            customerRequest.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/customers?search={userEmail}");
            customerRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var customerHttpResult = await client.SendAsync(customerRequest);
            PaddleCustomerSearchResult customerResult = JsonConvert.DeserializeObject<PaddleCustomerSearchResult>(await customerHttpResult.Content.ReadAsStringAsync());

            var customerId = customerResult is not null && customerResult.Data is not null && customerResult.Data.Length > 0 ? customerResult.Data[0].Id : null;

            if (customerId is null) return null;

            var subRequest = new HttpRequestMessage();
            subRequest.Method = HttpMethod.Get;
            subRequest.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/subscriptions?customer_id={customerId}");
            subRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var subHttpResult = await client.SendAsync(subRequest);
            PaddleSubscriptionSearchResult subResult = JsonConvert.DeserializeObject<PaddleSubscriptionSearchResult>(await subHttpResult.Content.ReadAsStringAsync());

            if (subResult is null || subResult.Data is null || subResult.Data.Length == 0) return null;

            var subsSorted = subResult.Data.OrderByDescending(s => s.Created_At).ToArray();

            return subsSorted[0];
        }

        public async Task<List<PaddleTransaction>> GetTransactions(string subscriptionId)
        {
            var client = _httpClientFactory.CreateClient();

            var request = new HttpRequestMessage();
            request.Method = HttpMethod.Get;
            request.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/transactions?subscription_id={subscriptionId}");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var httpResult = await client.SendAsync(request);
            PaddleTransactionSearchResult result = JsonConvert.DeserializeObject<PaddleTransactionSearchResult>(await httpResult.Content.ReadAsStringAsync());

            return result.Data.ToList();
        }

        public async Task ModifyTransaction(string userEmail, SubscriptionHelper.SubscriptionType subscriptionType)
        {
            var itemId = subscriptionType == SubscriptionHelper.SubscriptionType.Monthly ? PaddleMonthlyId :
                subscriptionType == SubscriptionHelper.SubscriptionType.Quarterly ? PaddleQuarterlyId :
                subscriptionType == SubscriptionHelper.SubscriptionType.Annual ? PaddleYearlyId :
                null;

            if (itemId is null) return;

            var subId = await GetSubscriptionId(userEmail);

            var client = _httpClientFactory.CreateClient();

            // un-cancel the subscription first
            var resumeRequest = new HttpRequestMessage();
            resumeRequest.Method = HttpMethod.Patch;
            resumeRequest.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/subscriptions/{subId}");
            resumeRequest.Content = new StringContent(JsonConvert.SerializeObject(new PaddleResumeSubscriptionRequest() { scheduled_change = null }), null, "application/json");
            resumeRequest.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var httpResumeResult = await client.SendAsync(resumeRequest);

            var request = new HttpRequestMessage();
            request.Method = HttpMethod.Patch;
            request.RequestUri = new Uri($"https://{Environment.GetEnvironmentVariable("PaddleApiDomain")}/subscriptions/{subId}");
            request.Content = new StringContent(JsonConvert.SerializeObject(new { proration_billing_mode = "prorated_immediately", items = new object[] { new { price_id = itemId, quantity = 1 } } }), null, "application/json");
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("PaddleApiKey"));

            var httpResult = await client.SendAsync(request);
            PaddleSubscription result = JsonConvert.DeserializeObject<PaddleSubscription>(await httpResult.Content.ReadAsStringAsync());
        }

        private class PaddleTransactionSearchResult
        {
            public PaddleTransaction[] Data { get; set; }
        }

        private class PaddleSubscriptionResult
        {
            public PaddleSubscription Data { get; set; }
        }

        private class PaddleResumeSubscriptionRequest
        {
            public object scheduled_change { get; set; }
        }
    }
}