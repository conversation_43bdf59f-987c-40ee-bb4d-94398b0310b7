using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Threading.Tasks;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Classes;

namespace TwentyDishes.Api.Services
{
    public class CloudflareService : ICloudflareService
    {
        private const string AccountId = "549ed4497195fc1fc983a3b44ca3715b";
        private const string BaseAddress = "https://api.cloudflare.com/client/v4/accounts/";

        public async Task<byte[]> GetUserImage(string imageId)
        {
            using (HttpClient httpClient = new HttpClient())
            {
                return await httpClient.GetFromJsonAsync<byte[]>($"{BaseAddress}{AccountId}/images/v1/{imageId}/blob");
            }
        }

        public async Task<string> InsertUserImage(byte[] newImage)
        {
            HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, $"{BaseAddress}{AccountId}/images/v1");
            HttpResponseMessage response;
            MultipartFormDataContent content = new MultipartFormDataContent
            {
                { new StringContent(Convert.ToBase64String(newImage)), "file" }
            };

            request.Content = content;

            using (HttpClient httpClient = new HttpClient())
            {
                httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("CloudflareToken"));
                response = await httpClient.SendAsync(request);
                response.EnsureSuccessStatusCode();

                return JObject.Parse(await response.Content.ReadAsStringAsync())["id"].ToString();
            }
        }

        public async Task<string> UpdateUserImage(string oldImageId, byte[] newImage)
        {
            //At the time of writing this, there is no image update for Cloudflare's API. Instead, we have to delete then insert
            using (HttpClient httpClient = new HttpClient())
            {
                httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", Environment.GetEnvironmentVariable("CloudflareToken"));
                HttpResponseMessage response = await httpClient.DeleteAsync($"{BaseAddress}{AccountId}/images/v1/{oldImageId}");
                response.EnsureSuccessStatusCode();
            }

            using (HttpClient httpClient = new HttpClient())
            {
                return await InsertUserImage(newImage);
            }
        }
    }
}