using Microsoft.AspNetCore.Http;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Threading.Tasks;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Entities;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using TwentyDishes.Api.Classes;

namespace TwentyDishes.Api.Services
{
    public class AuthService : IAuthService
    {
        private readonly IAuth0Service auth0Service;
        private readonly IUserService _userService;

        private const string RoleAdmin = "Administrator";

        public AuthService(IAuth0Service auth0Service, IUserService userService)
        {
            this.auth0Service = auth0Service;
            _userService = userService;
        }

        public async Task<AuthTokenValidationResult> GetAuthResult(IHeaderDictionary requestHeaders)
        {
            var authClaim = requestHeaders["X-app-Authorization"][0];
            var token = authClaim?.Replace("Bearer ", "");

            List<Claim> claims = null;

            var succeeded = false;

            try
            {
                claims = await ValidateBearerToken(token);

                succeeded = true;
            }
            catch (Exception)
            {
                // Token validation failed - succeeded remains false
            }
            var auth0UserId = claims?.Find(c => c.Type == "sub")?.Value;
            var appUserId = auth0UserId.Replace("auth0|", string.Empty);

            return new AuthTokenValidationResult()
            {
                Claims = claims,
                Success = succeeded,
                Auth0UserId = auth0UserId,
                AppUserId = appUserId
            };
        }

        public async Task ResendEmailVerificationLink(string userId)
        {
            await auth0Service.ResendEmailVerificationLink(userId);
        }

        public async Task<bool> UserEmailIsVerified(string userId)
        {
            return await auth0Service.UserEmailIsVerified(userId);
        }

        public async Task<List<Claim>> ValidateBearerToken(string token)
        {
            var configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                $"{Environment.GetEnvironmentVariable("OidcApiAuthSettings__IssuerUrl")}/.well-known/openid-configuration",
                new OpenIdConnectConfigurationRetriever(),
                new HttpDocumentRetriever());

            var discoveryDocument = await configurationManager.GetConfigurationAsync();
            var signingKeys = discoveryDocument.SigningKeys;

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidIssuer = $"{Environment.GetEnvironmentVariable("OidcApiAuthSettings__IssuerUrl")}/",
                ValidateAudience = true,
                ValidAudience = Environment.GetEnvironmentVariable("OidcApiAuthSettings__Audience"),
                ValidateIssuerSigningKey = true,
                IssuerSigningKeys = signingKeys,
                ValidateLifetime = true
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
            var jwt = (JwtSecurityToken)validatedToken;

            return jwt?.Claims?.ToList();
        }
    }
}