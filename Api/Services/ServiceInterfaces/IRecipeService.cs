using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Api.Services.ServiceInterfaces
{
    public interface IRecipeService
    {
        Task<List<UserWeek>> GenerateUserWeekRecipes(User currentUser, int newWeeksNeeded, DateTime latestWeekStartDate);
        Task<List<RecipePartial>> GetAllPartialRecipes(string userId = null);
        Task<List<Recipe>> GetAllRecipes(string userId = null);
        Task<string> GetNextAvailableId();
        Task<List<RecipePartial>> GetPartialRecipesByName(string keyword, string userId = null);
        Task<Recipe> GetRecipe(string pk, string userId = null);
        Task<List<Recipe>> GetRecipes(List<string> recipesToFetch, string userId = null);
        Task<List<Recipe>> GetRecipesByName(string keyword, string userId = null);
        Task<List<Recipe>> GetWeeklyRecipes(UserWeek userWeek, string userId = null);
        Task InsertRecipe(Recipe modified);
        Task UpdateRecipe(Recipe recipe);
    }
}