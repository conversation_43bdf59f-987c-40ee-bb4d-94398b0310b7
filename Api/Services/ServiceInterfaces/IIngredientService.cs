using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Api.Services.ServiceInterfaces
{
    public interface IIngredientService
    {
        Task DeleteIngredients(List<string> ingredientIds);
        Task<List<Ingredient>> GetAllIngredients();
        Task<Ingredient> GetIngredient(string ingredientName);
        Task<Ingredient> GetIngredientById(string ingredientId);
        Task<List<string>> GetIngredientNames();
        Task<string> GetNextAvailableId();
        Task<Dictionary<string, string>> GetShoppingCategoriesByIngrNames(List<string> ingredientNames);
        Task<Dictionary<string, string>> GetShoppingCategoriesForRecipes(List<Recipe> recipes);
        Task InsertIngredient(Ingredient modified);
        IQueryable<Ingredient> QueryIngredients();
        Task UpdateIngredient(Ingredient modified);
    }
}