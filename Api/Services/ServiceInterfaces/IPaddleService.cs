using System.Collections.Generic;
using System.Threading.Tasks;
using TwentyDishes.Shared.Classes;

namespace TwentyDishes.Api.Services.ServiceInterfaces
{
    public interface IPaddleService
    {
        Task<bool> SubscriptionExists();

        Task CancelSubscription(string subscriptionId);

        Task CancelPreviousSubscriptions(string userEmail, string currentSubId);

        Task<string> GetSubscriptionId(string userEmail);

        Task<PaddleSubscription> GetSubscription(string subscriptionId);

        Task<PaddleSubscription> GetSubscriptionByUserEmail(string userEmail);

        Task<List<PaddleTransaction>> GetTransactions(string subscriptionId);

        Task ModifyTransaction(string userEmail, SubscriptionHelper.SubscriptionType subscriptionType);
    }
}