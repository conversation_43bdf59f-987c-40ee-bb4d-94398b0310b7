using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using TwentyDishes.Api.Classes;
using TwentyDishes.Shared.Entities;
using TwentyDishes.Shared.Enums;

namespace TwentyDishes.Api.Services.ServiceInterfaces
{
    public interface IUserService
    {
        Task CreateUser(User newUser);
        Task<User> GetUser(string userId);
        Task UpdateUser(User modified);
        Task<bool> UserExists(string userId);

        Task<byte[]> CreateMenuPdf(
            UserWeek userWeek,
            List<string> additionalRecipeIds,
            DateTime weekStartDate,
            bool IncludeRecipes,
            bool IncludeMealPlan,
            bool IncludePrepGuide,
            bool IncludeShoppingList,
            int brandId,
            List<ShoppingListItem> additionalIngredients,
            MenuPdfFormat? format = MenuPdfFormat.FullMenu
        );
    }
}