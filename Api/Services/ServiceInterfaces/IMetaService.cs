using System.Collections.Generic;
using System.Threading.Tasks;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Api.Services.ServiceInterfaces
{
    public interface IMetaService
    {
        Task<Brand> GetBrand(int id);
        Task<Brand> GetBrand(string baseUrl);
        Task<List<Brand>> GetBrands();
        Task<List<DailyNutritionValues>> GetDailyNutritionValues();
        Task<List<Diet>> GetDiets();
        Task<List<string>> GetFoodCategories();
        Task<List<string>> GetLoadingQuotes();
        Task<List<string>> GetShoppingCategories();
        Task OpenAsync();
        Task UpdateBrands(List<Brand> modified);
        Task UpdateMeta(Meta modified);
    }
}