using Microsoft.AspNetCore.Localization;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;
using TwentyDishes.Api.Classes;
using TwentyDishes.Api.Helpers;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;
using TwentyDishes.Shared.Enums;
using Syncfusion.Pdf.Graphics;
using Syncfusion.Pdf.Grid;
using Syncfusion.Pdf;
using Syncfusion.Drawing;

namespace TwentyDishes.Api.Services
{
    public class UserService : Repository<User>, IUserService
    {
        private readonly IRecipeService recipeService;
        private readonly IHttpClientFactory _httpClientFactory = null!;
        private readonly IMetaService _metaService;
        private readonly IIngredientService _ingredientService;
        private readonly IPaddleService _paddleService = default!;

        public UserService(IRecipeService recipeService, IDbContext dbContext, IHttpClientFactory httpClientFactory, IMetaService metaService, IIngredientService ingredientService, IPaddleService paddleService) : base(dbContext)
        {
            this.recipeService = recipeService;
            _httpClientFactory = httpClientFactory;
            _metaService = metaService;
            _ingredientService = ingredientService;
            _paddleService = paddleService;
        }

        public async Task CreateUser(User newUser)
        {
            await Insert(newUser);
        }

        public async Task<User> GetUser(string userId)
        {
            var user = await Task.FromResult((from x in GetTable()
                                              where x.Pk == userId
                                              select x).FirstOrDefault());

            if (user is not null)
            {
                if (user.SubscriptionProvider == SubscriptionHelper.PaymentSystem.Lifetime.ToString())
                {
                    user.SubscriptionProvider = SubscriptionHelper.PaymentSystem.Lifetime.ToString();
                    user.SubscriptionId = null;
                }
                else 
                {
                    var subscription = await _paddleService.GetSubscriptionByUserEmail(user.EmailAddress);

                    if (subscription is not null && subscription.Status == "active")
                    {
                        user.SubscriptionProvider = SubscriptionHelper.PaymentSystem.Paddle.ToString();
                        user.SubscriptionId = subscription.Id;
                    }
                    else
                    {
                        user.SubscriptionProvider = SubscriptionHelper.PaymentSystem.Free.ToString();
                        user.SubscriptionId = null;
                    }
                }
            }

            return user;
        }

        public async Task UpdateUser(User modified)
        {
            await Update(modified);
        }

        public async Task<bool> UserExists(string userId)
        {
            return await Task.FromResult((from x in GetTable()
                                          where x.Id == userId
                                          select x.Id).ToList().Any());
        }

        public async Task<byte[]> CreateMenuPdf(
            UserWeek userWeek,
            List<string> additionalRecipeIds,
            DateTime weekStartDate,
            bool IncludeRecipes,
            bool IncludeMealPlan,
            bool IncludePrepGuide,
            bool IncludeShoppingList,
            int brandId,
            List<ShoppingListItem> additionalIngredients,
            MenuPdfFormat? format = MenuPdfFormat.FullMenu
        )
        {
            using HttpClient client = _httpClientFactory.CreateClient();

            // get logo image
            Brand logo = await _metaService.GetBrand(brandId);
            string logoUrl = MiscUtility.GetCloudflareImageUrl(logo.BaseUrl, logo.LogoCloudflareId, MiscUtility.ImageUrlSuffixPublic);
            var logoImageStream = await MenuPdfHelpers.GetImageStreamFromUrl(logoUrl, client);
            PdfBitmap logoImage = new PdfBitmap(logoImageStream);

            // data setup
            List<List<Recipe>> weekRecipesByDay = new List<List<Recipe>>();
            List<Recipe> allRecipes = new List<Recipe>();
            Dictionary<string, List<(string, double)>> assembledStepsDictionary = new Dictionary<string, List<(string, double)>>();
            List<(string, string)> assembledSteps = new List<(string, string)>();
            List<string> allShoppingCategories = await _metaService.GetShoppingCategories();
            Dictionary<string, List<string>> ingredientIdsByShoppingCategory = new Dictionary<string, List<string>>();
            Dictionary<string, Dictionary<string, double>> unitValuesByIngredientId = new Dictionary<string, Dictionary<string, double>>();
            Dictionary<string, string> ingredientNamesById = new Dictionary<string, string>();

            var weekRecipes = userWeek is not null ? await recipeService.GetWeeklyRecipes(userWeek) : new List<Recipe>();

            var additionalRecipes = additionalRecipeIds is not null ? await recipeService.GetRecipes(additionalRecipeIds) : new List<Recipe>();

            allRecipes = weekRecipes.Concat(additionalRecipes).ToList();

            var recipeIdsByDay = userWeek?.ToRecipeIdsByDay();

            if (recipeIdsByDay is not null)
            {
                // make dictionary of recipes keyed by day of the week index
                for (int i = 0; i < 7; i++)
                {
                    weekRecipesByDay.Add(allRecipes.Where(x => recipeIdsByDay[i].Contains(x.Id)).ToList());
                }
            }

            //Looking for the a generic note that is common in many recipes and adding it as the first key in the dictionary, so it gets sorted at the top of the dictionary
            // string genericNote = allRecipes.Where(x => x.Steps.Where(y => y.Description.Contains("Please Note:")).Any()).Select(x => x.Steps.Select(y => y.Description).FirstOrDefault()).FirstOrDefault();
            // if (!string.IsNullOrWhiteSpace(genericNote))
            // {
            //     assembledSteps.Add((genericNote, ""));
            // }

            foreach (Recipe recipe in allRecipes)
            {
                // here the ingredients and amounts are being organized by their shopping category
                foreach (var ingredient in recipe.Ingredients)
                {
                    if (ingredient is null || ingredient.Amount == null || ingredient.Amount == "0") continue;

                    var fullIngredient = ingredient.IngredientId is null ? await _ingredientService.GetIngredient(ingredient.Name) : await _ingredientService.GetIngredientById(ingredient.IngredientId);
                    var ingredientId = String.IsNullOrEmpty(ingredient.IngredientId) ? ingredient.Name : ingredient.IngredientId;
                    var unit = String.IsNullOrEmpty(ingredient.Unit) ? "none" : ingredient.Unit;
                    var ingredientName = ingredient.Name;

                    ingredientNamesById[ingredientId] = ingredientName;

                    // organize ingredient ids and category names
                    if (fullIngredient is not null)
                    {
                        if (ingredientIdsByShoppingCategory.ContainsKey(fullIngredient.ShoppingCategory))
                        {
                            if (!ingredientIdsByShoppingCategory[fullIngredient.ShoppingCategory].Any(i => i == ingredientId))
                            {
                                ingredientIdsByShoppingCategory[fullIngredient.ShoppingCategory].Add(ingredientId);
                            }
                        }
                        else
                        {
                            ingredientIdsByShoppingCategory.Add(fullIngredient.ShoppingCategory, new List<string>() { ingredientId });
                        }
                    }

                    // organize ingredients and units and values
                    if (unitValuesByIngredientId.ContainsKey(ingredientId))
                    {
                        if (unitValuesByIngredientId[ingredientId].ContainsKey(unit))
                        {
                            unitValuesByIngredientId[ingredientId][unit] += (double)ingredient.GetAmountAsNumber();
                        }
                        else
                        {
                            unitValuesByIngredientId[ingredientId].Add(unit, (double)ingredient.GetAmountAsNumber());
                        }
                    }
                    else
                    {
                        unitValuesByIngredientId.Add(ingredientId, new Dictionary<string, double>() { { unit, (double)ingredient.GetAmountAsNumber() } });
                    }
                }

                foreach (Step step in recipe.Steps)
                {
                    string stepDesc = step.Description?.Replace("[", string.Empty).Replace("]", string.Empty) ?? "";

                    if (!assembledStepsDictionary.ContainsKey(stepDesc))
                    {
                        var recipeList = new List<(string, double)>() { (recipe.Name, stepDesc.Contains("Please Note:") ? 0 : Double.Parse(step.Priority)) };
                        assembledStepsDictionary.Add(stepDesc, recipeList);
                    }
                    else
                    {
                        if (!assembledStepsDictionary[stepDesc].Any(i => i.Item1 == recipe.Name))
                        {
                            assembledStepsDictionary[stepDesc].Add((recipe.Name, stepDesc.Contains("Please Note:") ? 0 : Double.Parse(step.Priority)));
                        }
                    }
                }
            }

            assembledSteps.AddRange(assembledStepsDictionary.OrderBy(a => a.Value.First().Item2).Select(a => (a.Key, string.Join(", ", a.Value.Select(v => v.Item1).Order().ToArray()))).ToList());

            // make document
            PdfDocument document = new PdfDocument();
            PdfPage cursorPage = null;
            PdfFont bodyFont = new PdfStandardFont(PdfFontFamily.Helvetica, 13);
            PdfFont bodyFontBold = new PdfStandardFont(PdfFontFamily.Helvetica, 13, PdfFontStyle.Bold);
            PdfFont headerFont = new PdfStandardFont(PdfFontFamily.Helvetica, 30);
            PdfFont subHeaderFont = new PdfStandardFont(PdfFontFamily.Helvetica, 20);
            PdfFont subHeaderFontBold = new PdfStandardFont(PdfFontFamily.Helvetica, 20, PdfFontStyle.Bold);
            PdfFont bodySectionFont = new PdfStandardFont(PdfFontFamily.Helvetica, 16);
            PdfFont bodySectionFontBold = new PdfStandardFont(PdfFontFamily.Helvetica, 16, PdfFontStyle.Bold);
            PdfSolidBrush blackBorderBrush = new PdfSolidBrush(Color.Black);
            PdfSolidBrush whiteBorderBrush = new PdfSolidBrush(Color.White);
            PdfSolidBrush grayShapeBrush = new PdfSolidBrush(Color.LightGray);
            PdfPen blackPen = new PdfPen(PdfBrushes.Black, 1f);
            PdfStringFormat rightAlignedFormat = new PdfStringFormat() { Alignment = PdfTextAlignment.Right };
            PdfStringFormat multiLineBodyFormat = new PdfStringFormat() { WordWrap = PdfWordWrapType.Word };
            PdfLayoutResult cursorLayout = null;
            void Pages_PageAdded(object sender, PageAddedEventArgs args)
            {
                cursorPage = args.Page;
            }
            document.Pages.PageAdded += new PageAddedEventHandler(Pages_PageAdded);
            int bodySectionSpacing = 4;

            // week meal schedule section
            if (IncludeMealPlan)
            {
                cursorPage = document.Pages.Add();
                cursorLayout = new PdfLayoutResult(cursorPage, new RectangleF(0, 0, 0, 0));
                cursorLayout = MenuPdfHelpers.AddSectionHeader(cursorPage, cursorLayout, logoImage, "Meal Plan");

                PdfTextElement thisWeekElement = new PdfTextElement("This Week", subHeaderFont);
                cursorLayout = thisWeekElement.Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom));

                PdfLayoutFormat layoutFormat = new PdfLayoutFormat();
                layoutFormat.Layout = PdfLayoutType.Paginate;
                layoutFormat.Break = PdfLayoutBreakType.FitPage;

                for (int i = 0; i < weekRecipesByDay.Count; i++)
                {
                    var dayRecipes = weekRecipesByDay[i];

                    var dayDate = weekStartDate.AddDays(i);

                    PdfTextElement dayHeaderElement1 = new PdfTextElement($"{dayDate.ToString("M.dd")}", bodySectionFontBold);
                    var dayHeaderElement1Layout = dayHeaderElement1.Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 10));
                    PdfTextElement dayHeaderElement2 = new PdfTextElement($"{dayDate.ToString("ddd")}", bodySectionFont);
                    dayHeaderElement2.Draw(cursorPage, new PointF(dayHeaderElement1Layout.Bounds.Width + 10, dayHeaderElement1Layout.Bounds.Top));
                    cursorLayout = dayHeaderElement1Layout;

                    foreach (var recipe in dayRecipes)
                    {
                        var borderSpacing = 3;

                        PdfTextElement dayRecipeElement1 = new PdfTextElement($"{recipe.Diets.FirstOrDefault()}", bodyFontBold);
                        PdfTextElement dayRecipeElement2 = new PdfTextElement($"{recipe.Name}", bodyFont);

                        var elementTextHeight = 30;
                        var elementTotalHeight = elementTextHeight + (bodySectionSpacing * 2);

                        // this element needs to paginate together
                        if (cursorPage.GetClientSize().Height - cursorLayout.Bounds.Bottom < elementTotalHeight)
                        {
                            cursorPage = document.Pages.Add();
                            cursorLayout = new PdfLayoutResult(cursorPage, new RectangleF(0, 0, 0, 0));
                        }

                        var dayRecipeElement1Layout = dayRecipeElement1.Draw(cursorPage, new PointF(5, cursorLayout.Bounds.Bottom + borderSpacing + bodySectionSpacing));
                        var dayRecipeElement2Layout = dayRecipeElement2.Draw(cursorPage, new PointF(5, dayRecipeElement1Layout.Bounds.Bottom));

                        RectangleF daySectionBox = new RectangleF(2, dayRecipeElement1Layout.Bounds.Top - borderSpacing, cursorPage.GetClientSize().Width - 6, elementTotalHeight);
                        cursorPage.Graphics.DrawRectangle(blackPen, daySectionBox);

                        cursorLayout = new PdfLayoutResult(cursorPage, new RectangleF(0, dayRecipeElement1Layout.Bounds.Top - borderSpacing, cursorPage.GetClientSize().Width - 6, elementTotalHeight));
                    }
                }
            }

            // recipe steps section
            if (IncludePrepGuide)
            {
                cursorPage = document.Pages.Add();
                cursorLayout = new PdfLayoutResult(cursorPage, new RectangleF(0, 0, 0, 0));
                cursorLayout = MenuPdfHelpers.AddSectionHeader(cursorPage, cursorLayout, logoImage, "Simple Food Prep Guide");

                PdfGrid pdfGrid = new PdfGrid();
                pdfGrid.AllowRowBreakAcrossPages = false;
                pdfGrid.BeginCellLayout += PdfGrid_BeginCellLayout;
                void PdfGrid_BeginCellLayout(object sender, PdfGridBeginCellLayoutEventArgs args)
                {
                    if (args.CellIndex == 2 && !args.IsHeaderRow)
                    {
                        args.Graphics.DrawRectangle(blackBorderBrush, new RectangleF(args.Bounds.X + 8, args.Bounds.Y + 8, 24, 24));
                        args.Graphics.DrawRectangle(whiteBorderBrush, new RectangleF(args.Bounds.X + 10, args.Bounds.Y + 10, 20, 20));
                    }
                }
                PdfGridStyle gridStyle = new PdfGridStyle();
                gridStyle.Font = bodyFont;
                gridStyle.CellPadding = new PdfPaddings(5, 5, 5, 5);
                pdfGrid.Style = gridStyle;
                PdfGridCellStyle headerCellStyle = new PdfGridCellStyle();
                headerCellStyle.Font = bodySectionFontBold;
                headerCellStyle.Borders.All = PdfPens.LightGray;
                // headerCellStyle.BackgroundBrush = PdfBrushes.BlanchedAlmond;
                pdfGrid.Columns.Add(3);
                pdfGrid.Headers.Add(1);
                PdfGridRow pdfGridHeader = pdfGrid.Headers[0];
                pdfGridHeader.Cells[0].Value = "Instructions";
                pdfGridHeader.Cells[0].Style = headerCellStyle;
                pdfGridHeader.Cells[1].Value = "Recipe & Ingredients";
                pdfGridHeader.Cells[1].Style = headerCellStyle;
                pdfGridHeader.Cells[2].Value = "Done";
                pdfGridHeader.Cells[2].Style = headerCellStyle;
                var rowIndex = 1;
                foreach (var step in assembledSteps)
                {
                    PdfGridRow pdfGridRow = pdfGrid.Rows.Add();
                    pdfGridRow.Cells[0].Value = step.Item1;
                    pdfGridRow.Cells[0].Style.Borders.All = PdfPens.LightGray;
                    pdfGridRow.Cells[1].Value = step.Item2;
                    pdfGridRow.Cells[1].Style.Borders.All = PdfPens.LightGray;
                    pdfGridRow.Cells[2].Style.Borders.All = PdfPens.LightGray;
                    // pdfGridRow.Style = new PdfGridRowStyle { BackgroundBrush = rowIndex % 2 > 0 ? PdfBrushes.Tan : PdfBrushes.LightGray };
                    rowIndex++;
                }
                pdfGrid.Columns[0].Width = 250;
                pdfGrid.Columns[2].Width = 70;
                pdfGrid.Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 15));
            }

            // shopping list section
            if (IncludeShoppingList)
            {
                cursorPage = document.Pages.Add();
                cursorLayout = new PdfLayoutResult(cursorPage, new RectangleF(0, 0, 0, 0));
                cursorLayout = MenuPdfHelpers.AddSectionHeader(cursorPage, cursorLayout, logoImage, "Shopping List");

                var additionalItemsPrinted = false;

                foreach (var category in ingredientIdsByShoppingCategory)
                {
                    PdfTextElement categoryNameElement = new PdfTextElement(category.Key, subHeaderFont);
                    cursorLayout = categoryNameElement.Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 10));

                    foreach (var ingredientId in category.Value)
                    {
                        var units = unitValuesByIngredientId[ingredientId];

                        foreach (var unit in units)
                        {
                            PdfTextElement ingredientAmountElement = new PdfTextElement($"{IngredientHelper.UnitAmountToString(unit.Value, unit.Key == "none" ? "" : unit.Key)} {ingredientNamesById[ingredientId]}", bodyFont);
                            cursorLayout = ingredientAmountElement.Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 4));
                        }
                    }

                    if (category.Key == "Additional Items")
                    {
                        foreach (var ingredient in additionalIngredients)
                        {
                            PdfTextElement ingredientAmountElement2 = new PdfTextElement($"{IngredientHelper.UnitAmountToString(ingredient.Amount, ingredient.AmountUnit == "none" ? "" : ingredient.AmountUnit)} {ingredient.IngredientName}", bodyFont);
                            cursorLayout = ingredientAmountElement2.Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 4));
                        }

                        additionalItemsPrinted = true;
                    }
                }

                if (!additionalItemsPrinted && additionalIngredients is not null && additionalIngredients.Any())
                {
                    PdfTextElement categoryNameElement = new PdfTextElement("Additional Items", subHeaderFont);
                    cursorLayout = categoryNameElement.Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 10));

                    foreach (var ingredient in additionalIngredients)
                    {
                        PdfTextElement ingredientAmountElement2 = new PdfTextElement($"{IngredientHelper.UnitAmountToString(ingredient.Amount, ingredient.AmountUnit == "none" ? "" : ingredient.AmountUnit)} {ingredient.IngredientName}", bodyFont);
                        cursorLayout = ingredientAmountElement2.Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 4));
                    }

                    additionalItemsPrinted = true;
                }
            }

            // recipe details section
            if (IncludeRecipes)
            {
                cursorPage = document.Pages.Add();
                cursorLayout = new PdfLayoutResult(cursorPage, new RectangleF(0, 0, 0, 0));
                cursorLayout = MenuPdfHelpers.AddSectionHeader(cursorPage, cursorLayout, logoImage, format == MenuPdfFormat.FullMenu ? "Recipes" : "Recipe");
                var recipeIndex = 1;
                foreach (var recipe in allRecipes)
                {
                    if (recipeIndex != 1)
                    {
                        cursorPage = document.Pages.Add();
                        cursorLayout = new PdfLayoutResult(cursorPage, new RectangleF(0, 0, 0, 0));
                    }

                    cursorLayout = new PdfTextElement(recipe.Name, subHeaderFont).Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 10));
                    cursorLayout = new PdfTextElement($"Cook in {recipe.CookTime} mins  -  Prep in {recipe.PrepTime} mins  -  Serves {recipe.Servings} {recipe.ServingsUnit}", bodyFont).Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 5));

                    string recipeImageUrl = MiscUtility.GetCloudflareImageUrl(logo.BaseUrl, recipe.CloudflareImageId, MiscUtility.ImageUrlSuffixPublic);
                    var recipeImageStream = await MenuPdfHelpers.GetImageStreamFromUrl(recipeImageUrl, client);
                    PdfBitmap recipeImage = new PdfBitmap(recipeImageStream);
                    cursorPage.Graphics.DrawImage(recipeImage, 0, cursorLayout.Bounds.Bottom + 5, 150, 100);
                    cursorLayout = new PdfLayoutResult(cursorPage, new RectangleF(cursorLayout.Bounds.X, cursorLayout.Bounds.Y, cursorLayout.Bounds.Width, 135));

                    cursorLayout = new PdfTextElement("Ingredients", subHeaderFont).Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 10));
                    var ingredientIndex = 1;
                    foreach (var ingredient in recipe.Ingredients)
                    {
                        cursorLayout = new PdfTextElement("•", bodyFont, null, null, new PdfStringFormat { Alignment = PdfTextAlignment.Center }).Draw(cursorPage, new RectangleF(0, cursorLayout.Bounds.Bottom + 10, 20, 20));
                        cursorPage.Graphics.DrawEllipse(blackBorderBrush, new RectangleF(0, cursorLayout.Bounds.Top + 6, 5, 5));

                        cursorLayout = new PdfTextElement(IngredientHelper.FormatIngredientString(ingredient, 100), bodyFont).Draw(cursorPage, new PointF(15, cursorLayout.Bounds.Top));

                        ingredientIndex++;
                    }

                    cursorLayout = new PdfTextElement("Instructions", subHeaderFont).Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 10));
                    var instructionIndex = 1;
                    foreach (var step in recipe.Instructions)
                    {
                        if (step is null || String.IsNullOrEmpty(step.Text)) continue;

                        cursorLayout = new PdfTextElement($"{instructionIndex}", bodyFont, null, null, new PdfStringFormat { Alignment = PdfTextAlignment.Center }).Draw(cursorPage, new RectangleF(0, cursorLayout.Bounds.Bottom + 10, 20, 20));
                        cursorPage.Graphics.DrawEllipse(grayShapeBrush, new RectangleF(0, cursorLayout.Bounds.Top - 3, 20, 20));
                        new PdfTextElement($"{instructionIndex}", bodyFont, null, null, new PdfStringFormat { Alignment = PdfTextAlignment.Center }).Draw(cursorPage, new RectangleF(0, cursorLayout.Bounds.Top, 20, 20));

                        cursorLayout = new PdfTextElement(MiscUtility.ReplaceHtmlTags(step.Text), bodyFont).Draw(cursorPage, new RectangleF(30, cursorLayout.Bounds.Top, cursorPage.GetClientSize().Width - 30, cursorPage.GetClientSize().Height));

                        instructionIndex++;
                    }

                    recipeIndex++;
                }
            }

            // var footer = new PdfPageTemplateElement(new RectangleF(0, 0, document.Pages[0].GetClientSize().Width, 20));
            // // new PdfStringFormat() { Alignment = PdfTextAlignment.Right }
            // // PdfTextElement footerTextElement = new PdfTextElement(logo.Name, bodyFont, grayShapeBrush);
            // //footerTextElement.Bounds = footer.Bounds;
            // // footerTextElement.Draw(footer.Graphics, new PointF(0, 0));
            // //footer.Y = document.Pages[0].GetClientSize().Height + 40;
            // footer.Graphics.DrawString(logo.Name, bodyFont, grayShapeBrush, new PointF(0,0));
            // document.Template.Bottom = footer;

            byte[] renderedBytes = null;
            MemoryStream memoryStream = new MemoryStream();
            document.Save(memoryStream);
            renderedBytes = memoryStream.ToArray();

            return renderedBytes;
        }
    }
}