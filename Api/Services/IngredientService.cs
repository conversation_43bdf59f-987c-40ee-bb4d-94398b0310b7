using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Api.Services
{
    public class IngredientService : Repository<Ingredient>, IIngredientService
    {
        public IngredientService(IDbContext dbContext) : base(dbContext)
        {
        }

        public async Task DeleteIngredients(List<string> ingredientIds)
        {
            List<Ingredient> ingredientsToDelete = await Task.FromResult((from x in GetTable()
                                                                          where ingredientIds.Contains(x.Id)
                                                                          select x).ToList());

            await Delete(ingredientsToDelete);
        }

        public async Task<List<Ingredient>> GetAllIngredients()
        {
            return await Task.FromResult((from x in GetTable()
                                          select x).OrderBy(x => x.Name).ToList());
        }

        public async Task<Ingredient> GetIngredient(string ingredientName)
        {
            return await Task.FromResult((from x in GetTable()
                                          where x.Name.ToUpper().Trim() == ingredientName.ToUpper().Trim()
                                          select x).FirstOrDefault());
        }

        public async Task<Ingredient> GetIngredientById(string ingredientId)
        {
            return await Task.FromResult((from x in GetTable()
                                          where x.Pk == ingredientId
                                          select x).FirstOrDefault());
        }

        public async Task<List<string>> GetIngredientNames()
        {
            return await Task.FromResult((from x in GetTable()
                                          where x.Name != null && x.Name.Trim().Length > 0
                                          select x.Name).OrderBy(x => x).ToList());
        }

        public async Task<string> GetNextAvailableId()
        {
            long nextId = await Task.FromResult((from x in GetTable()
                                                 select long.Parse(x.Pk)).ToList().Max());
            nextId++;
            return nextId.ToString();
        }

        public async Task<Dictionary<string, string>> GetShoppingCategoriesByIngrNames(List<string> ingredientNames)
        {
            return await Task.FromResult((from x in GetTable()
                                          where ingredientNames.Contains(x.Name.ToLower())
                                          select x).ToList().GroupBy(x => x.Name).Select(x => x.FirstOrDefault()).OrderBy(x => x.Name).ToDictionary(x => x.Name.ToLower(), x => x.ShoppingCategory));
        }

        public async Task<Dictionary<string, string>> GetShoppingCategoriesForRecipes(List<Recipe> recipes)
        {
            List<string> ingredientNames = new List<string>();

            foreach (Recipe recipe in recipes)
            {
                foreach (Recipe.RecipeIngredient ingredient in recipe.Ingredients)
                {
                    if (!ingredientNames.Contains(ingredient.Name, StringComparer.InvariantCultureIgnoreCase))
                    {
                        ingredientNames.Add(ingredient.Name.ToLower());
                    }
                }
            }

            return await GetShoppingCategoriesByIngrNames(ingredientNames);
        }

        public async Task InsertIngredient(Ingredient modified)
        {
            await Insert(modified);
        }

        public IQueryable<Ingredient> QueryIngredients()
        {
            return GetTable().AsQueryable();
        }

        public async Task UpdateIngredient(Ingredient modified)
        {
            await Update(modified);
        }
    }
}