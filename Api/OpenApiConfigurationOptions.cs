using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Configurations;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.OpenApi.Models;
using System;
using System.Collections.Generic;

namespace TwentyDishes.Api
{
    public class OpenApiConfigurationOptions : DefaultOpenApiConfigurationOptions
    {
        public override OpenApiInfo Info { get; set; } = new OpenApiInfo()
        {
            Version = "v1",
            Title = "TwentyDishes API",
            Description = "A comprehensive meal planning and recipe management API built with Azure Functions. " +
                         "This API provides endpoints for managing users, recipes, ingredients, and generating meal plans.",
            Contact = new OpenApiContact()
            {
                Name = "TwentyDishes Support",
                Email = "<EMAIL>",
                Url = new Uri("https://twentydishes.com/support")
            },
            License = new OpenApiLicense()
            {
                Name = "MIT License",
                Url = new Uri("https://opensource.org/licenses/MIT")
            }
        };

        public override List<OpenApiServer> Servers { get; set; } = new List<OpenApiServer>()
        {
            new OpenApiServer()
            {
                Url = "https://your-function-app.azurewebsites.net",
                Description = "Production Server"
            },
            new OpenApiServer()
            {
                Url = "http://localhost:7071",
                Description = "Development Server"
            }
        };

        public override OpenApiVersionType OpenApiVersion { get; set; } = OpenApiVersionType.V3;

        public override bool IncludeRequestingHostName { get; set; } = false;

        public override bool ForceHttps { get; set; } = true;

        public override bool ForceHttp { get; set; } = false;
    }
}
