using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Api.Services;
using TwentyDishes.Shared.Entities;
using TwentyDishes.Api.Classes;

namespace TwentyDishes.Api.Functions
{
    public class IngredientFunction
    {
        private readonly IAuthService authService;
        private readonly IIngredientService ingredientService;

        public IngredientFunction(IAuthService authService, IIngredientService ingredientService)
        {
            this.authService = authService;
            this.ingredientService = ingredientService;
        }

        //[Function("DeleteIngredients")]
        //public async Task<IActionResult> DeleteIngredients([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        //{
        //    try
        //    {
        //        List<int> ingredientIds = new List<int>()
        //        {
        //            368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 401, 402, 403, 404, 405, 406, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 642, 643, 644, 645, 778, 779, 780, 781, 782, 783, 1016, 1017, 1019, 1025, 5123, 5124, 5125, 5126, 5127, 5128, 5129, 5130, 5131, 5132, 5133, 5134, 5135, 5136, 5373, 6013, 6041, 6043, 6049, 6050, 6051, 6052, 6054, 6055, 6056, 6413, 6414, 6448, 6456, 6457, 6458, 6463, 6467, 7289, 7290, 7291, 7292, 7293, 7294, 7295, 7296, 7297, 7298, 7365, 7366, 7370, 7371, 7372, 7377, 8108, 8109, 8110, 8111, 8112, 8113, 8114, 8115, 8187, 8188, 8194, 8195, 8196, 8197, 8206, 9262, 9263, 9264, 9265, 9266, 9267, 9268, 9269, 9270, 9271, 9272, 9273, 9274, 9275, 9276, 9277, 9278, 9279, 9280, 9281, 9282, 9283, 9284, 9285, 9286, 9287, 9288, 9289, 9290, 9291, 9292, 9293, 9294, 9295, 9296, 9297, 9298, 9299, 9300, 9301, 9302, 9303, 9304, 9305, 9306, 9307, 9308, 9309, 9310, 9311, 9312, 9313, 9314, 9315, 9316, 9317, 9318, 9319, 9320, 9321, 9322, 9323, 9324, 9657, 9658, 9659, 9660, 9661, 9662, 9663, 9664, 9665, 9666, 9667, 9668, 9669, 9670, 9671, 9672, 9673, 9674, 9675, 9676, 9677, 9678, 9679, 9680, 9681, 9682, 9683, 9684, 9685, 9686, 9687, 9688, 9689, 9690, 9691, 9692, 9693, 9694, 9695, 9696, 9697, 9698, 9699, 9700, 9701, 9702, 9703, 9704, 9705, 9706, 9707, 9708, 9709, 9710, 9711, 9712, 9713, 9714, 9715, 9716, 9717, 9718, 9719, 9720, 9721, 9722, 10567, 10568, 10569, 10570, 10571, 10572, 10573, 10574, 10575, 10576, 10577, 10578, 10579, 10580, 10581, 10582, 10583, 10584, 10585, 10586, 10587, 10588, 10589, 10590, 10591, 10592, 10593, 10594, 10595, 10596, 10597, 10598, 10599, 10600, 10601, 10602, 10603, 10604, 10605, 10606, 10607, 10608, 10609, 10610, 10611, 10612, 10613, 10614, 10615, 10616, 10617, 10618, 10619, 10620, 10621, 10622, 10623, 10624, 10625, 10626, 10627, 10628, 10629, 10630, 10631, 10632, 10633, 10634, 10635, 10636, 10637, 10638, 10639, 10640, 10641, 10642, 10643, 10644, 10645, 10646, 10647, 10648, 10649, 11800, 11801, 11802, 11803, 11804, 11805, 11806, 11807, 11808, 11809, 11810, 11811, 11812, 11813, 11814, 11815, 11816, 11817, 11818, 11819, 11820, 11821, 11822, 11823, 11824, 11825, 11826, 11827, 11828, 11829, 11830, 11831, 11832, 11833, 11834, 11835, 11836, 11837, 11838, 11839, 11840, 11841, 11842, 11843, 11844, 11845, 11846, 11847, 11848, 11849, 11850, 11851, 11852, 11853, 11854, 11855, 11856, 11857, 11858, 11859, 11860, 11861, 11862, 11863, 11864, 11865, 11866, 11867, 11868, 11869, 11870, 11871, 11872, 11873, 11874, 11875, 11876, 11877, 11878, 11879, 11880, 11881, 11882, 11883, 11884, 14105
        //        };
        //        List<string> ingredientIdsToString = ingredientIds.Select(x => x.ToString()).ToList();
        //        await ingredientService.DeleteIngredients(ingredientIdsToString);
        //        return new OkResult();
        //    }
        //    catch (System.Exception ex)
        //    {
        //        ObjectResult result = new ObjectResult(ex.Message);
        //        result.StatusCode = 500;
        //        return result;
        //    }
        //}

        [Function("Ingredients")]
        public async Task<IActionResult> GetAllIngredients([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                return new OkObjectResult(await ingredientService.GetAllIngredients());
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("IngredientByName")]
        public async Task<IActionResult> GetIngredient([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "IngredientByName/{name}")] HttpRequest req, ILogger log, string name)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                return new OkObjectResult(await ingredientService.GetIngredient(name));
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("IngredientNames")]
        public async Task<IActionResult> GetIngredientNames([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                return new OkObjectResult(await ingredientService.GetIngredientNames());
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("IngredientShoppingCategories")]
        public async Task<IActionResult> GetIngrShoppingCategories([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                List<Recipe> recipes = JsonConvert.DeserializeObject<List<Recipe>>(bodyJson);
                return new OkObjectResult(await ingredientService.GetShoppingCategoriesForRecipes(recipes));
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("InsertIngredient")]
        public async Task<IActionResult> InsertIngredient([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                Ingredient newIngredient = JsonConvert.DeserializeObject<Ingredient>(bodyJson);
                string nextId = await ingredientService.GetNextAvailableId();
                newIngredient.Pk = nextId;
                newIngredient.Id = nextId;
                await ingredientService.InsertIngredient(newIngredient);

                return new CreatedResult(string.Empty, newIngredient);
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("UpdateIngredient")]
        public async Task<IActionResult> UpdateIngredient([HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                await ingredientService.UpdateIngredient(JsonConvert.DeserializeObject<Ingredient>(bodyJson));

                return new OkResult();
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }
    }
}