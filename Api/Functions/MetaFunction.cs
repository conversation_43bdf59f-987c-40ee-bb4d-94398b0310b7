using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Api.Services;
using TwentyDishes.Shared.Entities;
using TwentyDishes.Api.Classes;

namespace TwentyDishes.Api.Functions
{
    public class MetaFunction
    {
        private readonly IAuthService authService;
        private readonly IMetaService metaService;

        public MetaFunction(IAuthService authService, IMetaService metaService)
        {
            this.authService = authService;
            this.metaService = metaService;
        }

        [Function("BrandByName")]
        public async Task<IActionResult> GetBrandByName([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                string baseUrl = JsonConvert.DeserializeObject<string>(bodyJson);

                if (baseUrl.Contains("localhost"))
                {
                    return new OkObjectResult(await metaService.GetBrand(1));
                }
                else
                {
                    return new OkObjectResult(await metaService.GetBrand(baseUrl));
                }
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("BrandById")]
        public async Task<IActionResult> GetBrandById([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "BrandById/{id}")] HttpRequest req, ILogger log, int id)
        {
            try
            {
                return new OkObjectResult(await metaService.GetBrand(id));
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("Brands")]
        public async Task<IActionResult> GetBrands([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                return new OkObjectResult(await metaService.GetBrands());
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("DailyNutritionValues")]
        public async Task<IActionResult> GetDailyNutritionValues([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                return new OkObjectResult(await metaService.GetDailyNutritionValues());
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("Diets")]
        public async Task<IActionResult> GetDiets([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                return new OkObjectResult(await metaService.GetDiets());
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("FoodCategories")]
        public async Task<IActionResult> GetFoodCategories([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                return new ObjectResult(await metaService.GetFoodCategories());
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("ShoppingCategories")]
        public async Task<IActionResult> GetShoppingCategories([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                return new ObjectResult(await metaService.GetShoppingCategories());
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("OpenAsync")]
        public async Task<IActionResult> OpenAsync([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                await metaService.OpenAsync();
                return new OkResult();
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("UpdateBrands")]
        public async Task<IActionResult> UpdateBrands([HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);
                List<Brand> brands;

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                brands = await req.ReadFromJsonAsync<List<Brand>>();
                await metaService.UpdateBrands(brands);

                return new OkResult();
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }
    }
}