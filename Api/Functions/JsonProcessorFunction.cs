using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System;
using TwentyDishes.Api.Classes;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;
using Newtonsoft.Json.Serialization;

namespace TwentyDishes.Api.Functions
{
    public class JsonProcessorFunction
    {
        private readonly IAuthService authService;
        private readonly IMetaService metaService;
        private readonly IRecipeService recipeService;

        public JsonProcessorFunction(IAuthService authService, IMetaService metaService, IRecipeService recipeService)
        {
            this.authService = authService;
            this.metaService = metaService;
            this.recipeService = recipeService;
        }

        [Function("JsonIngredientProcessor")]
        public async Task<IActionResult> ProcessIngredientJson([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                List<JsonImportType.IngredientGroup> ingredientGroups = JsonProcessor.GetJsonExport<List<JsonImportType.IngredientGroup>>(JsonConvert.DeserializeObject<string>(bodyJson));

                //Meta original = await metaService.GetIngredientMeta();
                //Meta modified = new Meta()
                //{
                //    Id = original.Id,
                //    Pk = original.Pk,
                //    Values = original.Values
                //};

                //foreach (JsonImportType.IngredientGroup ig in ingredientGroups)
                //{
                //    int idx = modified.Values.FindIndex(x => x.SelectToken("name").Value<string>().Contains(ig.IngredientName, StringComparison.InvariantCultureIgnoreCase));

                //    if (idx > -1)
                //    {
                //        modified.Values[idx]["foodGroup"] = ig.FoodGroup;
                //    }
                //}

                //string json = JsonConvert.SerializeObject(modified, Formatting.Indented);

                return new OkResult();
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("JsonRecipeProcessor")]
        public async Task<IActionResult> ProcessRecipeJson([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                List<JsonImportType.WPRMRecipe> wprmJson = JsonProcessor.GetJsonExport<List<JsonImportType.WPRMRecipe>>(JsonConvert.DeserializeObject<string>(bodyJson));
                Dictionary<string, string> newTags = new Dictionary<string, string>();
                int duplicateCount = 0;

                foreach (JsonImportType.WPRMRecipe recipe in wprmJson)
                {
                    int startIndex = recipe.post_content.IndexOf("WPRM Recipe ");
                    int stopIndex = recipe.post_content.Replace(" -->", string.Empty).IndexOf("-->");
                    string recipeId = recipe.post_content.Replace(" -->", string.Empty).Substring(startIndex, stopIndex - startIndex).Replace("WPRM Recipe ", string.Empty).Replace(" Recipe ", string.Empty).Trim();

                    if (!newTags.ContainsKey(recipeId))
                    {
                        newTags.Add(recipeId, "");
                    }
                    else
                    {
                        duplicateCount += 1;
                    }
                }

                List<Recipe> originalRecipes = await recipeService.GetRecipes(newTags.Select(x => x.Key).ToList());

                int missingRecipes = newTags.Where(x => !originalRecipes.Where(y => y.Pk == x.Key).Any()).Count();

                if (missingRecipes > 0)
                {
                    List<string> missingRecipeIds = newTags.Where(x => !originalRecipes.Where(y => y.Pk == x.Key).Any()).Select(x => x.Key).ToList();
                }

                //foreach (Recipe recipe in originalRecipes)
                //{
                //    if (newTags.ContainsKey(recipe.Pk))
                //    {
                //        if (recipe.Tags.FoodCategories == null)
                //        {
                //            List<string> foodCategories = new List<string>()
                //            {
                //                newTags[recipe.Pk]
                //            };
                //            recipe.Tags.FoodCategories = foodCategories;
                //        }
                //        else
                //        {
                //            recipe.Tags.FoodCategories.Add(newTags[recipe.Pk]);
                //        }
                //        await recipeService.UpdateRecipe(recipe);
                //    }
                //}

                //List<Recipe> updatedRecipes = await recipeService.GetRecipes(newTags.Select(x => x.Key).ToList());
                //updatedRecipes = updatedRecipes.Where(x => x.Tags.FoodCategories.Contains(newTags.Values.First())).ToList();

                return new OkResult();
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("StepProcessor")]
        public async Task<IActionResult> ProcessSteps([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                List<JsonImportType.WPRMStepByStep> wprmJson = JsonProcessor.GetJsonExport<List<JsonImportType.WPRMStepByStep>>(JsonConvert.DeserializeObject<string>(bodyJson));
                string currentRecipeId = string.Empty;
                List<JsonImportType.RecipeStep> result = new List<JsonImportType.RecipeStep>();
                List<JsonImportType.StepByStep> steps = new List<JsonImportType.StepByStep>();

                foreach (JsonImportType.WPRMStepByStep wStep in wprmJson)
                {
                    if (currentRecipeId != wStep.RecipeId)
                    {
                        JsonImportType.RecipeStep recipeStep = new JsonImportType.RecipeStep()
                        {
                            RecipeId = currentRecipeId,
                            Steps = steps
                        };
                        result.Add(recipeStep);

                        currentRecipeId = wStep.RecipeId;
                        steps = new List<JsonImportType.StepByStep>();
                    }

                    JsonImportType.StepByStep step = new JsonImportType.StepByStep()
                    {
                        Description = wStep.StepText,
                        Priority = wStep.stepPriority
                    };
                    steps.Add(step);
                }

                string resultJson = JsonConvert.SerializeObject(result, new JsonSerializerSettings { ContractResolver = new CamelCasePropertyNamesContractResolver() });

                return new OkObjectResult(resultJson);
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }
    }
}