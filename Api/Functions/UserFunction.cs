using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.OpenApi.Models;
using System.Net;using static TwentyDishes.Shared.Classes.SubscriptionHelper;
using System;
using System.Web;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using TwentyDishes.Api.Classes;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Api.Services;
using TwentyDishes.Shared.Classes;
using TwentyDishes.Shared.Entities;
using System.Text.RegularExpressions;
using TwentyDishes.Shared.Enums;
using System.Security.Claims;

namespace TwentyDishes.Api.Functions
{
    public class UserFunction
    {
        private readonly IAuthService authService;
        private readonly ICloudflareService cloudflareService;
        private readonly IMetaService metaService;
        private readonly IUserService userService;
        private readonly IPaddleService paddleService;
        private readonly IRecipeService recipeService;

        public UserFunction(IAuthService authService, ICloudflareService cloudflareService, IMetaService metaService,
                            IUserService userService, IPaddleService paddleService, IRecipeService recipeService)
        {
            this.authService = authService;
            this.cloudflareService = cloudflareService;
            this.metaService = metaService;
            this.userService = userService;
            this.paddleService = paddleService;
            this.recipeService = recipeService;
        }

        [OpenApiOperation(operationId: "CreateUser", tags: new[] { "Users" }, Summary = "Create a new user", Description = "Creates a new user account in the system.")]
        [OpenApiSecurity("Bearer", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody(contentType: "application/json", bodyType: typeof(User), Required = true, Description = "User object to create")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.Created, contentType: "application/json", bodyType: typeof(User), Summary = "User created successfully", Description = "Returns the created user object")]
        [OpenApiResponseWithoutBody(statusCode: HttpStatusCode.Unauthorized, Summary = "Unauthorized", Description = "Authentication failed or user ID mismatch")]
        [OpenApiResponseWithoutBody(statusCode: HttpStatusCode.InternalServerError, Summary = "Internal Server Error", Description = "An error occurred while creating the user")]        [Function("CreateUser")]
        public async Task<IActionResult> CreateUser([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);
                User user;

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                user = await req.ReadFromJsonAsync<User>();

                // the requester can only make a user for their id
                if (user.Id != authResult.AppUserId) return new UnauthorizedResult();

                await userService.CreateUser(user);

                return new CreatedResult(user.Pk, user);
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        // TODO: rename and refactor this or remove it
        [Function("CreateUserSubscription")]
        public async Task<IActionResult> CreateUserSubscription([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);
                UserSubscription userSubscription;
                User user;

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                userSubscription = await req.ReadFromJsonAsync<UserSubscription>();

                // the user referenced in the request must match the authenticated user
                if (userSubscription.UserId != authResult.AppUserId) return new UnauthorizedResult();

                user = await userService.GetUser(userSubscription.UserId);

                //Create new subscriptions
                if (userSubscription.SubscriptionType == SubscriptionHelper.SubscriptionType.Free)
                {
                    await paddleService.CancelPreviousSubscriptions(user.EmailAddress, null);

                    // user.SubscriptionId = null;
                    // user.SubscriptionProvider = SubscriptionHelper.PaymentSystem.Free.ToString();

                    // await userService.UpdateUser(user);

                    return new OkResult();
                }
                else if (userSubscription.PaymentSystem == SubscriptionHelper.PaymentSystem.Paddle)
                {
                    // the subscription won't be created in paddle here, it will be checked to already exist in Paddle

                    string paddleSubId = null;

                    try
                    {
                        paddleSubId = await paddleService.GetSubscriptionId(user.EmailAddress);

                        await paddleService.CancelPreviousSubscriptions(user.EmailAddress, paddleSubId);
                    }
                    catch (Exception error)
                    {
                        ObjectResult result = new ObjectResult(error.Message);
                        result.StatusCode = 500;
                        return result;
                    }

                    // user.SubscriptionId = paddleSubId;
                    // user.SubscriptionProvider = userSubscription.PaymentSystem.ToString();

                    // await userService.UpdateUser(user);

                    return new OkResult();
                }
                else
                {
                    return new BadRequestObjectResult("Invalid payment system: " + userSubscription.PaymentSystem.ToString());
                }
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("ModifyUserSubscription")]
        public async Task<IActionResult> ModifyUserSubscription([HttpTrigger(AuthorizationLevel.Anonymous, "patch", Route = "ModifyUserSubscription/{userId}/{subscriptionId}")] HttpRequest req, ILogger log, string userId, string subscriptionId)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                var request = await req.ReadFromJsonAsync<ModifySubscriptionRequest>();

                var user = await userService.GetUser(userId);

                var paddleSubscriptionId = await paddleService.GetSubscriptionId(user.EmailAddress);

                // this request should only work if the user making the request owns the subscription id in the request
                if (paddleSubscriptionId == subscriptionId)
                {
                    await paddleService.ModifyTransaction(user.EmailAddress, request.SubscripionType);

                    return new OkResult();
                }
                else
                {
                    return new UnauthorizedResult();
                }
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("GetUserProfileImage")]
        public async Task<IActionResult> GetUserProfileImage([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "GetUserProfileImage/{profileImageId}")] HttpRequest req, ILogger log, string profileImageId)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                byte[] fileBytes = await cloudflareService.GetUserImage(profileImageId);

                using (Stream ms = new MemoryStream(fileBytes))
                {
                    return new FileStreamResult(ms, "image/png");
                }
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [OpenApiOperation(operationId: "GetUser", tags: new[] { "Users" }, Summary = "Get user by ID", Description = "Retrieves a user by their unique identifier.")]
        [OpenApiSecurity("Bearer", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiParameter(name: "userId", In = ParameterLocation.Path, Required = true, Type = typeof(string), Summary = "User ID", Description = "The unique identifier of the user")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(User), Summary = "User retrieved successfully", Description = "Returns the user object")]
        [OpenApiResponseWithoutBody(statusCode: HttpStatusCode.Unauthorized, Summary = "Unauthorized", Description = "Authentication failed or user ID mismatch")]
        [OpenApiResponseWithoutBody(statusCode: HttpStatusCode.InternalServerError, Summary = "Internal Server Error", Description = "An error occurred while retrieving the user")]        [Function("User")]
        public async Task<IActionResult> GetUser([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "User/{userId}")] HttpRequest req, ILogger log, string userId)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                // the user referenced in the request must match the authenticated user
                if (authResult.AppUserId != userId) return new UnauthorizedResult();

                return new OkObjectResult(await userService.GetUser(userId));
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("UserSubscriptionDetails")]
        public async Task<IActionResult> GetUserSubscriptionDetails([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "UserSubscriptionDetails/{userId}/{paymentSystemString}")] HttpRequest req,
                                                                    ILogger log, string userId)
        {
            try
            {
                var user = await userService.GetUser(userId);

                // payment system
                SubscriptionHelper.PaymentSystem paymentSystem = (SubscriptionHelper.PaymentSystem)Enum.Parse(typeof(SubscriptionHelper.PaymentSystem), user.SubscriptionProvider);

                // the response object
                UserSubscriptionDetail subscriptionDetail = new();

                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                // the user referenced in the request must match the authenticated user
                if (authResult.AppUserId != userId) return new UnauthorizedResult();

                if (paymentSystem != SubscriptionHelper.PaymentSystem.Free)
                {
                    if (paymentSystem == SubscriptionHelper.PaymentSystem.Lifetime)
                    {
                        subscriptionDetail.Subscription = new UserSubscription()
                        {
                            PaymentSystem = SubscriptionHelper.PaymentSystem.Lifetime
                        };
                    }
                    else if (paymentSystem == SubscriptionHelper.PaymentSystem.Paddle)
                    {
                        var paddleSubscription = await paddleService.GetSubscription(user.SubscriptionId);
                        var paddleTransactions = await paddleService.GetTransactions(user.SubscriptionId);

                        var latestTransaction = paddleTransactions is null || paddleTransactions.Count == 0 ? null : paddleTransactions[0];
                        float subscriptionPrice = latestTransaction.Details?.Totals?.Subtotal is null ? 0 : float.Parse(latestTransaction.Details.Totals.Subtotal);

                        subscriptionDetail.Subscription = new UserSubscription()
                        {
                            PaymentSystem = PaymentSystem.Paddle,
                            UserId = userId,
                            SubscriptionType = paddleSubscription.GetSubscriptionType(),
                            SubscriptionPrice = subscriptionPrice / 100,
                        };
                        subscriptionDetail.Invoices = paddleTransactions.Select(pt => new UserInvoice()
                        {
                            Paid = pt.Status == "completed",
                            Status = pt.Status,
                            AmountDue = decimal.Parse(pt.Details.Totals.Balance) / 100,
                            AmountPaid = (decimal.Parse(pt.Details.Totals.Total) - decimal.Parse(pt.Details.Totals.Balance)) / 100,
                            AmountRemaining = decimal.Parse(pt.Details.Totals.Balance) / 100,
                            // BillingReason = sInv.BillingReason,
                            // CollectionMethod = sInv.CollectionMethod,
                            Created = DateTime.Parse(pt.Created_At),
                            InvoiceId = pt.Id,
                            // PeriodEnd = sInv.PeriodEnd,
                            // PeriodStart = sInv.PeriodStart,
                            // ReceiptNumber = sInv.ReceiptNumber,
                        }).ToList();
                        subscriptionDetail.Canceled = paddleSubscription.Scheduled_Change is not null;
                        // TODO: combine the status field and the canceled field, they are doing similar things
                        subscriptionDetail.Status = paddleSubscription.Status == "active" || paddleSubscription.Status == "trialing" ? SubscriptionStatus.Active : SubscriptionStatus.Inactive;
                        subscriptionDetail.EndDate = paddleSubscription.Scheduled_Change?.Effective_At is not null ? DateTime.Parse(paddleSubscription.Scheduled_Change.Effective_At) : null;
                        subscriptionDetail.SubscriptionId = paddleSubscription.Id;
                    }
                    else
                    {
                        return new BadRequestObjectResult("Invalid subscription type: " + paymentSystem.ToString());
                    }
                }
                else
                {
                    subscriptionDetail.Subscription = new UserSubscription()
                    {
                        PaymentSystem = paymentSystem
                    };
                }

                return new OkObjectResult(subscriptionDetail);
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [OpenApiOperation(operationId: "UpdateUser", tags: new[] { "Users" }, Summary = "Update user", Description = "Updates an existing user account.")]
        [OpenApiSecurity("Bearer", SecuritySchemeType.Http, Scheme = OpenApiSecuritySchemeType.Bearer, BearerFormat = "JWT")]
        [OpenApiRequestBody(contentType: "application/json", bodyType: typeof(User), Required = true, Description = "Updated user object")]
        [OpenApiParameter(name: "generateUserWeek", In = ParameterLocation.Query, Required = false, Type = typeof(bool), Summary = "Generate user week", Description = "Whether to generate weekly meal plans for the user")]
        [OpenApiResponseWithoutBody(statusCode: HttpStatusCode.OK, Summary = "User updated successfully", Description = "User has been updated successfully")]
        [OpenApiResponseWithoutBody(statusCode: HttpStatusCode.Unauthorized, Summary = "Unauthorized", Description = "Authentication failed or user ID mismatch")]
        [OpenApiResponseWithoutBody(statusCode: HttpStatusCode.InternalServerError, Summary = "Internal Server Error", Description = "An error occurred while updating the user")]        [Function("UpdateUser")]
        public async Task<IActionResult> UpdateUser([HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);
                User user;

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                user = await req.ReadFromJsonAsync<User>();

                // the user referenced in the request must match the authenticated user
                if (authResult.AppUserId != user.Id) return new UnauthorizedResult();

                var urlQuery = req.Query;
                bool generateUserWeek = urlQuery is not null && !String.IsNullOrEmpty(urlQuery["generateUserWeek"].FirstOrDefault()) ? Boolean.Parse(urlQuery["generateUserWeek"].First()) : false;

                // if the user is being updated from not setup status to setup then their meal schedule should be populated
                if (generateUserWeek)
                {
                    var newUserWeeks = await recipeService.GenerateUserWeekRecipes(user, 52, DateTimeHelper.GetFirstDateInWeek().AddDays(-14));

                    user.UserWeeks = newUserWeeks;
                }

                // var existingUser = await userService.GetUser(user.Id);

                // this http request cannot change the user's subscription to lifetime, that can only be done manually by an admin.
                // the subscription fields in the database are currently only used for this lifetime flag.
                // all other subscription categorization is done through paddle.
                // if (existingUser.SubscriptionProvider != SubscriptionHelper.PaymentSystem.Lifetime.ToString() && user.SubscriptionProvider == SubscriptionHelper.PaymentSystem.Lifetime.ToString())
                // {
                //     user.SubscriptionProvider = null;
                // }

                await userService.UpdateUser(user);

                return new OkResult();
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("UpdateUserProfileImage")]
        public async Task<IActionResult> UpdateUserProfileImage([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "UpdateUserProfileImage/{userId}")] HttpRequest req, ILogger log, string userId)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);
                byte[] newImage;

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                // the user referenced in the request must match the authenticated user
                if (authResult.AppUserId != userId) return new UnauthorizedResult();

                User user = await userService.GetUser(userId);

                newImage = await req.ReadFromJsonAsync<byte[]>();

                if (!string.IsNullOrWhiteSpace(user.ProfileImageId))
                {
                    user.ProfileImageId = await cloudflareService.UpdateUserImage(user.ProfileImageId, newImage);
                }
                else
                {
                    user.ProfileImageId = await cloudflareService.InsertUserImage(newImage);
                }

                await userService.UpdateUser(user);

                return new OkObjectResult(user.ProfileImageId);
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("UserExists")]
        public async Task<IActionResult> UserExists([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "UserExists/{userId}")] HttpRequest req, ILogger log, string userId)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                // the user referenced in the request must match the authenticated user
                if (authResult.AppUserId != userId) return new UnauthorizedResult();

                return new OkObjectResult(await userService.UserExists(userId));
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("CreateMenuPdf")]
        public async Task<IActionResult> CreateMenuPdf([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                CreateMenuPdfRequest requestObject = await req.ReadFromJsonAsync<CreateMenuPdfRequest>();

                byte[] pdfData = await userService.CreateMenuPdf(
                    requestObject.UserWeek,
                    requestObject.AdditionalRecipeIds,
                    requestObject.WeekStartDate,
                    requestObject.IncludeRecipes,
                    requestObject.IncludeMealPlan,
                    requestObject.IncludePrepGuide,
                    requestObject.IncludeShoppingList,
                    requestObject?.BrandId ?? 1,
                    requestObject?.AdditionalIngredients,
                    requestObject.Format
                );

                req.HttpContext.Response.Headers["Content-Disposition"] = "attachment; filename=\"test.pdf\"";

                return new FileContentResult(pdfData, "application/pdf");
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("ResendEmailVerificationLink")]
        public async Task<IActionResult> ResendEmailVerificationLink([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                await authService.ResendEmailVerificationLink(authResult?.Auth0UserId);

                return new OkResult();
            }
            catch (System.Exception ex)
            {
                ObjectResult result = new ObjectResult($"{ex.Message}, {req.Headers.Authorization[0]}");
                result.StatusCode = 500;
                return result;
            }
        }
    }
}