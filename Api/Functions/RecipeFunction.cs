using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Entities;
using TwentyDishes.Api.Classes;
using Microsoft.Azure.Functions.Worker;

namespace TwentyDishes.Api.Functions
{
    public class RecipeFunction
    {
        private readonly IAuthService authService;
        private readonly IRecipeService recipeService;

        public RecipeFunction(IAuthService authService, IRecipeService recipeService)
        {
            this.authService = authService;
            this.recipeService = recipeService;
        }

        [Function("PartialRecipes")]
        public async Task<IActionResult> GetPartialRecipes([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                return new OkObjectResult(await recipeService.GetAllPartialRecipes(authResult.AppUserId));
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("PartialRecipesByName")]
        public async Task<IActionResult> GetPartialRecipesByName([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                string searchText = JsonConvert.DeserializeObject<string>(bodyJson);
                List<RecipePartial> result;

                if (!string.IsNullOrWhiteSpace(searchText))
                {
                    result = await recipeService.GetPartialRecipesByName(searchText.ToLower(), authResult.AppUserId);
                }
                else
                {
                    result = await recipeService.GetAllPartialRecipes();
                }

                return new OkObjectResult(result);
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("Recipe")]
        public async Task<IActionResult> GetRecipe([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "Recipe/{recipeId}")] HttpRequest req, ILogger log, string recipeId)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                Recipe recipe = await recipeService.GetRecipe(recipeId, authResult.AppUserId);
                return new OkObjectResult(recipe);
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("Recipes")]
        public async Task<IActionResult> GetRecipes([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                List<Recipe> recipes = await recipeService.GetRecipes(JsonConvert.DeserializeObject<List<string>>(bodyJson), authResult.AppUserId);
                return new OkObjectResult(recipes);
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("RecipesByName")]
        public async Task<IActionResult> GetRecipesByName([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                List<Recipe> recipes = await recipeService.GetRecipesByName(JsonConvert.DeserializeObject<string>(bodyJson), authResult.AppUserId);
                return new OkObjectResult(recipes);
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("WeeklyRecipes")]
        public async Task<IActionResult> GetWeeklyRecipes([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                return new OkObjectResult(await recipeService.GetWeeklyRecipes(JsonConvert.DeserializeObject<UserWeek>(bodyJson), authResult.AppUserId));
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("InsertRecipe")]
        public async Task<IActionResult> InsertRecipe([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                Recipe newRecipe = JsonConvert.DeserializeObject<Recipe>(bodyJson);
                string nextId = await recipeService.GetNextAvailableId();
                newRecipe.Pk = nextId;
                newRecipe.Id = nextId;
                await recipeService.InsertRecipe(newRecipe);

                return new CreatedResult(string.Empty, newRecipe);
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }

        [Function("UpdateRecipe")]
        public async Task<IActionResult> UpdateRecipe([HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = null)] HttpRequest req, ILogger log)
        {
            try
            {
                AuthTokenValidationResult authResult = await authService.GetAuthResult(req.Headers);

                if (!authResult.Success)
                {
                    return new UnauthorizedResult();
                }

                string bodyJson;

                using (StreamReader sr = new StreamReader(req.Body))
                {
                    bodyJson = await sr.ReadToEndAsync();
                }

                await recipeService.UpdateRecipe(JsonConvert.DeserializeObject<Recipe>(bodyJson));

                return new OkResult();
            }
            catch (Exception ex)
            {
                ObjectResult result = new ObjectResult(ex.Message);
                result.StatusCode = 500;
                return result;
            }
        }
    }
}