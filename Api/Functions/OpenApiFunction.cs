using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using System.Net;

namespace TwentyDishes.Api.Functions
{
    public class OpenApiFunction
    {
        [Function("GetApiInfo")]
        [OpenApiOperation(operationId: "GetApiInfo", tags: new[] { "System" }, Summary = "Get API Information", Description = "Returns basic information about the TwentyDishes API.")]
        [OpenApiResponseWithBody(statusCode: HttpStatusCode.OK, contentType: "application/json", bodyType: typeof(ApiInfo), Summary = "API information retrieved successfully", Description = "Returns API version, name, and description")]
        public IActionResult GetApiInfo([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "info")] HttpRequest req, ILogger log)
        {
            var apiInfo = new ApiInfo
            {
                Name = "TwentyDishes API",
                Version = "v1.0.0",
                Description = "A comprehensive meal planning and recipe management API built with Azure Functions.",
                Environment = req.Host.Host.Contains("localhost") ? "Development" : "Production",
                Endpoints = new[]
                {
                    "Users Management",
                    "Recipe Management", 
                    "Ingredient Management",
                    "Menu PDF Generation",
                    "Subscription Management"
                }
            };

            return new OkObjectResult(apiInfo);
        }
    }

    public class ApiInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Environment { get; set; } = string.Empty;
        public string[] Endpoints { get; set; } = new string[0];
    }
}
