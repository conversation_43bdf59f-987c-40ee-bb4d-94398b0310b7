using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using Syncfusion.Drawing;
using Syncfusion.Pdf;
using Syncfusion.Pdf.Graphics;

namespace TwentyDishes.Api.Helpers
{
    public static class MenuPdfHelpers
    {
        public static async Task<Stream> GetImageStreamFromUrl(string url, HttpClient client)
        {
            var request = await client.GetAsync(url);
            var imageContent = await request.Content.ReadAsByteArrayAsync();
            return new MemoryStream(imageContent);
        }

        public static int ScaledWidthByHeight(int height, int width, int newHeight)
        {
            return (width / height) * newHeight;
        }

        public static PdfLayoutResult AddSectionHeader(PdfPage cursorPage, PdfLayoutResult cursorLayout, PdfBitmap logoImage, string sectionName)
        {
            PdfPen blackPen = new PdfPen(PdfBrushes.Black, 1f);
            PdfStringFormat titleFormat = new PdfStringFormat() { Alignment = PdfTextAlignment.Center };
            PdfFont headerFont = new PdfStandardFont(PdfFontFamily.Helvetica, 25);

            cursorPage.Graphics.DrawImage(logoImage, 0, 0, ScaledWidthByHeight(logoImage.Height, logoImage.Width, 60), 60);
            cursorLayout = new PdfLayoutResult(cursorPage, new RectangleF(cursorLayout.Bounds.X, cursorLayout.Bounds.Y, cursorLayout.Bounds.Width, 70));
            PdfLine line = new PdfLine(new PointF(0, 0), new PointF(cursorPage.GetClientSize().Width, 0))
            {
                Pen = blackPen
            };
            PdfTextElement title = new PdfTextElement(sectionName, headerFont, null, null, titleFormat);
            cursorLayout = title.Draw(cursorPage, new RectangleF(0, cursorLayout.Bounds.Bottom + 10, cursorPage.GetClientSize().Width, 50));
            return line.Draw(cursorPage, new PointF(0, cursorLayout.Bounds.Bottom + 5));
        }
    }
}