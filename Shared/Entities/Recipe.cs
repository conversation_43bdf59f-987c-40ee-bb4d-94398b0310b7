using System.Collections.Generic;

namespace TwentyDishes.Shared.Entities
{
    public class Recipe
    {
        public string AuthorLink { get; set; }
        public string AuthorName { get; set; }
        public string CloudflareImageId { get; set; }
        public string CookTime { get; set; }
        public List<string> Courses { get; set; }
        public List<string> Cuisines { get; set; }
        public string CustomTime { get; set; }
        public List<string> Diets { get; set; }
        public string Id { get; set; }
        public string ImageUrl { get; set; }
        public List<RecipeIngredient> Ingredients { get; set; }
        public List<Instruction> Instructions { get; set; }
        public List<string> Meals { get; set; }
        public string Name { get; set; }
        public string Notes { get; set; }
        public RecipeNutrition Nutrition { get; set; }
        public string ParentId { get; set; }
        public string Pk { get; set; }
        public string PrepTime { get; set; }
        public string Servings { get; set; }
        public string ServingsUnit { get; set; }
        public string Shareable { get; set; }
        public List<Step> Steps { get; set; }
        public string Summary { get; set; }
        public string TotalTime { get; set; }
        public string UserId { get; set; }
        public int Version { get; set; }
        public string VersionState { get; set; }

        public class RecipeIngredient
        {
            private string amount;
            
            public string Amount
            {
                get
                {
                    decimal result;
                    if (decimal.TryParse(amount, out result) && result == 0.33m)
                    {
                        amount = "0.33333333333333";
                    }
                    else if (decimal.TryParse(amount, out result) && result == 0.66m)
                    {
                        amount = "0.66666666666666";
                    }
                    else if (!decimal.TryParse(amount, out result))
                    {
                        amount = "0";
                    }
                    return amount; 
                }

                set { amount = value; } 
            }

            public string IngredientId { get; set; }
            public string Name { get; set; }
            public string Notes { get; set; }
            public string Unit { get; set; }

            public decimal GetAmountAsNumber() {
                decimal result;

                var succeeded = decimal.TryParse(this.amount, out result);

                if (!succeeded) return 0;

                return result;
            }
        }
    }

    public class Instruction
    {
        public string ImageUrl { get; set; }
        public string Text { get; set; }
    }

    public class RecipeNutrition
    {
        private double? calcium;
        private double? cholesterol;
        private double? energy;
        private double? fiber;
        private double? iron;
        private double? monoUnsaturatedFat;
        private double? polyUnsaturatedFat;
        private double? potassium;
        private double? protein;
        private double? saturatedFat;
        private double? sodium;
        private double? totalSugar;
        private double? totalCarbsByDifference;
        private double? totalFat;
        private double? vitaminA;
        private double? vitaminC;

        public double? Calcium
        {
            get { return calcium; }
            set
            {
                if (!value.HasValue)
                {
                    calcium = 0;
                }
                else
                {
                    calcium = value.Value;
                }
            }
        }

        public string CalciumUnit { get; set; }

        public double? Cholesterol
        {
            get { return cholesterol; }
            set
            {
                if (!value.HasValue)
                {
                    cholesterol = 0;
                }
                else
                {
                    cholesterol = value.Value;
                }
            }
        }

        public string CholesterolUnit { get; set; }

        public double? Energy
        {
            get { return energy; }
            set
            {
                if (!value.HasValue)
                {
                    energy = 0;
                }
                else
                {
                    energy = value.Value;
                }
            }
        }

        public string EnergyUnit { get; set; }

        public double? Fiber
        {
            get { return fiber; }
            set
            {
                if (!value.HasValue)
                {
                    fiber = 0;
                }
                else
                {
                    fiber = value.Value;
                }
            }
        }

        public string FiberUnit { get; set; }

        public double? Iron
        {
            get { return iron; }
            set
            {
                if (!value.HasValue)
                {
                    iron = 0;
                }
                else
                {
                    iron = value.Value;
                }
            }
        }

        public string IronUnit { get; set; }

        public double? MonoUnsaturatedFat
        {
            get { return monoUnsaturatedFat; }
            set
            {
                if (!value.HasValue)
                {
                    monoUnsaturatedFat = 0;
                }
                else
                {
                    monoUnsaturatedFat = value.Value;
                }
            }
        }

        public string MonoUnsaturatedFatUnit { get; set; }

        public double? PolyUnsaturatedFat
        {
            get { return polyUnsaturatedFat; }
            set
            {
                if (!value.HasValue)
                {
                    polyUnsaturatedFat = 0;
                }
                else
                {
                    polyUnsaturatedFat = value.Value;
                }
            }
        }

        public string PolyUnsaturatedFatUnit { get; set; }

        public double? Potassium
        {
            get { return potassium; }
            set
            {
                if (!value.HasValue)
                {
                    potassium = 0;
                }
                else
                {
                    potassium = value.Value;
                }
            }
        }

        public string PotassiumUnit { get; set; }

        public double? Protein
        {
            get { return protein; }
            set
            {
                if (!value.HasValue)
                {
                    protein = 0;
                }
                else
                {
                    protein = value.Value;
                }
            }
        }

        public string ProteinUnit { get; set; }

        public double? SaturatedFat
        {
            get { return saturatedFat; }
            set
            {
                if (!value.HasValue)
                {
                    saturatedFat = 0;
                }
                else
                {
                    saturatedFat = value.Value;
                }
            }
        }

        public string SaturatedFatUnit { get; set; }

        public double? Sodium
        {
            get { return sodium; }
            set
            {
                if (!value.HasValue)
                {
                    sodium = 0;
                }
                else
                {
                    sodium = value.Value;
                }
            }
        }

        public string SodiumUnit { get; set; }

        public double? TotalCarbsByDifference
        {
            get { return totalCarbsByDifference; }
            set
            {
                if (!value.HasValue)
                {
                    totalCarbsByDifference = 0;
                }
                else
                {
                    totalCarbsByDifference = value.Value;
                }
            }
        }

        public string TotalCarbsByDifferenceUnit { get; set; }

        public double? TotalFat
        {
            get { return totalFat; }
            set
            {
                if (!value.HasValue)
                {
                    totalFat = 0;
                }
                else
                {
                    totalFat = value.Value;
                }
            }
        }

        public string TotalFatUnit { get; set; }

        public double? TotalSugar
        {
            get { return totalSugar; }
            set
            {
                if (!value.HasValue)
                {
                    totalSugar = 0;
                }
                else
                {
                    totalSugar = value.Value;
                }
            }
        }

        public string TotalSugarUnit { get; set; }

        public double? VitaminA
        {
            get { return vitaminA; }
            set
            {
                if (!value.HasValue)
                {
                    vitaminA = 0;
                }
                else
                {
                    vitaminA = value.Value;
                }
            }
        }

        public string VitaminAUnit { get; set; }

        public double? VitaminC
        {
            get { return vitaminC; }
            set
            {
                if (!value.HasValue)
                {
                    vitaminC = 0;
                }
                else
                {
                    vitaminC = value.Value;
                }
            }
        }

        public string VitaminCUnit { get; set; }
    }

    public class Step
    {
        public string Description { get; set; }
        public string Priority { get; set; }
    }
}