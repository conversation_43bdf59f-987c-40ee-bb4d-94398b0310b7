#nullable enable
using System;
using System.Collections.Generic;
using System.Linq;

namespace TwentyDishes.Shared.Entities
{
    public class User
    {
        public required string Pk { get; set; }
        public required string EmailAddress { get; set; }
        public byte FamilySize { get; set; }
        public required string FirstName { get; set; }
        public required List<long> GroupIds { get; set; }
        public required string Id { get; set; }
        public bool IsSetup { get; set; }
        public required string LastName { get; set; }
        public required UserMealSelection MealSelection { get; set; }
        public byte PrintFontSize { get; set; }
        public bool PrintOneRecipePerPage { get; set; }
        public string? ProfileImageId { get; set; }
        public required List<ShoppingListItem> ShoppingListItems { get; set; }
        public string? SubscriptionId { get; set; }
        public string? SubscriptionProvider { get; set; }
        public required List<string> UserDiets { get; set; }
        public required List<RecipePartial> UserFavorites { get; set; }
        public required List<string> UserFoodCategoryExclusions { get; set; }
        public required List<string> UserIngredientExclusions { get; set; }
        public required List<UserMenu> UserMenus { get; set; }
        public required List<UserRecipe> UserRecipes { get; set; }
        public string? UserRole { get; set; }
        public required List<UserWeek> UserWeeks { get; set; }
        public required List<WeeklyRecipeItem> WeeklyRecipePrepItems { get; set; }
        public required List<RecipePrepStorage> WeeklyRecipePrepStorage { get; set; }
        public required List<UserStep> WeeklyRecipeSteps { get; set; }
    }

    public class UserMealSelection
    {
        public bool BreakfastEnabled { get; set; }
        public bool DinnerEnabled { get; set; } = true;
        public bool LunchEnabled { get; set; }
        public bool SnacksEnabled { get; set; }
    }

    public class UserMenu
    {
        public bool IsShared { get; set; }
        public DateTime LastUpdated { get; set; }
        public string? MenuName { get; set; }
        public string? Notes { get; set; }
        public UserWeek? UserWeek { get; set; }
    }

    public class UserRecipe
    {
        public string? Notes { get; set; }
        public required string RecipeId { get; set; }
        public DateTime ScheduleDate { get; set; }
        public required List<ShoppingListItem> ShoppingListItems { get; set; }
        public required List<string> ShoppingNotes { get; set; }
        public byte SortOrder { get; set; }
    }

    public class UserStep : Step
    {
        public bool IsCompleted { get; set; }
        public DateTime WeekStartDate { get; set; }
    }

    public class UserWeek
    {
        public required List<string> FridayBreakfast { get; set; }
        public required List<string> FridayDinner { get; set; }
        public required List<string> FridayLunch { get; set; }
        public required List<string> FridaySnacks { get; set; }
        public required List<string> MondayBreakfast { get; set; }
        public required List<string> MondayDinner { get; set; }
        public required List<string> MondayLunch { get; set; }
        public required List<string> MondaySnacks { get; set; }
        public required List<string> SaturdayBreakfast { get; set; }
        public required List<string> SaturdayDinner { get; set; }
        public required List<string> SaturdayLunch { get; set; }
        public required List<string> SaturdaySnacks { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime StopDate { get; set; }
        public required List<string> SundayBreakfast { get; set; }
        public required List<string> SundayDinner { get; set; }
        public required List<string> SundayLunch { get; set; }
        public required List<string> SundaySnacks { get; set; }
        public required List<string> ThursdayBreakfast { get; set; }
        public required List<string> ThursdayDinner { get; set; }
        public required List<string> ThursdayLunch { get; set; }
        public required List<string> ThursdaySnacks { get; set; }
        public required List<string> TuesdayBreakfast { get; set; }
        public required List<string> TuesdayDinner { get; set; }
        public required List<string> TuesdayLunch { get; set; }
        public required List<string> TuesdaySnacks { get; set; }
        public required List<string> WednesdayBreakfast { get; set; }
        public required List<string> WednesdayDinner { get; set; }
        public required List<string> WednesdayLunch { get; set; }
        public required List<string> WednesdaySnacks { get; set; }

        public List<List<string>> ToRecipeIdsByDay()
        {
            return new List<List<string>> {
                this.FridayBreakfast.Concat(this.FridayLunch).Concat(this.FridayDinner).Concat(this.FridaySnacks).ToList(),
                this.SaturdayBreakfast.Concat(this.SaturdayLunch).Concat(this.SaturdayDinner).Concat(this.SaturdaySnacks).ToList(),
                this.SundayBreakfast.Concat(this.SundayLunch).Concat(this.SundayDinner).Concat(this.SundaySnacks).ToList(),
                this.MondayBreakfast.Concat(this.MondayLunch).Concat(this.MondayDinner).Concat(this.MondaySnacks).ToList(),
                this.TuesdayBreakfast.Concat(this.TuesdayLunch).Concat(this.TuesdayDinner).Concat(this.TuesdaySnacks).ToList(),
                this.WednesdayBreakfast.Concat(this.WednesdayLunch).Concat(this.WednesdayDinner).Concat(this.WednesdaySnacks).ToList(),
                this.ThursdayBreakfast.Concat(this.ThursdayLunch).Concat(this.ThursdayDinner).Concat(this.ThursdaySnacks).ToList(),
            };
        }

        public List<string> ToRecipeIds()
        {
            var result = new List<string>();

            foreach (List<string> ids in this.ToRecipeIdsByDay())
            {
                result.Concat(ids);
            }

            return result;
        }

        public UserWeek ToExcluded(List<string> excludeIds)
        {
            return new UserWeek()
            {
                FridayBreakfast = this.FridayBreakfast.Where(fb => !excludeIds.Contains(fb)).ToList(),
                FridayLunch = this.FridayLunch.Where(fb => !excludeIds.Contains(fb)).ToList(),
                FridayDinner = this.FridayDinner.Where(fb => !excludeIds.Contains(fb)).ToList(),
                FridaySnacks = this.FridaySnacks.Where(fb => !excludeIds.Contains(fb)).ToList(),

                SaturdayBreakfast = this.SaturdayBreakfast.Where(fb => !excludeIds.Contains(fb)).ToList(),
                SaturdayLunch = this.SaturdayLunch.Where(fb => !excludeIds.Contains(fb)).ToList(),
                SaturdayDinner = this.SaturdayDinner.Where(fb => !excludeIds.Contains(fb)).ToList(),
                SaturdaySnacks = this.SaturdaySnacks.Where(fb => !excludeIds.Contains(fb)).ToList(),

                SundayBreakfast = this.SundayBreakfast.Where(fb => !excludeIds.Contains(fb)).ToList(),
                SundayLunch = this.SundayLunch.Where(fb => !excludeIds.Contains(fb)).ToList(),
                SundayDinner = this.SundayDinner.Where(fb => !excludeIds.Contains(fb)).ToList(),
                SundaySnacks = this.SundaySnacks.Where(fb => !excludeIds.Contains(fb)).ToList(),

                MondayBreakfast = this.MondayBreakfast.Where(fb => !excludeIds.Contains(fb)).ToList(),
                MondayLunch = this.MondayLunch.Where(fb => !excludeIds.Contains(fb)).ToList(),
                MondayDinner = this.MondayDinner.Where(fb => !excludeIds.Contains(fb)).ToList(),
                MondaySnacks = this.MondaySnacks.Where(fb => !excludeIds.Contains(fb)).ToList(),

                TuesdayBreakfast = this.TuesdayBreakfast.Where(fb => !excludeIds.Contains(fb)).ToList(),
                TuesdayLunch = this.TuesdayLunch.Where(fb => !excludeIds.Contains(fb)).ToList(),
                TuesdayDinner = this.TuesdayDinner.Where(fb => !excludeIds.Contains(fb)).ToList(),
                TuesdaySnacks = this.TuesdaySnacks.Where(fb => !excludeIds.Contains(fb)).ToList(),

                WednesdayBreakfast = this.WednesdayBreakfast.Where(fb => !excludeIds.Contains(fb)).ToList(),
                WednesdayLunch = this.WednesdayLunch.Where(fb => !excludeIds.Contains(fb)).ToList(),
                WednesdayDinner = this.WednesdayDinner.Where(fb => !excludeIds.Contains(fb)).ToList(),
                WednesdaySnacks = this.WednesdaySnacks.Where(fb => !excludeIds.Contains(fb)).ToList(),

                ThursdayBreakfast = this.ThursdayBreakfast.Where(fb => !excludeIds.Contains(fb)).ToList(),
                ThursdayLunch = this.ThursdayLunch.Where(fb => !excludeIds.Contains(fb)).ToList(),
                ThursdayDinner = this.ThursdayDinner.Where(fb => !excludeIds.Contains(fb)).ToList(),
                ThursdaySnacks = this.ThursdaySnacks.Where(fb => !excludeIds.Contains(fb)).ToList(),
            };
        }
    }

    public class RecipePrepStorage
    {
        public bool IsCompleted { get; set; }
        public required string RecipeName { get; set; }
        public DateTime ScheduleDate { get; set; }
    }

    public class ShoppingListItem
    {
        public required string Amount { get; set; }
        public required string AmountUnit { get; set; }
        public required string IngredientName { get; set; }
        public bool IsCompleted { get; set; }
        public bool IsCustom { get; set; }
        public DateTime ScheduleDate { get; set; }
    }

    public class WeeklyRecipeItem
    {
        public required string Amount { get; set; }
        public required string AmountUnit { get; set; }
        public required string IngredientName { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime ScheduleDate { get; set; }
    }
}
