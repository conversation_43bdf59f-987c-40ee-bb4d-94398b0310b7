#nullable enable
namespace TwentyDishes.Shared.Entities
{
    public class Ingredient
    {
        public required string FoodCategory { get; set; }
        public required string Id { get; set; }
        public required string Name { get; set; }
        public required IngredientNutrition Nutrition { get; set; }
        public string? ParentId { get; set; }
        public required string Pk { get; set; }
        public required UnitValue Portion { get; set; }
        public string? Shareable { get; set; }
        public required string ShoppingCategory { get; set; }
        public string? UserId { get; set; }
        public string? Version { get; set; }
        public string? VersionState { get; set; }
    }

    public class IngredientNutrition
    {
        public required UnitValue Calcium { get; set; }
        public required UnitValue Cholesterol { get; set; }
        public required UnitValue Energy { get; set; }
        public required UnitValue Fiber { get; set; }
        public required UnitValue Iron { get; set; }
        public required UnitValue MonoUnsaturatedFat { get; set; }
        public required UnitValue PolyUnsaturatedFat { get; set; }
        public required UnitValue Potassium { get; set; }
        public required UnitValue Protein { get; set; }
        public required UnitValue SaturatedFat { get; set; }
        public required UnitValue Sodium { get; set; }
        public required UnitValue TotalCarbsByDifference { get; set; }
        public required UnitValue TotalFat { get; set; }
        public required UnitValue TotalSugar { get; set; }
        public required UnitValue TransFat { get; set; }
        public required UnitValue VitaminA { get; set; }
        public required UnitValue VitaminC { get; set; }
    }

    public class UnitValue
    {
        double? val;

        public required string Unit { get; set; }
        public double? Value
        {
            get { return val; }
            set
            {
                if (!value.HasValue)
                {
                    val = 0;
                }
                else
                {
                    val = value.Value;
                }
            }
        }
    }
}
