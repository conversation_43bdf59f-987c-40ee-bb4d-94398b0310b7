namespace TwentyDishes.Shared.Entities
{
    public class Ingredient
    {
        public string FoodCategory { get; set; }
        public string Id { get; set; }
        public string Name { get; set; }
        public IngredientNutrition Nutrition { get; set; }
        public string ParentId { get; set; }
        public string Pk { get; set; }
        public UnitValue Portion { get; set; }
        public string Shareable { get; set; }
        public string ShoppingCategory { get; set; }
        public string UserId { get; set; }
        public string Version { get; set; }
        public string VersionState { get; set; }
    }

    public class IngredientNutrition
    {
        public UnitValue Calcium { get; set; }
        public UnitValue Cholesterol { get; set; }
        public UnitValue Energy { get; set; }
        public UnitValue Fiber { get; set; }
        public UnitValue Iron { get; set; }
        public UnitValue MonoUnsaturatedFat { get; set; }
        public UnitValue PolyUnsaturatedFat { get; set; }
        public UnitValue Potassium { get; set; }
        public UnitValue Protein { get; set; }
        public UnitValue SaturatedFat { get; set; }
        public UnitValue Sodium { get; set; }
        public UnitValue TotalCarbsByDifference { get; set; }
        public UnitValue TotalFat { get; set; }
        public UnitValue TotalSugar { get; set; }
        public UnitValue TransFat { get; set; }
        public UnitValue VitaminA { get; set; }
        public UnitValue VitaminC { get; set; }
    }

    public class UnitValue
    {
        double? val;

        public string Unit { get; set; }
        public double? Value
        {
            get { return val; }
            set
            {
                if (!value.HasValue)
                {
                    val = 0;
                }
                else
                {
                    val = value.Value;
                }
            }
        }
    }
}