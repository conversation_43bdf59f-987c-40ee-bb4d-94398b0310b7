using System;

namespace TwentyDishes.Shared.Entities
{
    public class UserInvoice
    {
        public decimal AmountDue { get; set; }
        public decimal AmountPaid { get; set; }
        public decimal AmountRemaining { get; set; }
        public string BillingReason { get; set; }
        public string CollectionMethod { get; set; }
        public DateTime Created { get; set; }
        public string InvoiceId { get; set; }
        public bool Paid { get; set; }
        public DateTime PeriodEnd { get; set; }
        public DateTime PeriodStart { get; set; }
        public string ReceiptNumber { get; set; }
        public string Status { get; set; }
    }
}