namespace TwentyDishes.Shared.Entities
{
    public class Brand
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string BaseUrl { get; set; }
        public string PageTitle { get; set; }
        public string LogoCloudflareId { get; set; }
        public string IconCloudflareId { get; set; }
        public string FavIconCloudflareId { get; set; }
        public string PrimaryColor { get; set; }
        public string SecondaryColor { get; set; }
        public string ComplementaryColor1 { get; set; }
        public string ComplementaryColor2 { get; set; }
        public string TextColor { get; set; }
        public string PrimaryFont { get; set; }
        public string SecondaryFont { get; set; }
        public string GoogleAnalyticsProperty { get; set; }
        public string GoogleAnalyticsMeasurementId { get; set; }
    }
}