#nullable enable
namespace TwentyDishes.Shared.Entities
{
    public class Brand
    {
        public int Id { get; set; }
        public required string Name { get; set; }
        public required string BaseUrl { get; set; }
        public required string PageTitle { get; set; }
        public required string LogoCloudflareId { get; set; }
        public required string IconCloudflareId { get; set; }
        public required string FavIconCloudflareId { get; set; }
        public required string PrimaryColor { get; set; }
        public required string SecondaryColor { get; set; }
        public required string ComplementaryColor1 { get; set; }
        public required string ComplementaryColor2 { get; set; }
        public required string TextColor { get; set; }
        public required string PrimaryFont { get; set; }
        public required string SecondaryFont { get; set; }
        public string? GoogleAnalyticsProperty { get; set; }
        public string? GoogleAnalyticsMeasurementId { get; set; }
    }
}
