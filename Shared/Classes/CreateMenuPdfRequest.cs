using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TwentyDishes.Shared.Enums;

namespace TwentyDishes.Shared.Entities
{
    public class CreateMenuPdfRequest
    {
        public UserWeek UserWeek { get; set; }

        public List<string> AdditionalRecipeIds { get; set; }

        public DateTime WeekStartDate { get; set; }

        public MenuPdfFormat? Format { get; set; }

        public bool IncludeMealPlan { get; set; } = true;

        public bool IncludeShoppingList { get; set; } = true;

        public bool IncludeRecipes { get; set; } = true;

        public bool IncludePrepGuide { get; set; } = true;

        public int BrandId { get; set; }

        public List<ShoppingListItem> AdditionalIngredients { get; set; }
    }
}
