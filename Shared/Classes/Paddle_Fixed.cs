#nullable enable
using System;

namespace TwentyDishes.Shared.Classes
{
    public class PaddleCustomerSearchResult
    {
        public required PaddleCustomer[] Data { get; set; }
    }

    public class PaddleCustomer
    {
        public required string Id { get; set; }
    }

    public class PaddleSubscriptionSearchResult
    {
        public required PaddleSubscription[] Data { get; set; }
    }

    public class PaddleSubscription
    {
        public required string Id { get; set; }

        // active, canceled, past_due, paused, trialing
        public required string Status { get; set; }

        public required string Created_At { get; set; }

        public required PaddleBillingCycle Billing_Cycle { get; set; }

        public required PaddleItem[] Items { get; set; }

        public SubscriptionHelper.SubscriptionType GetSubscriptionType()
        {
            if (this.Billing_Cycle.Interval == "month" && this.Billing_Cycle.Frequency == 1) return SubscriptionHelper.SubscriptionType.Monthly;

            if (this.Billing_Cycle.Interval == "year" && this.Billing_Cycle.Frequency == 1) return SubscriptionHelper.SubscriptionType.Annual;

            if (this.Billing_Cycle.Interval == "month" && this.Billing_Cycle.Frequency == 3) return SubscriptionHelper.SubscriptionType.Quarterly;

            return SubscriptionHelper.SubscriptionType.Free;
        }

        public PaddleScheduledChange? Scheduled_Change { get; set; }
    }

    public class PaddleScheduledChange
    {
        public required string Action { get; set; }
        public required string Effective_At { get; set; }
    }

    public class PaddleBillingCycle
    {
        public required string Interval { get; set; }

        public int Frequency { get; set; }
    }

    public class PaddleTransaction
    {
        public required string Id { get; set; }
        public required string Status { get; set; }

        public required PaddleTransactionDetails Details { get; set; }

        public required string Created_At { get; set; }
    }

    public class PaddleTransactionDetails
    {
        public required PaddleTransactionTotals Totals { get; set; }
    }

    public class PaddleTransactionTotals
    {
        public required string Total { get; set; }

        public required string Balance { get; set; }

        public required string Subtotal { get; set; }
    }

    public class PaddleHelpers
    {
        public static bool IsValidSubscription(PaddleSubscription subscription)
        {
            return subscription.Status == "active" || subscription.Status == "trialing";
        }
    }

    public class PaddleItem
    {
        public required PaddlePrice Price { get; set; }
    }

    public class PaddlePrice
    {
        public required PaddleUnitPrice Unit_Price { get; set; }
    }

    public class PaddleUnitPrice
    {
        public required string Amount { get; set; }

        public required string Currency_Code { get; set; }
    }
}
