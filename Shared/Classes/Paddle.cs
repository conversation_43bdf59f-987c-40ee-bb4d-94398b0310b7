#nullable enable
using System;

namespace TwentyDishes.Shared.Classes
{
    public class PaddleCustomerSearchResult
    {
        public PaddleCustomer[] Data { get; set; }
    }

    public class PaddleCustomer
    {
        public string Id { get; set; }
    }

    public class PaddleSubscriptionSearchResult
    {
        public PaddleSubscription[] Data { get; set; }
    }

    public class PaddleSubscription
    {
        public string Id { get; set; }

        // active, canceled, past_due, paused, trialing
        public string Status { get; set; }

        public string Created_At { get; set; }

        public PaddleBillingCycle Billing_Cycle { get; set; }

        public PaddleItem[] Items { get; set; }

        public SubscriptionHelper.SubscriptionType GetSubscriptionType()
        {
            if (this.Billing_Cycle.Interval == "month" && this.Billing_Cycle.Frequency == 1) return SubscriptionHelper.SubscriptionType.Monthly;

            if (this.Billing_Cycle.Interval == "year" && this.Billing_Cycle.Frequency == 1) return SubscriptionHelper.SubscriptionType.Annual;

            if (this.Billing_Cycle.Interval == "month" && this.Billing_Cycle.Frequency == 3) return SubscriptionHelper.SubscriptionType.Quarterly;

            return SubscriptionHelper.SubscriptionType.Free;
        }

        public PaddleScheduledChange? Scheduled_Change {get; set;}
    }

    public class PaddleScheduledChange {
        public string Action {get; set;}
        public string Effective_At {get; set;}
    }

    public class PaddleBillingCycle
    {
        public string Interval { get; set; }

        public int Frequency { get; set; }
    }

    public class PaddleTransaction
    {
        public string Id { get; set; }
        public string Status { get; set; }

        public PaddleTransactionDetails Details { get; set; }

        public string Created_At { get; set; }
    }

    public class PaddleTransactionDetails
    {
        public PaddleTransactionTotals Totals { get; set; }
    }

    public class PaddleTransactionTotals
    {
        public string Total { get; set; }

        public string Balance { get; set; }

        public string Subtotal {get; set;}
    }

    public class PaddleHelpers
    {
        public static bool IsValidSubscription(PaddleSubscription subscription)
        {
            return subscription.Status == "active" || subscription.Status == "trialing";
        }
    }

    public class PaddleItem
    {
        public PaddlePrice Price { get; set; }
    }

    public class PaddlePrice
    {
        public PaddleUnitPrice Unit_Price { get; set; }
    }

    public class PaddleUnitPrice
    {
        public string Amount { get; set; }

        public string Currency_Code { get; set; }
    }
}