using System;
using System.Text;

namespace TwentyDishes.Shared.Classes
{
    public class Fraction
    {
        public int Numerator { get; private set; }
        public int Denominator { get; private set; }

        public Fraction(int numerator, int denominator)
        {
            Numerator = numerator;
            Denominator = denominator;
        }

        public override string ToString()
        {
            int remainder;
            StringBuilder sb = new StringBuilder(10);
            int wholeNum = Math.DivRem(Numerator, Denominator, out remainder);

            if (Numerator == 0)
            {
                return string.Empty;
            }
            else if (wholeNum != 0 && remainder != 0)
            {
                sb.Append(wholeNum.ToString())
                  .Append(" ")
                  .Append(remainder.ToString())
                  .Append("/")
                  .Append(Denominator.ToString());

                return sb.ToString();
            }
            else if (wholeNum != 0)
            {
                return wholeNum.ToString();
            }
            else
            {
                sb.Append(remainder.ToString())
                  .Append("/")
                  .Append(Denominator.ToString());

                return sb.ToString();
            }
        }
    }
}