using System.Collections.Generic;
using System.Linq;
using System.Text;
using TwentyDishes.Shared.Entities;
using System;

namespace TwentyDishes.Shared.Classes
{
    public static class IngredientHelper
    {
        public const string AdditionalShoppingItems = "Additional Items";

        public static string FormatIngredientString(Recipe.RecipeIngredient ri, int stringInitCapacity)
        {
            decimal amount;
            bool validAmount = decimal.TryParse(ri.Amount, out amount);

            if (validAmount)
            {
                return FormatIngredientString(amount, ri.Unit, ri.Name, stringInitCapacity);
            }
            else
            {
                return FormatIngredientString(0.0m, ri.Unit, ri.Name, stringInitCapacity);
            }
        }

        public static string FormatIngredientString(SummedIngredient si, int stringInitCapacity)
        {
            return FormatIngredientString(si.Amount, si.AmountUnit, si.IngredientName, stringInitCapacity);
        }

        private static string FormatIngredientString(decimal amount, string amountUnit, string ingrName, int stringInitCapacity)
        {
            MathHelper mathHelper = new MathHelper();
            StringBuilder sb = new StringBuilder(stringInitCapacity);

            if (amount != 0)
            {
                sb.Append(mathHelper.RealToFraction(amount, 0.01m).ToString())
                  .Append(" ")
                  .Append(amountUnit)
                  .Append(" ")
                  .Append(ingrName);

                return sb.ToString();
            }
            else
            {
                return ingrName;
            }
        }

        public static List<SummedIngredient> SumTotalIngredients(List<Recipe> selectedWeekRecipes)
        {
            List<SummedIngredient> summedIngredients = new List<SummedIngredient>();

            foreach (Recipe recipe in selectedWeekRecipes)
            {
                foreach (Recipe.RecipeIngredient ing in recipe.Ingredients)
                {
                    if (string.IsNullOrWhiteSpace(ing.Unit) && !decimal.TryParse(ing.Amount, out decimal val))
                    {
                        if (!summedIngredients.Where(x => x.IngredientName == ing.Name && string.IsNullOrWhiteSpace(x.AmountUnit)).Any()) //ContainsKey((ing.Name, string.Empty))
                        {
                            SummedIngredient summedIngredient = new SummedIngredient()
                            {
                                Amount = 0,
                                AmountUnit = string.Empty,
                                IngredientName = ing.Name
                            };
                            summedIngredients.Add(summedIngredient);
                        }
                    }
                    else
                    {
                        if (!summedIngredients.Where(x => x.IngredientName == ing.Name && x.AmountUnit == ing.Unit).Any())
                        {
                            SummedIngredient summedIngredient = new SummedIngredient()
                            {
                                Amount = MathHelper.FractionToDecimal(ing.Amount.Trim()),
                                AmountUnit = ing.Unit,
                                IngredientName = ing.Name
                            };
                            summedIngredients.Add(summedIngredient);
                        }
                        else
                        {
                            int siIndex = summedIngredients.IndexOf(summedIngredients.Where(x => x.IngredientName == ing.Name && x.AmountUnit == ing.Unit).First());
                            if (siIndex != -1)
                            {
                                summedIngredients[siIndex].Amount = summedIngredients[siIndex].Amount + MathHelper.FractionToDecimal(ing.Amount.Trim());
                            }
                        }
                    }
                }
            }
            summedIngredients = summedIngredients.OrderBy(x => x.IngredientName).ToList();
            return summedIngredients;
        }

        public static string UnitAmountToString(double amount, string unit)
        {
            MathHelper mathHelper = new MathHelper();

            return $"{mathHelper.RealToFraction((decimal)amount, 0.01m)}{(String.IsNullOrEmpty(unit) ? "" : $" {unit}")}";
        }

        public static string UnitAmountToString(string amountString, string unit)
        {
            decimal amount;
            bool validAmount = decimal.TryParse(amountString, out amount);

            if (!validAmount)
            {
                amount = 0.0m;
            }

            return UnitAmountToString((double)amount, unit);
        }
    }
}