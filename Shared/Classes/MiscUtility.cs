using System.Text;

namespace TwentyDishes.Shared.Classes
{
    public static class MiscUtility
    {
        public const string ImageAccount20Dishes = "B0I0zY-lQ4c4ucmNdibFrQ/";
        public const string ImageUrlBody = "/cdn-cgi/imagedelivery/";
        public const string ImageUrlPrefix = "https://";
        public const string ImageUrlSuffixDesktopModal = "/desktopModal";
        public const string ImageUrlSuffixPublic = "/public";
        public const string ImageUrlSuffixThumbnail = "/thumbnail";
        public const string ImageUrlSuffixThumbnailHiRes = "/thumbnailHires";

        public enum AlertMessageType
        {
            Success,
            Warning,
            Error
        }

        public enum GetHelpType
        {
            ContactUs,
            PrivacyPolicy,
            DisclosureStatement,
            TermsOfUse
        }

        public static string GetCloudflareImageUrl(string baseUrl, string imageId, string suffix)
        {
            StringBuilder sb = new StringBuilder(150);
            sb.Append(ImageUrlPrefix).Append(baseUrl).Append(ImageUrlBody).Append(ImageAccount20Dishes).Append(imageId).Append(suffix);
            return sb.ToString();
        }

        public static string ReplaceHtmlTags(string text)
        {
            if (text is null) return "";

            return text.Replace("<p>", string.Empty).Replace("</p>", string.Empty).Replace("<br>", string.Empty).Replace("</br>", string.Empty).Replace("<strong>", string.Empty).Replace("</strong>", string.Empty);
        }
    }
}