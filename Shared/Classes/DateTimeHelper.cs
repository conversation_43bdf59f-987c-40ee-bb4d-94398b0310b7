using System;
using System.Collections.Generic;
using System.Linq;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Shared.Classes
{
    public class DateTimeHelper
    {
        public static DateTime GetFirstDateInWeek()
        {
            DateTime result = DateTime.Today.ToLocalTime();
            while (result.DayOfWeek != System.Threading.Thread.CurrentThread.CurrentCulture.DateTimeFormat.FirstDayOfWeek)
            {
                result = result.AddDays(-1);
            }
            return result;
        }

        public static int GetUserWeeksRemaining(List<UserWeek> userWeeks)
        {
            if (userWeeks == null || userWeeks.Count < 1)
            {
                return 0;
            }
            else
            {
                return userWeeks.IndexOf(userWeeks.Last()) - userWeeks.IndexOf(userWeeks.Where(x => x.StartDate == GetFirstDateInWeek()).FirstOrDefault());
            }
        }
    }
}